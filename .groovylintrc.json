{"extends": "recommended", "rules": {"ClassJavadoc": {"enabled": false}, "ClosureAsLastMethodParameter": {"enabled": false}, "CompileStatic": {"enabled": false}, "DuplicateListLiteral": {"enabled": false}, "DuplicateMapLiteral": {"enabled": false}, "DuplicateNumberLiteral": {"enabled": false}, "DuplicateStringLiteral": {"enabled": false}, "FactoryMethodName": {"enabled": false}, "GStringAsMapKey": {"enabled": false}, "GStringExpressionWithinString": {"enabled": false}, "ImplicitClosureParameter": {"enabled": false}, "Indentation": {"enabled": false}, "Instanceof": {"enabled": false}, "InvertedIfElse": {"enabled": false}, "JavadocEmptyFirstLine": {"enabled": false}, "JavadocEmptyLastLine": {"enabled": false}, "LineLength": {"enabled": false}, "MethodParameterTypeRequired": {"enabled": false}, "MethodReturnTypeRequired": {"enabled": false}, "MethodSize": {"enabled": false}, "NestedBlockDepth": {"enabled": false}, "NestedForLoop": {"enabled": false}, "NoDef": {"enabled": false}, "ParameterCount": {"enabled": false}, "ParameterName": {"enabled": false}, "ParameterReassignment": {"enabled": false}, "SpaceAfterClosingBrace": {"enabled": false}, "ThrowException": {"enabled": false}, "UnnecessaryCatchBlock": {"enabled": false}, "UnnecessaryGetter": {"enabled": false}, "UnnecessaryObjectReferences": {"enabled": false}, "UnusedMethodParameter": {"enabled": false}, "VariableName": {"enabled": false}, "VariableTypeRequired": {"enabled": false}, "ReturnNullFromCatchBlock": {"enabled": false}, "UnnecessaryCollectCall": {"enabled": false}, "basic.DeadCode": "error"}}