# MTBios Sign and Repack Tool Makefile

BINARY_NAME=mtbios-sign-repack
VERSION?=1.0.0
BUILD_TIME=$(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build flags
LDFLAGS=-ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT) -s -w"
# Static build flags for better compatibility
STATIC_FLAGS=CGO_ENABLED=0

# Default target
.PHONY: all
all: clean deps build

# Download dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Build for current platform (GLIBC compatible)
.PHONY: build
build:
	@echo "Building GLIBC compatible binary for current platform..."
	$(STATIC_FLAGS) $(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) .
	@echo "Checking binary compatibility:"
	@ldd $(BINARY_NAME) || echo "✅ Static binary (GLIBC compatible)"
	@echo "Binary size: $$(du -h $(BINARY_NAME) | cut -f1)"

# Build for all platforms (GLIBC compatible)
.PHONY: build-all
build-all: clean deps
	@echo "Building GLIBC compatible binaries for all platforms..."
	mkdir -p bin/linux bin/windows bin/darwin

	# Linux
	$(STATIC_FLAGS) GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/linux/$(BINARY_NAME) .
	$(STATIC_FLAGS) GOOS=linux GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o bin/linux/$(BINARY_NAME)-arm64 .

	# Windows
	$(STATIC_FLAGS) GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/windows/$(BINARY_NAME).exe .

	# macOS
	$(STATIC_FLAGS) GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/darwin/$(BINARY_NAME) .
	$(STATIC_FLAGS) GOOS=darwin GOARCH=arm64 $(GOBUILD) $(LDFLAGS) -o bin/darwin/$(BINARY_NAME)-arm64 .

	@echo "✅ All binaries built with GLIBC compatibility"
	@echo "Binary sizes:"
	@find bin/ -name "$(BINARY_NAME)*" -exec du -h {} \;

# Deploy binaries to OSS
.PHONY: deploy
deploy: build-all
	@echo "Uploading binaries to OSS..."
	mc cp -r bin/* sh-moss/dependency/mtbios-sign-tool/

	@echo "Upload completed. Cleaning up local binaries..."
	rm -rf bin/

	@echo "Binaries are now available on OSS at: sh-moss/dependency/mtbios-sign-tool/"



# Test
.PHONY: test
test:
	$(GOTEST) -v ./...

# Clean
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -rf bin/



# Install locally
.PHONY: install
install: build
	sudo cp $(BINARY_NAME) /usr/local/bin/

# Show help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, download deps, and build (GLIBC compatible)"
	@echo "  deps         - Download and tidy dependencies"
	@echo "  build        - Build for current platform (GLIBC compatible)"
	@echo "  build-all    - Build for all platforms (GLIBC compatible)"
	@echo "  deploy       - Build all platforms and upload to OSS"
	@echo "  test         - Run tests"
	@echo "  clean        - Clean build artifacts"
	@echo "  install      - Install binary to /usr/local/bin"
	@echo "  help         - Show this help"
	@echo ""
	@echo "All builds use static linking (CGO_ENABLED=0) for GLIBC compatibility"
