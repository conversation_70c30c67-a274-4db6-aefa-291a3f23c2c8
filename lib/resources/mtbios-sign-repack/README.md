# MTBios Sign and Repack Tool

A Go-based tool for signing binary files through MTBios signing server and repacking them into the final binary format.

## Features

- **Single Binary**: No dependencies, just one executable file
- **Cross-Platform**: Supports Linux, Windows, and macOS
- **Fast**: Quick startup and execution
- **Secure**: Credential management with cookie storage
- **Simple**: Easy command-line interface

## Installation

### Build from Source

```bash
# Clone and build
git clone <repository>
cd mtbios-sign-repack

# Build for current platform
make build

# Build for all platforms
make build-all

# Build for Jenkins shared library
make build-jenkins
```

### Download Binary

Download the pre-built binary for your platform from the releases page.

## Usage

### Login Mode (First Time)

Save your credentials for future use:

```bash
./mtbios-sign-repack -m login -u <EMAIL> -d sw
```

### Sign Mode

Sign and repack a binary file:

```bash
# Using saved credentials
./mtbios-sign-repack -f firmware.bin -o ./

# With explicit credentials
./mtbios-sign-repack -u <EMAIL> -d sw -f firmware.bin -o ./
```

## Command Line Options

```
-u  User email address [required for login mode]
-p  Email login password [optional, will prompt if not provided]
-d  Department name, default is SDM [optional]
-f  Signature file [required for sign mode]
-o  Output directory [required for sign mode]
-c  Cookie file path, default is ./login.cookie [optional]
-m  Mode: 'login' or 'sign' (default) [optional]
```

## Examples

```bash
# First time setup
./mtbios-sign-repack -m login -u <EMAIL> -d engineering

# Sign a firmware file
./mtbios-sign-repack -f mtfw-gen6rv.bin -o ./output/

# Sign with custom cookie file
./mtbios-sign-repack -c ./my-cookies.json -f firmware.bin -o ./
```

## Jenkins Integration

### Using in Jenkins Pipeline

```groovy
// In your Jenkinsfile
pipeline {
    agent any
    stages {
        stage('Sign Firmware') {
            steps {
                script {
                    // Use the shared library function
                    mtbiosSign([
                        mode: 'login',
                        username: '<EMAIL>',
                        department: 'sw'
                    ])

                    mtbiosSign([
                        file: 'firmware.bin',
                        output: './'
                    ])
                }
            }
        }
    }
}
```

## Development

### Prerequisites

- Go 1.21 or later
- Make (optional, for using Makefile)

### Building

```bash
# Build for current platform (GLIBC compatible)
make build

# Build for all platforms (GLIBC compatible)
make build-all

# Build and deploy to OSS (for production)
make deploy

# Manual build with GLIBC compatibility
CGO_ENABLED=0 go build -ldflags "-s -w" -o mtbios-sign-repack .
```

### Deployment

The `make deploy` target will:

1. Build binaries for all platforms with GLIBC compatibility
2. Upload binaries to OSS at `sh-moss/dependency/mtbios-sign-tool/`
3. Clean up local binary files

**OSS Structure:**

```
sh-moss/dependency/mtbios-sign-tool/
├── linux/
│   ├── mtbios-sign-repack
│   └── mtbios-sign-repack-arm64
├── windows/
│   └── mtbios-sign-repack.exe
└── darwin/
    ├── mtbios-sign-repack
    └── mtbios-sign-repack-arm64
```

### GLIBC Compatibility

All builds now use static linking (`CGO_ENABLED=0`) to ensure maximum compatibility across different Linux distributions and GLIBC versions. This solves the common issue:

```
/lib/x86_64-linux-gnu/libc.so.6: version GLIBC_2.34' not found
```

**Benefits of static linking:**

- ✅ No GLIBC version dependencies
- ✅ Works on older Linux distributions (GLIBC 2.31 and below)
- ✅ Self-contained binary with no external dependencies
- ✅ Slightly larger binary size (~5.1MB vs ~5.4MB dynamic)

**Verification:**

```bash
# Check if binary is statically linked
ldd mtbios-sign-repack
# Should output: "not a dynamic executable"
```

### Testing

```bash
make test
```

## File Structure

```
mtbios-sign-repack/
├── main.go           # Main application code
├── go.mod           # Go module definition
├── Makefile         # Build automation
├── README.md        # This file
└── bin/             # Built binaries (created by make)
    ├── linux/
    ├── windows/
    └── darwin/
```

## Comparison with Python Version

| Feature | Go Version | Python Version |
|---------|------------|----------------|
| Dependencies | None | requests-html, dateutil, lxml |
| Startup Time | ~1ms | ~200ms |
| Memory Usage | ~10MB | ~50MB |
| Deployment | Single file | Python + packages |
| Cross-compile | Yes | No |
| Performance | Fast | Moderate |

## License

[Your License Here]
