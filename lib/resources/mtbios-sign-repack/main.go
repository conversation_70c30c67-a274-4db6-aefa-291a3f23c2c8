package main

import (
	"archive/zip"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"golang.org/x/net/html"
	"golang.org/x/term"
)

const (
	ServerURL     = "https://mtbios-sign.mthreads.com"
	DefaultCookie = "login.cookie"
	DefaultDept   = "SDM"
	SignHeaderLen = 64
	SignSigLen    = 256
	SignExtraLen  = 64
)

type Config struct {
	Username   string
	Password   string
	Department string
	FilePath   string
	OutputDir  string
	CookieFile string
	Mode       string
}

type UserInfo struct {
	Username   string `json:"username"`
	Department string `json:"department"`
}

type CookieData struct {
	UserInfo *UserInfo `json:"_user_info,omitempty"`
	Cookies  []Cookie  `json:"cookies"`
}

type Cookie struct {
	Name     string `json:"name"`
	Value    string `json:"value"`
	Domain   string `json:"domain"`
	Path     string `json:"path"`
	Expires  string `json:"expires"`
	Secure   bool   `json:"secure"`
	HttpOnly bool   `json:"httponly"`
}

type SignResponse struct {
	Status string `json:"status"`
	Name   string `json:"name"`
}

var client *http.Client

func init() {
	jar, _ := cookiejar.New(nil)
	client = &http.Client{
		Jar:     jar,
		Timeout: 30 * time.Second,
	}
}

func main() {
	config := parseFlags()

	// Resolve cookie file path relative to binary location if it's a relative path
	config.CookieFile = resolveCookieFilePath(config.CookieFile)

	if config.Mode == "login" {
		handleLoginMode(config)
	} else {
		handleSignMode(config)
	}
}

func parseFlags() *Config {
	config := &Config{}

	flag.StringVar(&config.Username, "u", "", "User email address")
	flag.StringVar(&config.Password, "p", "", "Email login password")
	flag.StringVar(&config.Department, "d", DefaultDept, "Department name")
	flag.StringVar(&config.FilePath, "f", "", "Signature file")
	flag.StringVar(&config.OutputDir, "o", "", "Output directory")
	flag.StringVar(&config.CookieFile, "c", DefaultCookie, "Cookie file path")
	flag.StringVar(&config.Mode, "m", "sign", "Mode: login or sign")

	flag.Usage = func() {
		fmt.Printf("Usage: %s [options]\n\n", os.Args[0])
		fmt.Println("Command options:")
		fmt.Println("  -u  User email address [required for login mode]")
		fmt.Println("  -p  Email login password [optional, will prompt if not provided]")
		fmt.Println("  -d  Department name, default is SDM [optional]")
		fmt.Println("  -f  Signature file [required for sign mode]")
		fmt.Println("  -o  Output directory [required for sign mode]")
		fmt.Println("  -c  Cookie file path, default is login.cookie in binary directory [optional]")
		fmt.Println("  -m  Mode: 'login' or 'sign' (default) [optional]")
		fmt.Println("\nExamples:")
		fmt.Printf("  # Login mode\n  %s -m login -u <EMAIL> -d sw\n\n", os.Args[0])
		fmt.Printf("  # Sign mode\n  %s -f firmware.bin -o ./\n", os.Args[0])
	}

	flag.Parse()
	return config
}

// resolveCookieFilePath resolves the cookie file path relative to the binary location
// if it's a relative path, otherwise returns the absolute path as-is
func resolveCookieFilePath(cookieFile string) string {
	// If it's already an absolute path, return as-is
	if filepath.IsAbs(cookieFile) {
		return cookieFile
	}

	// Get the directory where the binary is located
	execPath, err := os.Executable()
	if err != nil {
		// If we can't get the executable path, fall back to current directory
		fmt.Printf("Warning: Could not determine binary location, using current directory for cookie file\n")
		return cookieFile
	}

	// Get the directory containing the binary
	binaryDir := filepath.Dir(execPath)

	// Join the binary directory with the cookie file name
	resolvedPath := filepath.Join(binaryDir, cookieFile)

	return resolvedPath
}

func handleLoginMode(config *Config) {
	if config.Username == "" {
		fmt.Println("Error: Username (-u) is required for login mode")
		flag.Usage()
		os.Exit(1)
	}

	if config.Password == "" {
		fmt.Print("Please enter password: ")
		passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
		if err != nil {
			fmt.Printf("Error reading password: %v\n", err)
			os.Exit(1)
		}
		config.Password = string(passwordBytes)
		fmt.Println()
	}

	err := login(config.Username, config.Password, config.Department, config.CookieFile)
	if err != nil {
		fmt.Printf("Login failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Login successful! Cookie saved to %s\n", config.CookieFile)
	fmt.Printf("You can now sign files without providing username/password:\n")
	fmt.Printf("%s -f your_file.bin -o ./\n", os.Args[0])
}

func handleSignMode(config *Config) {
	if config.FilePath == "" || config.OutputDir == "" {
		fmt.Println("Error: Both -f (file) and -o (output) are required for sign mode")
		flag.Usage()
		os.Exit(1)
	}

	if _, err := os.Stat(config.FilePath); os.IsNotExist(err) {
		fmt.Printf("Error: File not found: %s\n", config.FilePath)
		os.Exit(1)
	}

	if _, err := os.Stat(config.OutputDir); os.IsNotExist(err) {
		fmt.Printf("Error: Output directory not found: %s\n", config.OutputDir)
		os.Exit(1)
	}

	// Load user credentials from cookie if available
	userInfo, err := loadUserCredentials(config.CookieFile)
	if err == nil && userInfo != nil {
		if config.Username == "" {
			config.Username = userInfo.Username
		}
		if config.Department == DefaultDept {
			config.Department = userInfo.Department
		}
	}

	// Check authentication
	if !checkLoginStatus() {
		if config.Username == "" {
			fmt.Println("Error: Username (-u) is required when no valid cookie exists")
			flag.Usage()
			os.Exit(1)
		}

		if config.Password == "" {
			fmt.Print("Please enter password: ")
			passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
			if err != nil {
				fmt.Printf("Error reading password: %v\n", err)
				os.Exit(1)
			}
			config.Password = string(passwordBytes)
			fmt.Println()
		}

		err := login(config.Username, config.Password, config.Department, config.CookieFile)
		if err != nil {
			fmt.Printf("Login failed: %v\n", err)
			os.Exit(1)
		}
	}

	// Perform signing and repacking
	err = performSigningAndRepacking(config)
	if err != nil {
		fmt.Printf("Signing failed: %v\n", err)
		os.Exit(1)
	}
}

func loadUserCredentials(cookieFile string) (*UserInfo, error) {
	if _, err := os.Stat(cookieFile); os.IsNotExist(err) {
		return nil, err
	}

	data, err := os.ReadFile(cookieFile)
	if err != nil {
		return nil, err
	}

	var cookieData CookieData
	err = json.Unmarshal(data, &cookieData)
	if err != nil {
		return nil, err
	}

	// Load cookies into client
	if len(cookieData.Cookies) > 0 {
		serverURL, _ := url.Parse(ServerURL)
		var cookies []*http.Cookie
		for _, c := range cookieData.Cookies {
			cookie := &http.Cookie{
				Name:     c.Name,
				Value:    c.Value,
				Domain:   c.Domain,
				Path:     c.Path,
				Secure:   c.Secure,
				HttpOnly: c.HttpOnly,
			}
			cookies = append(cookies, cookie)
		}
		client.Jar.SetCookies(serverURL, cookies)
	}

	return cookieData.UserInfo, nil
}

func checkLoginStatus() bool {
	resp, err := client.Get(ServerURL + "/api/auth/session")
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return false
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false
	}

	bodyText := strings.ToLower(string(body))
	return !strings.Contains(bodyText, "login") && !strings.Contains(bodyText, "signin")
}

func login(username, password, department, cookieFile string) error {
	// Step 1: Get login page
	callbackURL := url.QueryEscape(ServerURL + "/")
	loginURL := fmt.Sprintf("%s/api/auth/signin?callbackUrl=%s", ServerURL, callbackURL)

	resp, err := client.Get(loginURL)
	if err != nil {
		return fmt.Errorf("failed to get login page: %v", err)
	}
	defer resp.Body.Close()

	// Parse form parameters
	formParams, err := parseLoginForm(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to parse login form: %v", err)
	}

	// Step 2: Submit initial form
	formData := url.Values{}
	for k, v := range formParams {
		formData.Set(k, v)
	}

	resp, err = client.PostForm(formParams["action"], formData)
	if err != nil {
		return fmt.Errorf("failed to submit initial form: %v", err)
	}
	defer resp.Body.Close()

	// Step 3: Submit credentials
	credentialsForm, err := parseLoginForm(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to parse credentials form: %v", err)
	}

	credData := url.Values{}
	credData.Set("username", username)
	credData.Set("password", password)

	resp, err = client.PostForm(credentialsForm["action"], credData)
	if err != nil {
		return fmt.Errorf("failed to submit credentials: %v", err)
	}
	defer resp.Body.Close()

	// Step 4: Verify login success and save cookies
	return saveCookies(username, department, cookieFile)
}

func parseLoginForm(body io.Reader) (map[string]string, error) {
	doc, err := html.Parse(body)
	if err != nil {
		return nil, err
	}

	form := findForm(doc)
	if form == nil {
		return nil, fmt.Errorf("no form found")
	}

	result := make(map[string]string)

	// Get form action
	for _, attr := range form.Attr {
		if attr.Key == "action" {
			result["action"] = attr.Val
			break
		}
	}

	// Get input fields
	var findInputs func(*html.Node)
	findInputs = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "input" {
			var name, value string
			for _, attr := range n.Attr {
				if attr.Key == "name" {
					name = attr.Val
				} else if attr.Key == "value" {
					value = attr.Val
				}
			}
			if name != "" {
				result[name] = value
			}
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			findInputs(c)
		}
	}
	findInputs(form)

	return result, nil
}

func findForm(n *html.Node) *html.Node {
	if n.Type == html.ElementNode && n.Data == "form" {
		return n
	}
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if form := findForm(c); form != nil {
			return form
		}
	}
	return nil
}

func saveCookies(username, department, cookieFile string) error {
	serverURL, _ := url.Parse(ServerURL)
	cookies := client.Jar.Cookies(serverURL)

	// Check if login was successful
	hasSessionToken := false
	var cookieData []Cookie
	for _, cookie := range cookies {
		if cookie.Name == "__Secure-next-auth.session-token" {
			hasSessionToken = true
		}
		cookieData = append(cookieData, Cookie{
			Name:     cookie.Name,
			Value:    cookie.Value,
			Domain:   cookie.Domain,
			Path:     cookie.Path,
			Expires:  cookie.Expires.Format(time.RFC3339),
			Secure:   cookie.Secure,
			HttpOnly: cookie.HttpOnly,
		})
	}

	if !hasSessionToken {
		return fmt.Errorf("login failed: no session token found")
	}

	// Save cookies and user info
	data := CookieData{
		UserInfo: &UserInfo{
			Username:   username,
			Department: department,
		},
		Cookies: cookieData,
	}

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal cookie data: %v", err)
	}

	err = os.WriteFile(cookieFile, jsonData, 0600)
	if err != nil {
		return fmt.Errorf("failed to save cookie file: %v", err)
	}

	return nil
}

func performSigningAndRepacking(config *Config) error {
	// Create temporary directory
	tempDir, err := os.MkdirTemp("", "mtbios-sign-*")
	if err != nil {
		return fmt.Errorf("failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Sign and download file
	zipPath, err := signAndDownloadFile(config.FilePath, tempDir, config.Username, config.Department)
	if err != nil {
		return fmt.Errorf("failed to sign file: %v", err)
	}

	// Repack signed firmware
	originalFilename := filepath.Base(config.FilePath)
	outputPath := filepath.Join(config.OutputDir, originalFilename)

	err = repackSignedFirmware(zipPath, outputPath)
	if err != nil {
		return fmt.Errorf("failed to repack firmware: %v", err)
	}

	fmt.Printf("\nProcess completed successfully!\n")
	fmt.Printf("Final output: %s\n", outputPath)
	return nil
}

func signAndDownloadFile(filePath, tempDir, email, department string) (string, error) {
	// Prepare multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Add form fields
	writer.WriteField("uid", fmt.Sprintf("rc-upload-%d", time.Now().UnixNano()))
	writer.WriteField("department", department)
	writer.WriteField("purpose", "test")
	writer.WriteField("email", email)
	writer.WriteField("validityDate", getValidityDate())

	// Add file
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	filename := filepath.Base(filePath)
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return "", fmt.Errorf("failed to create form file: %v", err)
	}

	_, err = io.Copy(part, file)
	if err != nil {
		return "", fmt.Errorf("failed to copy file: %v", err)
	}

	writer.Close()

	// Upload and sign
	req, err := http.NewRequest("POST", ServerURL+"/api/filedata/file", &buf)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Referer", ServerURL)
	req.Header.Set("Origin", ServerURL)

	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to upload file: %v", err)
	}
	defer resp.Body.Close()

	// Parse response
	var signResp SignResponse
	err = json.NewDecoder(resp.Body).Decode(&signResp)
	if err != nil {
		return "", fmt.Errorf("failed to parse response: %v", err)
	}

	if signResp.Status != "ok" {
		return "", fmt.Errorf("signing failed: %s", signResp.Status)
	}

	fmt.Printf("File signed successfully: %s\n", signResp.Name)

	// Download signed file
	downloadURL := fmt.Sprintf("%s/api/filedata/file/%s", ServerURL, signResp.Name)
	req, err = http.NewRequest("GET", downloadURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create download request: %v", err)
	}

	req.Header.Set("Referer", ServerURL)
	req.Header.Set("Origin", ServerURL)

	resp, err = client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to download file: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("download failed with status: %d", resp.StatusCode)
	}

	// Save to temp directory
	zipPath := filepath.Join(tempDir, signResp.Name)
	outFile, err := os.Create(zipPath)
	if err != nil {
		return "", fmt.Errorf("failed to create output file: %v", err)
	}
	defer outFile.Close()

	_, err = io.Copy(outFile, resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to save file: %v", err)
	}

	fmt.Printf("Signature file downloaded successfully: %s\n", zipPath)
	return zipPath, nil
}

func getValidityDate() string {
	return time.Now().AddDate(0, 6, 0).Format("Mon, 02 Jan 2006 15:04:05 GMT")
}

func repackSignedFirmware(zipPath, outputPath string) error {
	// Create temporary directory for extraction
	extractDir, err := os.MkdirTemp("", "extract-*")
	if err != nil {
		return fmt.Errorf("failed to create extract directory: %v", err)
	}
	defer os.RemoveAll(extractDir)

	// Extract ZIP file
	fmt.Printf("Extracting %s...\n", filepath.Base(zipPath))
	err = extractZip(zipPath, extractDir)
	if err != nil {
		return fmt.Errorf("failed to extract zip: %v", err)
	}

	// Find required files
	files, err := findRequiredFiles(extractDir)
	if err != nil {
		return err
	}

	// Validate file sizes
	err = validateFileSizes(files)
	if err != nil {
		return err
	}

	// Determine header content
	headerContent := determineHeaderContent(files["metadata"])
	fmt.Printf("Using header content: %s\n", headerContent)

	// Create output file
	fmt.Printf("Creating output file: %s\n", outputPath)
	err = createFinalBinary(outputPath, files, headerContent)
	if err != nil {
		return err
	}

	// Print file info
	printFileInfo(outputPath, files, headerContent)
	return nil
}

func extractZip(zipPath, destDir string) error {
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return err
	}
	defer reader.Close()

	for _, file := range reader.File {
		path := filepath.Join(destDir, file.Name)

		// Create directory if needed
		if file.FileInfo().IsDir() {
			os.MkdirAll(path, file.FileInfo().Mode())
			continue
		}

		// Create file
		fileReader, err := file.Open()
		if err != nil {
			return err
		}
		defer fileReader.Close()

		targetFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.FileInfo().Mode())
		if err != nil {
			return err
		}
		defer targetFile.Close()

		_, err = io.Copy(targetFile, fileReader)
		if err != nil {
			return err
		}
	}
	return nil
}

func findRequiredFiles(extractDir string) (map[string]string, error) {
	files := map[string]string{
		"bin":      "",
		"sig":      "",
		"extra":    "",
		"metadata": "",
	}

	err := filepath.Walk(extractDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		filename := info.Name()
		switch {
		case strings.HasSuffix(filename, ".bin"):
			files["bin"] = path
		case strings.HasSuffix(filename, ".sig"):
			files["sig"] = path
		case strings.HasSuffix(filename, ".extra"):
			files["extra"] = path
		case filename == "metadata.json":
			files["metadata"] = path
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// Check required files exist
	if files["bin"] == "" || files["sig"] == "" || files["extra"] == "" {
		return nil, fmt.Errorf("required files (.bin, .sig, .extra) not found in the archive")
	}

	return files, nil
}

func validateFileSizes(files map[string]string) error {
	sigInfo, err := os.Stat(files["sig"])
	if err != nil {
		return err
	}
	if sigInfo.Size() != SignSigLen {
		return fmt.Errorf(".sig file size mismatch. Expected: %d, Got: %d", SignSigLen, sigInfo.Size())
	}

	extraInfo, err := os.Stat(files["extra"])
	if err != nil {
		return err
	}
	if extraInfo.Size() != SignExtraLen {
		return fmt.Errorf(".extra file size mismatch. Expected: %d, Got: %d", SignExtraLen, extraInfo.Size())
	}

	return nil
}

func determineHeaderContent(metadataPath string) string {
	if metadataPath != "" {
		data, err := os.ReadFile(metadataPath)
		if err == nil && strings.Contains(string(data), "encrypt") {
			return "encrypt"
		}
	}
	return "sig"
}

func createFinalBinary(outputPath string, files map[string]string, headerContent string) error {
	outFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// Create and write header
	header := make([]byte, SignHeaderLen)
	copy(header, []byte(headerContent))
	_, err = outFile.Write(header)
	if err != nil {
		return err
	}

	// Write bin, sig, extra files in order
	for _, fileType := range []string{"bin", "sig", "extra"} {
		file, err := os.Open(files[fileType])
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = io.Copy(outFile, file)
		if err != nil {
			return err
		}
	}

	return nil
}

func printFileInfo(outputPath string, files map[string]string, headerContent string) {
	binInfo, _ := os.Stat(files["bin"])
	sigInfo, _ := os.Stat(files["sig"])
	extraInfo, _ := os.Stat(files["extra"])

	totalSize := SignHeaderLen + int(binInfo.Size()) + int(sigInfo.Size()) + int(extraInfo.Size())

	fmt.Printf("Successfully created %s\n", outputPath)
	fmt.Println("File composition:")
	fmt.Printf("  Header (%s): %d bytes\n", headerContent, SignHeaderLen)
	fmt.Printf("  Bin file: %d bytes\n", binInfo.Size())
	fmt.Printf("  Sig file: %d bytes\n", sigInfo.Size())
	fmt.Printf("  Extra file: %d bytes\n", extraInfo.Size())
	fmt.Printf("  Total size: %d bytes\n", totalSize)
}
