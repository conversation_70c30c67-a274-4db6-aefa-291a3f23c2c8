#!/bin/bash

check_run_user() {
    if [ "$EUID" -ne 0 ]; then
        echo "请使用sudo/root运行!"
        exit 1
    fi
}

get_os_type() {
    #Ubuntu/Kylin/uos
    os_type=$(head -n 1 /etc/lsb-release | awk -F '=' '{print $2}')
}

get_platform() {
    platform=$(uname -m)
}

set_root_ssh() {
    echo "root:Passw0rd!" | sudo chpasswd
    #/etc/ssh/sshd_config里面PermitRootLogin yes
    if grep -E "^PermitRootLogin " /etc/ssh/sshd_config; then
        sudo sed -i "s/^PermitRootLogin.*/PermitRootLogin yes/" /etc/ssh/sshd_config
    else
        if grep -E "^#PermitRootLogin " /etc/ssh/sshd_config; then
            sudo sed -i "s/^#PermitRootLogin.*/PermitRootLogin yes/" /etc/ssh/sshd_config
        else
            sudo sed -i '$a PermitRootLogin yes' /etc/ssh/sshd_config
        fi
    fi
    sudo systemctl restart sshd
}

init() {
    get_os_type
    get_platform
    set_root_ssh

    sudo apt install openjdk-17-jdk -y
    sudo apt install dkms -y
    if [[ "$os_type" == "Ubuntu" && "$platform" == "x86_64" ]]; then
        if grep "file:///cdrom" /etc/apt/sources.list; then
            sudo mv /etc/apt/sources.list.d/original.list /etc/apt/sources.list
        fi
        # sudo sed -i "s/^deb https:\/\/.*\/repository\/apt-proxy/deb http:\/\/mirrors.tuna.tsinghua.edu.cn\/ubuntu/" /etc/apt/sources.list
        sudo apt update

        sudo apt-mark hold linux-headers-5.15.0-105-generic
        sudo apt install ubuntu-desktop -y
    fi
}

check_run_user
init

# TODO: is below neccessary?
if [ -f "/etc/apt/sources.list.bak" ]; then
    rm -rf /etc/apt/sources.list.bak
fi
if [ -d "/etc/apt/sources.list.d.bak" ]; then
    rm -rf /etc/apt/sources.list.d.bak/
fi

# config apt
cd /tmp || exit
{
    apt-get install ca-certificates --reinstall
    apt update
} || {
    wget -q --no-check-certificate https://oss.mthreads.com/sw-infra-devops/miyongpeng/tools/ca-certificates_20211016ubuntu0.20.04.1_all.deb
    dpkg -i ca-certificates_20211016ubuntu0.20.04.1_all.deb
    apt-get update
}
wget -q https://oss.mthreads.com/sw-infra-devops/miyongpeng/tools/3b4fe6acc0b21f32.key --no-check-certificate
wget -q https://oss.mthreads.com/sw-infra-devops/miyongpeng/tools/871920d1991bc93c.key --no-check-certificate
echo 3b4fe6acc0b21f32.key | sudo apt-key add
echo 871920d1991bc93c.key | sudo apt-key add
OS_VERSION=$(lsb_release -cs)  # output: jammy
cat > /etc/apt/sources.list <<EOF
deb https://nexus.shg1.mthreads.com/repository/apt-proxy/ ${OS_VERSION} main restricted universe multiverse
deb https://nexus.shg1.mthreads.com/repository/apt-proxy/ ${OS_VERSION}-security main restricted universe multiverse
EOF
apt-get update

# install docker
cd /tmp || exit
deb_names=("containerd.io_1.7.22-1_amd64.deb" "docker-ce-cli_27.3.1-1~ubuntu.22.04~jammy_amd64.deb" "docker-ce_27.3.1-1~ubuntu.22.04~jammy_amd64.deb")
for deb_name in "${deb_names[@]}"; do
    if [ -f "$deb_name" ]; then
        rm -rf "$deb_name"
    fi

    wget -q --no-check-certificate https://oss.mthreads.com/installation-files/docker-ce/ubuntu22.04-docker27.3.1/"$deb_name"
    dpkg -i "$deb_name"
done
docker version
docker images
usermod -aG docker "${USER}"
echo '{
    "insecure-registries" : ["sh-harbor.mthreads.com"]
}' >/etc/docker/daemon.json
systemctl restart docker

# install other dependencies
apt_requirements="clinfo waffle-utils mesa-utils curl git openjdk-11-jdk python3-pip cmake g++ mesa-common-dev libgl1-mesa-dev python3-numpy python3-mako python3-pip freeglut3-dev x11proto-gl-dev libxrender-dev libwaffle-dev libudev-dev libpng-dev libvulkan-dev glslang-tools libdrm-dev libgbm-dev wayland-protocols pkg-config glmark2"
apt install "${apt_requirements}" -y

# install python dependencies
# py_requirements="pytest==7.1.3 allure-pytest pandas"
# pip3 install ${py_requirements} --index="http://nexus.infra.shg1.mthreads.com/repository/pypi-public/simple" --trusted-host=nexus.infra.shg1.mthreads.com

# deploy piglit
cd /root/ && rm -rf piglit* && wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/piglit/piglit.tar.gz && tar xzf piglit*.tar.gz
# https://oss.mthreads.com/tmp/weihua/piglit_2card.tar.gz

# deploy pdump
cd /root/ || exit
rm -rf pdump*
git config --global user.email "<EMAIL>"
git config --global user.name "git-robot"
git clone https://sh-jenkins-sh-code:<EMAIL>/sw/pdump_test.git

# install mc
# check if mc exists
ARCH=$(uname -m)
MC_INSTALL_PATH="/usr/local/bin/mc"
if [ -x "$MC_INSTALL_PATH" ]; then
    mc alias set oss https://oss.mthreads.com mtoss mtoss123
    echo "mc is already installed."
else
    case "$ARCH" in
    x86_64)
        MC_URL="https://oss.mthreads.com/installation-files/mc/linux-amd64/2022-06-26/mc"
        ;;
    aarch64)
        MC_URL="https://oss.mthreads.com/installation-files/mc/linux-arm64/2022-06-26/mc"
        ;;
    *)
        echo "Unsupported architecture: $ARCH"
        exit 1
        ;;
    esac
    wget -q "$MC_URL" -O "$MC_INSTALL_PATH" --no-check-certificate
    chmod +x "$MC_INSTALL_PATH"
    mc alias set oss https://oss.mthreads.com mtoss mtoss123
    echo "mc has been installed."
fi

# get model and os
MODEL=intel
cpu_model=$(lscpu | grep -E 'Model Name|型号名称')
[[ ${cpu_model,,} =~ "zhaoxin" ]] && MODEL=zhaoxin
[[ ${cpu_model,,} =~ "hygon" ]] && MODEL=hygon
[[ ${cpu_model,,} =~ "phytium" ]] && MODEL=phytium
[[ ${cpu_model,,} =~ "d2000" ]] && MODEL=phytium

OS=ubuntu
os_release=$(cat /etc/os-release)
[[ ${os_release,,} =~ "uos" ]] && OS=uos
[[ ${os_release,,} =~ "kylin" ]] && OS=kylin

# deploy cts
get_latest_version() {
    local var=$1
    array=("${var//./}")
    max=${array[0]}
    for ((i = 0; i < ${#array[*]}; i++)); do
        if [ "${array[$i]}" -gt "$max" ]; then
            max=${array[$i]}
        else
            continue
        fi
    done
    re=${max:0:1}.${max:1:1}.${max:2:1}.${max:3:1}
    echo -n "$re"
}

get_cts_file() {
    local cts_type=$1
    local Architecture=$2
    local search_path=""
    local filter=""

    case $cts_type in
    gles)
        search_path="sh-moss/sw-release/cts/gles"
        filter="opengl-es-cts-*-xorg-$Architecture.*"
        result=$(/usr/local/bin/mc find "$search_path" --name "$filter")
        var=$(echo "$result" | awk -F "-" '{print $5}')
        version=$(get_latest_version "$var")
        cts_file="opengl-es-cts-$version-xorg-$Architecture"
        ;;
    gl)
        search_path="sh-moss/sw-release/cts/gl"
        filter="opengl-cts-*-xorg-$Architecture.*"
        result=$(/usr/local/bin/mc find "$search_path" --name "$filter")
        var=$(echo "$result" | awk -F "-" '{print $4}')
        version=$(get_latest_version "$var")
        cts_file="opengl-cts-$version-xorg-$Architecture"
        ;;
    vk)
        search_path="sh-moss/sw-release/cts/vk"
        filter="vulkan-cts-*-xorg-$Architecture.*"
        result=$(/usr/local/bin/mc find "$search_path" --name "$filter")
        var=$(echo "$result" | awk -F "-" '{print $4}')
        version=$(get_latest_version "$var")
        cts_file="vulkan-cts-$version-xorg-$Architecture"
        ;;
    *)
        echo "NOT FOUND"
        return
        ;;
    esac

    cts_path="$search_path/$cts_file.tar.gz"
    mkdir -p /root/cts/"$cts_type" && cd /root/cts/"$cts_type" || exit
    mc cp -r "$cts_path" . && tar -zxvf ./*
    rm -rf cts_"$cts_type" || true && ln -s /root/cts/"$cts_type"/"$cts_file" /root/cts_"$cts_type"
}

cd /root/ || exit
rm -rf cts gl gles vk
mkdir -p /root/sw
cts_types=("gles" "gl" "vk")

for type in "${cts_types[@]}"; do
    get_cts_file "$type" "$MODEL-$OS"
done

# update ulimit
if [ -z "$(sudo grep -E '^* soft nofile' /etc/security/limits.conf)" ]; then
    sudo sed -i '/# End of file/i\\* soft nofile 1000000' /etc/security/limits.conf
    echo "updated soft nofile.."
fi
if [ -z "$(sudo grep -E '^* hard nofile' /etc/security/limits.conf)" ]; then
    sudo sed -i '/# End of file/i\\* hard nofile 1000000' /etc/security/limits.conf
    echo "updated hard nofile.."
fi
