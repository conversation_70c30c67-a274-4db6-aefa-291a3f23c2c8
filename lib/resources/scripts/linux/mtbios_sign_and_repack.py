#!/usr/bin/env python3
"""
MTBios Sign and Repack Tool

This tool combines the functionality of signing binary files through MTBios signing server
and repacking the signed files into the final binary format.

Usage:
    # Login mode - save credentials for future use
    python mtbios_sign_and_repack.py -m login -u <EMAIL> -d department

    # Sign mode - sign and repack binary file
    python mtbios_sign_and_repack.py -f firmware.bin -o ./
"""

import getopt
import getpass
import json
import mimetypes
import os
import shutil
import sys
import tempfile
import time
import urllib.parse
import zipfile
from datetime import datetime

from dateutil.relativedelta import relativedelta
from requests_html import HTMLSession

# Configuration
SERVER_URL = "https://mtbios-sign.mthreads.com"
DEFAULT_COOKIE_FILE = "./login.cookie"
DEFAULT_DEPARTMENT = "SDM"

# Constants for firmware repacking
SIGN_HEADER_LEN = 64
SIGN_SIG_LEN = 256
SIGN_EXTRA_LEN = 64

# Global session
session = HTMLSession()


# ============================================================================
# Utility Functions
# ============================================================================


def usage():
    """Print usage information."""
    script_name = os.path.basename(sys.argv[0])
    print(f"Usage: python ./{script_name} [options]")
    print("""
Command options:
  -u  User email address [required for login mode, optional for sign mode if cookie exists]
  -p  Email login password [optional, will prompt if not provided]
  -d  Department name, default is SDM [optional]
  -f  Signature file [required for sign mode]
  -o  Output directory for final .bin file [required for sign mode]
  -c  Cookie file path, default is ./login.cookie [optional]
  -m  Mode: 'login' to login and save cookie, 'sign' to sign file (default) [optional]

Examples:
  # First time login (saves cookie for future use)
  python ./{script_name} -m login -u <EMAIL> -d sw

  # Sign file using saved cookie
  python ./{script_name} -f firmware.bin -o ./

  # Sign file with explicit login
  python ./{script_name} -u <EMAIL> -d sw -f firmware.bin -o ./
    """)


def save_file(file_path: str, content) -> None:
    """Save content to file, creating directories if needed."""
    if "/" in file_path:
        directory = os.path.dirname(file_path)
        if directory and not os.path.exists(directory):
            os.makedirs(directory)

    mode = "w" if isinstance(content, str) else "wb"
    encoding = "utf-8" if isinstance(content, str) else None

    with open(file_path, mode, encoding=encoding) as f:
        f.write(content)


def read_file(file_path: str, encoding: str = "utf-8") -> str:
    """Read file content as string."""
    if not os.path.exists(file_path):
        return ""

    with open(file_path, "r", encoding=encoding) as f:
        return f.read()


def to_json_str(data, pretty: bool = False) -> str:
    """Convert data to JSON string."""
    if not data:
        return ""

    if pretty:
        return json.dumps(data, ensure_ascii=False, indent=4)
    return json.dumps(data, ensure_ascii=False, separators=(",", ":"))


def to_json(json_str: str):
    """Parse JSON string to object."""
    if not json_str:
        return None

    try:
        return json.loads(json_str)
    except (ValueError, json.JSONDecodeError) as e:
        print(f"JSON parse error: {e}")
        return None


def get_validity_date() -> str:
    """Get validity date 6 months from now."""
    now = datetime.now()
    future_date = now + relativedelta(months=6)
    return future_date.strftime("%a, %d %b %Y 23:59:59 GMT")


# ============================================================================
# Authentication Functions
# ============================================================================


def build_multipart_data(boundary: str, form_data: dict, file_path: str) -> str:
    """Build multipart form data for file upload."""
    lines = []

    # Determine MIME type
    mime_type, _ = mimetypes.guess_type(file_path)
    if not mime_type:
        mime_type = "application/octet-stream"

    # Add form fields
    for name, value in form_data.items():
        lines.extend(
            [
                f"--{boundary}",
                f'Content-Disposition: form-data; name="{name}"',
                "",
                value,
            ]
        )

    # Add file
    file_name = os.path.basename(file_path)
    lines.extend(
        [
            f"--{boundary}",
            f'Content-Disposition: form-data; name="file"; filename="{file_name}"',
            f"Content-Type: {mime_type}",
            "",
        ]
    )

    with open(file_path, "rb") as f:
        file_content = f.read()
    lines.append(file_content.decode("ISO-8859-1"))

    lines.extend([f"--{boundary}--", ""])
    return "\r\n".join(lines)


def load_cookies(cookie_file: str) -> dict:
    """Load cookies from file and set them in session."""
    cookie_data = to_json(read_file(cookie_file))
    if not cookie_data:
        raise Exception(f"Cookie file {cookie_file} is empty or invalid")

    user_info = None
    for cookie_name, cookie_info in cookie_data.items():
        if cookie_name == "_user_info":
            user_info = cookie_info
            continue

        session.cookies.set(
            name=cookie_info["name"],
            value=cookie_info["value"],
            domain=cookie_info["domain"],
            expires=cookie_info["expires"],
            path=cookie_info["path"],
            rfc2109=cookie_info["rfc2109"],
            secure=cookie_info["secure"],
        )

    return user_info


def save_cookies(cookie_file: str, cookies, user_info: dict = None) -> None:
    """Save cookies and user info to file."""
    if not cookie_file:
        return

    cookie_data = {}
    for cookie in cookies:
        cookie_data[cookie.name] = {
            "name": cookie.name,
            "value": cookie.value,
            "domain": cookie.domain,
            "expires": cookie.expires,
            "path": cookie.path,
            "rfc2109": cookie.rfc2109,
            "secure": cookie.secure,
        }

    # Save user info for future use
    if user_info:
        cookie_data["_user_info"] = user_info

    save_file(cookie_file, to_json_str(cookie_data))


def check_login_status() -> bool:
    """Check if current session is logged in."""
    try:
        session_url = f"{SERVER_URL}/api/auth/session"
        response = session.get(session_url)
        if response.status_code == 200:
            response_text = response.text.lower()
            # If response doesn't contain login-related errors, assume we're logged in
            return "login" not in response_text and "signin" not in response_text
    except Exception:
        pass
    return False


def login(
    username: str,
    password: str,
    department: str = DEFAULT_DEPARTMENT,
    cookie_file: str = DEFAULT_COOKIE_FILE,
) -> None:
    """Login to the signing server and save credentials."""
    # Clear existing cookies
    session.cookies.clear()

    # Step 1: Get login page
    callback_url = urllib.parse.quote(f"{SERVER_URL}/")
    login_url = f"{SERVER_URL}/api/auth/signin?callbackUrl={callback_url}"

    response = session.get(login_url)
    login_form = response.html.find("form", first=True)

    # Extract form parameters
    form_params = {}
    for input_field in login_form.find("input"):
        form_params[input_field.attrs["name"]] = input_field.attrs["value"]

    # Step 2: Submit initial form
    response = session.post(
        login_form.attrs["action"], data=form_params, allow_redirects=True
    )

    # Step 3: Submit credentials
    login_form = response.html.find("form", first=True)
    credentials = {"username": username, "password": password}
    session.post(login_form.attrs["action"], data=credentials, allow_redirects=True)

    # Step 4: Verify login success
    has_login = any(
        cookie.name == "__Secure-next-auth.session-token" for cookie in session.cookies
    )

    if not has_login:
        raise Exception("Login failed: Please check your username and password")

    # Step 5: Save credentials
    user_info = {"username": username, "department": department}
    save_cookies(cookie_file, session.cookies, user_info)
    print(f"Login successful, saved cookie {cookie_file}")


# ============================================================================
# Signing Functions
# ============================================================================


def sign_and_download_file(
    file_path: str, temp_dir: str, email: str, department: str
) -> str:
    """Sign file and download the signed zip file to temp directory."""
    boundary = "----WebKitFormBoundaryYeozW9rm38UBtU2o"
    sign_url = f"{SERVER_URL}/api/filedata/file"

    # Prepare form data
    form_data = {
        "uid": f"rc-upload-{time.time_ns()}",
        "department": department,
        "purpose": "test",
        "email": email,
        "validityDate": get_validity_date(),
    }

    # Prepare headers
    headers = {
        "Referer": SERVER_URL,
        "Origin": SERVER_URL,
        "Content-Type": f"multipart/form-data; boundary={boundary}",
    }

    # Upload and sign file
    multipart_data = build_multipart_data(boundary, form_data, file_path)
    response = session.post(
        sign_url, headers=headers, data=multipart_data.encode("ISO-8859-1")
    )

    # Parse response
    sign_result = to_json(response.text)
    if not sign_result or sign_result.get("status") != "ok":
        raise Exception(f"Sign file failed: {response.text}")

    signed_file_name = sign_result["name"]
    print(f"File signed successfully: {signed_file_name}")

    # Download signed file
    download_url = f"{SERVER_URL}/api/filedata/file/{signed_file_name}"
    download_response = session.get(
        download_url, headers={"Referer": SERVER_URL, "Origin": SERVER_URL}
    )

    if download_response.status_code != 200:
        raise Exception(f"Download failed: {download_response.status_code}")

    # Save to temp directory
    zip_file_path = os.path.join(temp_dir, signed_file_name)
    save_file(zip_file_path, download_response.content)
    print(f"Signature file downloaded successfully: {zip_file_path}")

    return zip_file_path


# ============================================================================
# Firmware Repacking Functions
# ============================================================================


def repack_signed_firmware(
    zip_file_path: str, output_dir: str, original_filename: str
) -> str:
    """Extract zip file and repack into final .bin file."""
    extract_dir = tempfile.mkdtemp()

    try:
        # Extract ZIP file
        print(f"Extracting {os.path.basename(zip_file_path)}...")
        with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
            zip_ref.extractall(extract_dir)

        # Find required files
        files = _find_required_files(extract_dir)
        _validate_file_sizes(files)

        # Determine header content
        header_content = _determine_header_content(files.get("metadata"))
        print(f"Using header content: {header_content}")

        # Create output file
        output_file = os.path.join(output_dir, original_filename)
        print(f"Creating output file: {output_file}")

        _create_final_binary(output_file, files, header_content)
        _print_file_info(output_file, files, header_content)

        return output_file

    finally:
        shutil.rmtree(extract_dir, ignore_errors=True)


def _find_required_files(extract_dir: str) -> dict:
    """Find required files in extracted directory."""
    files = {"bin": None, "sig": None, "extra": None, "metadata": None}

    for root, _, filenames in os.walk(extract_dir):
        for filename in filenames:
            file_path = os.path.join(root, filename)
            if filename.endswith(".bin"):
                files["bin"] = file_path
            elif filename.endswith(".sig"):
                files["sig"] = file_path
            elif filename.endswith(".extra"):
                files["extra"] = file_path
            elif filename == "metadata.json":
                files["metadata"] = file_path

    # Check required files exist
    if not files["bin"] or not files["sig"] or not files["extra"]:
        raise Exception("Required files (.bin, .sig, .extra) not found in the archive")

    return files


def _validate_file_sizes(files: dict) -> None:
    """Validate that signature and extra files have correct sizes."""
    sig_size = os.path.getsize(files["sig"])
    extra_size = os.path.getsize(files["extra"])

    if sig_size != SIGN_SIG_LEN:
        raise Exception(
            f".sig file size mismatch. Expected: {SIGN_SIG_LEN}, Got: {sig_size}"
        )

    if extra_size != SIGN_EXTRA_LEN:
        raise Exception(
            f".extra file size mismatch. Expected: {SIGN_EXTRA_LEN}, Got: {extra_size}"
        )


def _determine_header_content(metadata_file: str) -> str:
    """Determine header content based on metadata."""
    if metadata_file and os.path.exists(metadata_file):
        with open(metadata_file, "r") as f:
            metadata = f.read()
            if "encrypt" in metadata:
                return "encrypt"
    return "sig"


def _create_final_binary(output_file: str, files: dict, header_content: str) -> None:
    """Create the final binary file by combining all components."""
    # Create header
    header = header_content.encode("utf-8")
    header += b"\x00" * (SIGN_HEADER_LEN - len(header))

    # Write final file: header + .bin + .sig + .extra
    with open(output_file, "wb") as out_f:
        out_f.write(header)

        for file_type in ["bin", "sig", "extra"]:
            with open(files[file_type], "rb") as f:
                out_f.write(f.read())


def _print_file_info(output_file: str, files: dict, header_content: str) -> None:
    """Print information about the created file."""
    bin_size = os.path.getsize(files["bin"])
    sig_size = os.path.getsize(files["sig"])
    extra_size = os.path.getsize(files["extra"])
    total_size = SIGN_HEADER_LEN + bin_size + sig_size + extra_size

    print(f"Successfully created {output_file}")
    print("File composition:")
    print(f"  Header ({header_content}): {SIGN_HEADER_LEN} bytes")
    print(f"  Bin file: {bin_size} bytes")
    print(f"  Sig file: {sig_size} bytes")
    print(f"  Extra file: {extra_size} bytes")
    print(f"  Total size: {total_size} bytes")


# ============================================================================
# Main Function
# ============================================================================


def main():
    """Main function to handle command line arguments and execute operations."""
    try:
        opts, args = getopt.getopt(sys.argv[1:], "u:p:d:f:o:c:m:")
    except getopt.GetoptError as e:
        print(f"Error: {e}")
        usage()
        sys.exit(2)

    # Initialize parameters with defaults
    username = ""
    password = ""
    department = DEFAULT_DEPARTMENT
    file_path = ""
    output_dir = ""
    cookie_file = DEFAULT_COOKIE_FILE
    mode = "sign"

    # Parse command line arguments
    for option, argument in opts:
        if option == "-u":
            username = argument
        elif option == "-p":
            password = argument
        elif option == "-d":
            department = argument or DEFAULT_DEPARTMENT
        elif option == "-f":
            file_path = argument
        elif option == "-o":
            output_dir = argument
        elif option == "-c":
            cookie_file = argument
        elif option == "-m":
            mode = argument

    # Handle login mode
    if mode == "login":
        _handle_login_mode(username, password, department, cookie_file)
        return

    # Handle sign mode
    _handle_sign_mode(
        username, password, department, file_path, output_dir, cookie_file
    )


def _handle_login_mode(username: str, password: str, department: str, cookie_file: str):
    """Handle login mode operations."""
    if not username:
        print("Error: Username (-u) is required for login mode")
        usage()
        sys.exit(1)

    if not password:
        password = getpass.getpass(f"Please enter the user {username} password:")

    try:
        login(username, password, department, cookie_file)
        print(f"Login successful! Cookie saved to {cookie_file}")
        print("You can now sign files without providing username/password:")
        print(f"python {os.path.basename(sys.argv[0])} -f your_file.bin -o ./")
    except Exception as e:
        print(f"Login failed: {e}")
        sys.exit(1)


def _handle_sign_mode(
    username: str,
    password: str,
    department: str,
    file_path: str,
    output_dir: str,
    cookie_file: str,
):
    """Handle sign mode operations."""
    # Validate required parameters
    if not file_path or not output_dir:
        usage()
        sys.exit(1)

    if not os.path.exists(file_path):
        print(f"Error: File not found: {file_path}")
        sys.exit(1)

    if not os.path.exists(output_dir):
        print(f"Error: Output directory not found: {output_dir}")
        sys.exit(1)

    # Get original filename
    original_filename = os.path.basename(file_path)

    # Load cookies and check login status
    username, department = _load_user_credentials(username, department, cookie_file)

    # Ensure we have valid authentication
    _ensure_authentication(username, password, department, cookie_file)

    # Perform signing and repacking
    _perform_signing_and_repacking(
        file_path, output_dir, original_filename, username, department
    )


def _load_user_credentials(username: str, department: str, cookie_file: str) -> tuple:
    """Load user credentials from cookie file if available."""
    if cookie_file and os.path.exists(cookie_file):
        try:
            user_info = load_cookies(cookie_file)
            if user_info:
                username = username or user_info.get("username", "")
                department = department or user_info.get(
                    "department", DEFAULT_DEPARTMENT
                )
        except Exception:
            pass

    return username, department


def _ensure_authentication(
    username: str, password: str, department: str, cookie_file: str
):
    """Ensure user is authenticated, login if necessary."""
    # Check if already logged in
    if check_login_status():
        return

    # Need to login
    if not username:
        print("Error: Username (-u) is required when no valid cookie exists")
        usage()
        sys.exit(1)

    if not password:
        password = getpass.getpass(f"Please enter the user {username} password:")

    try:
        login(username, password, department, cookie_file)
    except Exception as e:
        print(f"Login failed: {e}")
        sys.exit(1)


def _perform_signing_and_repacking(
    file_path: str,
    output_dir: str,
    original_filename: str,
    username: str,
    department: str,
):
    """Perform the actual signing and repacking operations."""
    temp_dir = tempfile.mkdtemp()

    try:
        # Sign and download file
        zip_file_path = sign_and_download_file(
            file_path, temp_dir, username, department
        )

        # Repack signed firmware
        final_output = repack_signed_firmware(
            zip_file_path, output_dir, original_filename
        )

        print("\nProcess completed successfully!")
        print(f"Final output: {final_output}")

    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    main()
