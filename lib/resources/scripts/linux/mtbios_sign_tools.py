import getopt
import getpass
import json
import mimetypes
import os
import sys
import time
import urllib.parse
from datetime import datetime

from dateutil.relativedelta import relativedelta
from requests_html import HTMLSession

server_url = "https://mtbios-sign.mthreads.com"
refresh_token_interval = 60 * 3
session = HTMLSession()


# pip install requests_html -i https://mirrors.aliyun.com/pypi/simple/
# pip install lxml -i https://mirrors.aliyun.com/pypi/simple/
# pip install lxml_html_clean -i https://mirrors.aliyun.com/pypi/simple/
# pip install python-dateutil -i https://mirrors.aliyun.com/pypi/simple/


def usage():
    print(
        "Usage: python ./%s -u <EMAIL> -p 123456789 -d SDM -f ./flash_chip_secure_dr_20240815.wic -o ./ -c ./cookie.txt -m s"
        % os.path.basename(sys.argv[0])
    )
    print("""Command options：
-u User email address [required]
-p Email login password [required]
-d Department name, default is SDM [optional]
-f Signature file [required]
-o Signature file output directory [required]
-c Files that store/read cookie information [optional]
-m s: Signature (default), t: Automatically refresh tokens [optional]
    """)


def save_file(full_path: str, content):
    if full_path.__contains__("/"):
        _dir = full_path[: full_path.rfind("/")]
        if _dir != "" and _dir != "./" and _dir != "." and not os.path.exists(_dir):
            os.makedirs(_dir)
    if isinstance(content, str):
        with open(full_path, "w", encoding="utf-8") as file:
            file.write(content)
    else:
        with open(full_path, "wb") as file:
            file.write(content)


def read_file(_file_path, encoding="utf-8"):
    if not os.path.exists(_file_path):
        return ""
    f = open(_file_path, encoding=encoding)
    _content = f.read()
    f.close()
    return _content


def to_json_str(data, pretty=False):
    if not data:
        return ""
    if pretty:
        return json.dumps(data, ensure_ascii=False, separators=(",", ":"), indent=4)
    return json.dumps(data, ensure_ascii=False, separators=(",", ":"))


def to_json(json_str):
    if not json_str:
        return None
    try:
        return json.loads(json_str)
    except ValueError as e:
        print(e)
        return None


def get_validity_date():
    now = datetime.now()
    new_time = now + relativedelta(months=6)
    return new_time.strftime("%a, %d %b %Y 23:59:59 GMT")


def build_multipart_data(_boundary, _form_data, _file_path):
    lines = []

    mime_type, _ = mimetypes.guess_type(_file_path)
    if not mime_type:
        mime_type = "application/octet-stream"

    for name, value in _form_data.items():
        lines.append(f"--{_boundary}")
        lines.append(f'Content-Disposition: form-data; name="{name}"')
        lines.append("")
        lines.append(value)

    file_name = _file_path.split("/")[-1]
    lines.append(f"--{_boundary}")
    lines.append(f'Content-Disposition: form-data; name="file"; filename="{file_name}"')
    lines.append(f"Content-Type: {mime_type}")
    lines.append("")
    with open(_file_path, "rb") as f:
        file_content = f.read()
    lines.append(file_content.decode("ISO-8859-1"))

    lines.append(f"--{_boundary}--")
    lines.append("")

    body = "\r\n".join(lines)
    return body


def load_cookies(_cookie_file):
    cookie_contents = to_json((read_file(_cookie_file)))
    if not cookie_contents:
        raise Exception(f"Cookie file {_cookie_file} is empty or invalid")
    for cookie_name in cookie_contents:
        cookie = cookie_contents[cookie_name]
        session.cookies.set(
            name=cookie["name"],
            value=cookie["value"],
            domain=cookie["domain"],
            expires=cookie["expires"],
            path=cookie["path"],
            rfc2109=cookie["rfc2109"],
            secure=cookie["secure"],
        )


def save_cookies(_cookie_file, _cookies):
    if not cookie_file:
        return
    cookie_contents = {}

    for cookie in _cookies:
        cookie_contents[cookie.name] = {
            "name": cookie.name,
            "value": cookie.value,
            "domain": cookie.domain,
            "expires": cookie.expires,
            "path": cookie.path,
            "rfc2109": cookie.rfc2109,
            "secure": cookie.secure,
        }
    save_file(_cookie_file, to_json_str(cookie_contents))


def refresh_token():
    session_url = f"{server_url}/api/auth/session"
    session.get(session_url)
    # print(html_response.text)

    cookie_contents = {}
    if os.path.exists(cookie_file):
        cookie_contents = to_json((read_file(cookie_file)))
    if not cookie_contents:
        cookie_contents = {}
    cookies = session.cookies
    has_login = False
    for cookie in cookies:
        if cookie.name == "__Secure-next-auth.session-token":
            has_login = True
        cookie_contents[cookie.name] = {
            "name": cookie.name,
            "value": cookie.value,
            "domain": cookie.domain,
            "expires": cookie.expires,
            "path": cookie.path,
            "rfc2109": cookie.rfc2109,
            "secure": cookie.secure,
        }

    if has_login:
        save_file(cookie_file, to_json_str(cookie_contents))
        print(f"Refresh token successful, saved cookie {cookie_file}")
    else:
        login(username, password)


def login(_username, _password):
    # clear cookie
    session.cookies.clear()

    call_back_url = urllib.parse.quote(f"{server_url}/")
    login_url = f"{server_url}/api/auth/signin?callbackUrl={call_back_url}"
    html_response = session.get(login_url)
    login_form = html_response.html.find("form", first=True)
    inputs = login_form.find("input")
    click_param = {}
    for input_field in inputs:
        click_param[input_field.attrs["name"]] = input_field.attrs["value"]

    html_response = session.post(
        login_form.attrs["action"], data=click_param, allow_redirects=True
    )
    login_form = html_response.html.find("form", first=True)
    click_param = {"username": _username, "password": _password}
    session.post(login_form.attrs["action"], data=click_param, allow_redirects=True)
    cookies = session.cookies

    has_login = False
    for cookie in cookies:
        if cookie.name == "__Secure-next-auth.session-token":
            has_login = True
            save_cookies(cookie_file, cookies)
            break
    if not has_login:
        raise Exception("Login failed: Please check your username and password")

    print(f"Login successful, saved cookie {cookie_file}")


def sign_test_file(_file_path, _sign_file_dir, _email, _department):
    boundary = "----WebKitFormBoundaryYeozW9rm38UBtU2o"
    sign_url = f"{server_url}/api/filedata/file"
    form_data = {
        "uid": f"rc-upload-{time.time_ns()}",
        "department": _department,
        "purpose": "test",
        "email": _email,
        "validityDate": get_validity_date(),
    }
    headers = {
        "Referer": server_url,
        "Origin": server_url,
        "Content-Type": f"multipart/form-data; boundary={boundary}",
    }
    multipart_data = build_multipart_data(boundary, form_data, _file_path)
    html_response = session.post(
        sign_url, headers=headers, data=multipart_data.encode("ISO-8859-1")
    )
    sign_res = to_json(html_response.text)
    if not sign_res or "status" not in sign_res or sign_res["status"] != "ok":
        raise Exception(f"sign file failed: {html_response.text}")
    sign_file_name = sign_res["name"]

    download_url = f"{server_url}/api/filedata/file/{sign_file_name}"
    file_response = session.get(
        download_url, headers={"Referer": server_url, "Origin": server_url}
    )
    if file_response.status_code == 200:
        file_content = file_response.content

        if sign_file_dir.endswith("/"):
            sign_file_full_path = f"{_sign_file_dir}{sign_file_name}"
        else:
            sign_file_full_path = f"{_sign_file_dir}/{sign_file_name}"

        save_file(sign_file_full_path, file_content)
        print(f"Signature file downloaded successfully: {sign_file_full_path}")
    else:
        print(f"Signature file download failed: {file_response.status_code}")


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(args=sys.argv[1:], shortopts="u:p:d:f:o:c:m:")
    except getopt.GetoptError as e:
        print(e)
        usage()
        sys.exit(2)

    username = ""
    password = ""
    department = "SDM"
    purpose = "test"
    file_path = ""
    sign_file_dir = ""
    cookie_file = ""

    # s: Signature (default), t: Automatically refresh tokens
    mode = ""

    for o, a in opts:
        if o == "-u":
            username = a
        elif o == "-p":
            password = a
        elif o == "-d":
            if a:
                department = a
        elif o == "-f":
            file_path = a
        elif o == "-o":
            sign_file_dir = a
        elif o == "-c":
            cookie_file = a
        elif o == "-m":
            mode = a
    if not mode:
        mode = "s"

    if mode == "t":
        if username:
            if password == "":
                password = getpass.getpass(
                    f"Please enter the user {username} password:"
                )
            login(username, password)

            while True:
                time.sleep(refresh_token_interval)
                refresh_token()
            os._exit(0)
    else:
        if os.path.exists(cookie_file):
            load_cookies(cookie_file)
        if not os.path.exists(file_path):
            usage()
            raise FileNotFoundError(f"File not found: {file_path}")
        if not os.path.exists(sign_file_dir):
            usage()
            raise FileNotFoundError(f"Directory not found: {sign_file_dir}")
        retry = True
        while retry:
            try:
                sign_test_file(file_path, sign_file_dir, username, department)
                retry = False
            except Exception as e:
                print(e)
                if "You must be logged in." in str(e):
                    if username != "":
                        password = getpass.getpass(
                            f"Please enter the user {username} password:"
                        )
                        login(username, password)
                        continue
                os._exit(1)
