# -*- coding: utf-8 -*-
# -----------------------------------------------------------------------------
# Filename: influxdb.py
# Original Author: <EMAIL>
# Creation Date: Unknown
# Description : compute 各个数学库代码覆盖率数据上传时序数据库 influxdb
#
# Modifier: <EMAIL> <EMAIL>
# Modification Date: 2024.08.07 2025.08.20
# Modifications: Upload mtcc code coverage to the time-series database
# -----------------------------------------------------------------------------

import argparse
import re
from datetime import datetime
from pathlib import Path

import influxdb_client
from influxdb_client.client.write_api import SYNCHRONOUS

################ compute qa influxdb ################
# g_influxdb_url = "http://************:58086"
g_influxdb_url = "http://swqa-influxdb.mthreads.com"
g_influxdb_token = "EQd3XoyipeVnZeqEyGEu"
g_influxdb_org = "SWQA"
g_influxdb_bucket = "coverage"
g_influxdb_measurement = "mtcc"


def getCoveragedata(file):
    matches = re.findall(r"headerCovTableEntry.*>([0-9.]+)", Path(file).read_text())
    keys = [
        f"{item}_{suffix}"
        for item in ["line", "function", "branch"]
        for suffix in ["covered", "total", "coverage"]
    ]
    converters = [int, int, float] * 3
    data = {k: c(m) for k, c, m in zip(keys, converters, matches)}

    print("data: ", data)
    return data


def writetoInfluxDB(product, data, url="", token="", org="", bucket="", measurement=""):
    # @description :
    # ---------
    # @param :product:具体数学库测试仓库名称,如muBLAS_cts
    #         data:代码覆盖率结果，字典
    #         url, token, org:influxDB鉴权用
    #         bucket(桶):数据库表名称,数据的存储容器或命名空间
    #         _measurement(测量):按照类型或类别组织的数据集
    #         _field:代表数据点中的字段名称
    #         _value:代表数据点中的字段值
    #         _time:代表数据点的时间戳。
    # -------
    # @Returns :
    # -------

    # 创建客户端
    client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)
    # 使用写API准备要写入数据库的数据
    write_api = client.write_api(write_options=SYNCHRONOUS)

    time_now = datetime.utcnow()
    print("time_now: ", time_now)
    point = {
        "measurement": measurement,
        "time": time_now,
        "tags": {"product": product},  # 添加标签值
        "fields": data,
    }
    write_api.write(bucket=bucket, record=point)
    print("Coverage data successfully written to InfluxDB")


def deletefromInfluxDB(url="", token="", org="", bucket="", measurement=""):
    client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)
    start = "2023-10-24T00:00:00Z"
    # stop = "2023-01-05T23:00:00Z"
    stop = datetime.now()
    client.delete_api().delete(
        start, stop, "_measurement={}".format(measurement), bucket=bucket, org=org
    )
    print(f"Measurement '{measurement}' deleted successfully")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--indexfile",
        help="set indexfile path which exist code coverage",
        type=str,
        default="",
    )
    parser.add_argument(
        "--product", help="set product name, such as mtcc", type=str, default=""
    )
    args = parser.parse_args()

    data = getCoveragedata(file=args.indexfile)

    # deletefromInfluxDB(
    #     url=g_influxdb_url,
    #     token=g_influxdb_token,
    #     org=g_influxdb_org,
    #     bucket=g_influxdb_bucket,
    #     measurement=g_influxdb_measurement)

    writetoInfluxDB(
        product=args.product,
        data=data,
        url=g_influxdb_url,
        token=g_influxdb_token,
        org=g_influxdb_org,
        bucket=g_influxdb_bucket,
        measurement=g_influxdb_measurement,
    )

    # Run command: python mtcc_coverage_writetoInfluxDB.py --indexfile coverage_report/index.html --product mtcc
