"""
<AUTHOR> <EMAIL>
-------
@description : compute 各个数学库代码覆盖率数据上传时序数据库 influxdb
---------
"""

import re
import argparse
from datetime import datetime

import influxdb_client
from influxdb_client.client.write_api import SYNCHRONOUS

################ compute qa influxdb ################
# g_influxdb_url = "http://************:58086"
g_influxdb_url = "http://swqa-influxdb.mthreads.com"
g_influxdb_token = "EQd3XoyipeVnZeqEyGEu"
g_influxdb_org = "SWQA"
g_influxdb_bucket = "coverage"
g_influxdb_measurement = "mathX"


def getCoveragedata(file):
    """
    从LCOV生成的html报告中提取代码覆盖率数据

    Args:
        file (str): html文件路径

    Returns:
        data (dict): 包含各项覆盖率指标的字典
    """
    data = {}
    with open(file, "r") as f:
        html_content = f.read()

        # 提取行覆盖率信息 (Lines)
        lines_match = re.search(
            r'<td class="headerItem">Lines:</td>\s*<td class="headerCovTableEntry">(\d+)</td>\s*<td class="headerCovTableEntry">(\d+)</td>\s*<td class="headerCovTableEntry[^>]*">([0-9.]+)',
            html_content,
        )
        if lines_match:
            data["line_covered"] = int(lines_match.group(1))
            data["line_total"] = int(lines_match.group(2))
            data["line_coverage"] = float(lines_match.group(3))

        # 提取函数覆盖率信息 (Functions)
        functions_match = re.search(
            r'<td class="headerItem">Functions:</td>\s*<td class="headerCovTableEntry">(\d+)</td>\s*<td class="headerCovTableEntry">(\d+)</td>\s*<td class="headerCovTableEntry[^>]*">([0-9.]+)',
            html_content,
        )
        if functions_match:
            data["function_covered"] = int(functions_match.group(1))
            data["function_total"] = int(functions_match.group(2))
            data["function_coverage"] = float(functions_match.group(3))

        # 提取分支覆盖率信息 (Branches)
        branches_match = re.search(
            r'<td class="headerItem">Branches:</td>\s*<td class="headerCovTableEntry">(\d+)</td>\s*<td class="headerCovTableEntry">(\d+)</td>\s*<td class="headerCovTableEntry[^>]*">([0-9.]+)',
            html_content,
        )
        if branches_match:
            data["branch_covered"] = int(branches_match.group(1))
            data["branch_total"] = int(branches_match.group(2))
            data["branch_coverage"] = float(branches_match.group(3))

        # 检查是否所有数据都成功提取
        required_keys = [
            "line_covered",
            "line_total",
            "line_coverage",
            "function_covered",
            "function_total",
            "function_coverage",
        ]

        missing_keys = [key for key in required_keys if key not in data]
        if missing_keys:
            raise ValueError(f"未能从html文件中提取以下数据: {missing_keys}")

        print("data: ", data)
    return data


def writetoInfluxDB(product, data, url="", token="", org="", bucket="", measurement=""):
    # @description :
    # ---------
    # @param :product:具体数学库名称,如muBLAS
    #         data:代码覆盖率结果，字典
    #         url, token, org:influxDB鉴权用
    #         bucket(桶):数据库表名称,数据的存储容器或命名空间
    #         _measurement(测量):按照类型或类别组织的数据集
    #         _field:代表数据点中的字段名称
    #         _value:代表数据点中的字段值
    #         _time:代表数据点的时间戳。
    # -------
    # @Returns :
    # -------

    # 创建客户端
    client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)
    # 使用写API准备要写入数据库的数据
    write_api = client.write_api(write_options=SYNCHRONOUS)

    time_now = datetime.utcnow()
    print("time_now: ", time_now)
    point = {
        "measurement": measurement,
        "time": time_now,
        "tags": {"product": product},  # 添加标签值
        "fields": data,
    }
    write_api.write(bucket=bucket, record=point)
    print("Coverage data successfully written to InfluxDB")


def deletefromInfluxDB(url="", token="", org="", bucket="", measurement=""):
    client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)
    start = "2023-10-24T00:00:00Z"
    # stop = "2023-01-05T23:00:00Z"
    stop = datetime.now()
    client.delete_api().delete(
        start, stop, "_measurement={}".format(measurement), bucket=bucket, org=org
    )
    print(f"Measurement '{measurement}' deleted successfully")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--indexfile",
        help="set indexfile path which exist code coverage",
        type=str,
        default="",
    )
    parser.add_argument(
        "--product", help="set product name, such as muBLAS", type=str, default=""
    )
    parser.add_argument(
        "--bucket", help="set influxdb bucket name", type=str, default=g_influxdb_bucket
    )
    parser.add_argument(
        "--measurement",
        help="set influxdb measurement name",
        type=str,
        default=g_influxdb_measurement,
    )
    args = parser.parse_args()

    data = getCoveragedata(file=args.indexfile)

    # deletefromInfluxDB(
    #     url=g_influxdb_url,
    #     token=g_influxdb_token,
    #     org=g_influxdb_org,
    #     bucket=args.bucket,
    #     measurement=args.measurement)

    writetoInfluxDB(
        product=args.product,
        data=data,
        url=g_influxdb_url,
        token=g_influxdb_token,
        org=g_influxdb_org,
        bucket=args.bucket,
        measurement=args.measurement,
    )

    # 运行命令参考: python mathx_coverage_writetoInfluxDB.py --indexfile /home/<USER>/xiawei/tmp/muthrust_llvm_coverage_report/index.html --product muBLAS
