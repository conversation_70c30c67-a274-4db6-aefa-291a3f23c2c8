builds:
  - job: "build.FFmpeg"
    name: "jenkins/linux-build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v60"
      cmd: "mkdir build && ./ffmpeg_docker_build.sh"
      distPath: "mt_video/mt_video_dist"
      packageNames: "ffmpeg_arm64.deb\nffmpeg_x86_64.deb\nffmpeg_loongarch64.deb"
tests:
  - job: "test.FFmpeg"
    name: "jenkins/ffmpeg_test"
    parameters:
      nodeLabel: "S50_video_test"
      ddkBranch: "master"
      packageName: "ddk2.0.deb"
      ffmpegPackageName: "ffmpeg_x86_64.deb"
      vaapiBranch: "scj_dev"
      videosPackageUrl: "https://sh-moss.mthreads.com/sw-build/media/depend/videos.tar.gz"
