dockerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v56"
builds:
  - job: "build.linux_vdi"
    buildAfterMerged: true
    name: "jenkins/guest_gr-umd"
    parameters:
      targetName: "guest_gr-umd"
      exports: ""
      cmd: "./umd_build.sh -b release -w xorg -p mtgpu_linux -d 0 -r 1.0.0.0 -o hw -v 1 -g -j8"
      binPath: "guest_gr-umd/dist/"
      packageName: "x86_64-mtgpu_linux-xorg-release-umd.tar.gz"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd/${mrBucket}/"
      dockerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v22"
  - job: "build.linux_vdi_pack"
    buildAfterMerged: true
    async: true
    name: "jenkins/guest pack"
    parameters:
      packRepoBranch: "pack_rpm_by_url"
      artifactConfig: >
        {
          "kmd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_gr-kmd/", "commit": "${latestKmdCommit}", "packageName": "mtgpu-1.0.0.amd64.deb"},
          "umd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd/${mrBucket}/", "commit": "${latestUmdCommit}", "packageName": "x86_64-mtgpu_linux-xorg-release-umd.tar.gz"},
          "video": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_mt-media-driver/", "commit": "${latestMediaCommit}", "packageName": "video_release_vgpu.tar.gz"},
          "directstream": {"prefix": "https://sh-moss.mthreads.com/sw-build/DirectStream/${branch}/linux/", "commit": "", "packageName": "directstream.tar.gz"},
          "mtml": {"prefix": "https://sh-moss.mthreads.com/sw-build/mtml/develop/", "pkgFilter": "*_mtml_*.*.*-linux-D_amd64.deb"}
        }
      exports: >
        {
          "glvnd_enable": "Y",
          "without_xorg": "Y"
        }
      packCmd: "./build_package.sh -t uos -v ${date}-vGPU-uos -p DEB -s false -b false -a x86_64"
      packageName: "musa_vGPU_amd64.deb"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/drivers/${mrBucket}/"
  - job: "build.linux_vdi_pack"
    buildAfterMerged: true
    async: true
    name: "jenkins/host pack"
    parameters:
      packRepoBranch: "pack_rpm_by_url"
      artifactConfig: >
        {
          "kmd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/host_gr-kmd/", "commit": "${latestKmdCommit}", "packageName": "mtgpu.ko"},
          "umd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd/${mrBucket}/", "commit": "${latestUmdCommit}", "packageName": "x86_64-mtgpu_linux-xorg-release-umd.tar.gz"},
          "video": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_mt-media-driver/", "commit": "${latestMediaCommit}", "packageName": "video_release_vgpu.tar.gz"}
        }
      exports: ""
      packCmd: ""
      packageName: "xc-kmd_mtgpu_amd64_5.4.0-42-generic_release.tar.gz"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/drivers/${mrBucket}/"
tests:
  - job: "test.linux_vdi"
    name: "jenkins/linux vdi glmark2 test"
    parameters:
      nodeLabel: "Linux_VDI"
      insmodOptions: "mtgpu_load_windows_firmware=0 mtgpu_drm_major=1"
      vdiHostPackageName: "xc-kmd_mtgpu_amd64_5.4.0-42-generic_release.tar.gz"
      vdiGuestPackageName: "musa_vGPU_amd64.deb"
