builds:
  - job: "build.MUSA-Runtime"
    name: "compile MUSA-Runtime"
    parameters:
      pkgName: "MUSA_Runtime"
  - job: "build.MUSA-Runtime"
    name: "compile MUSA-Runtime_ASAN"
    parameters:
      compileArgs: "-DENABLE_ASAN_CHECK=ON"
      pkgName: "MUSA_Runtime_ASAN"
  # - job: "build.MUSA-Runtime"
  #   name: "compile MUSA-Runtime_debug_pdump_log"
  #   parameters:
  #     compileArgs: "-DDDK_BUILD_DEBUG=ON -DDDK_PDUMP_ENABLE=ON -DMUSA_BUILD_DEBUG=ON -DCOMPILE_MUSA_LOG=ON"
  #     pkgName: "MUSA_Runtime_debug_pdump_log"
  #     umdPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/ddk/stable/umd_x86-mtgpu_linux-xorg-debug-pdump_on.tar"
tests:
  # - job: "test.musa-runtime_pdump"
  #   name: "musa-runtime pdump test"
  #   parameters:
  #     umdPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/ddk/stable/umd_x86-mtgpu_linux-xorg-debug-pdump_on.tar"
  #     pkgName: "MUSA_Runtime_debug_pdump_log.tar.gz"
  - job: "test.musa_cts"
    name: "musa cts xorg test"
    parameters:
      pkgName: "MUSA_Runtime_ASAN.tar.gz"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=MULTI_CARD=n;"
      testMark: "musa_xorg"
  - job: "test.musa_cts"
    name: "musa cts hip test"
    parameters:
      pkgName: "MUSA_Runtime_ASAN.tar.gz"
      testMark: "musa_hip_test"
  - job: "test.musa_cts"
    name: "musa cts mtcc test"
    parameters:
      pkgName: "MUSA_Runtime_ASAN.tar.gz"
      testMark: "musa_mtcc"
  - job: "test.musa_cts"
    name: "musa cts multiDev test"
    parameters:
      pkgName: "MUSA_Runtime_ASAN.tar.gz"
      testMark: "musa_multiDev"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=MULTI_CARD=y;In=HOST_TYPE=server"
      podResources: "requests=cpu=6;requests=memory=20Gi;limits=cpu=10;limits=memory=32Gi;limits=mthreads.com/gpu=2;"
  - job: "test.musa_cts"
    name: "musa cts release test"
    parameters:
      pkgName: "MUSA_Runtime.tar.gz"
      testMark: "not musa_mtcc_lit and not musa_xorg and not musa_multiDev and not musa_hip_test and not musa_third_party and not musa_mtcc"
      isASAN: 'false'
  - job: "test.musa_cts"
    name: "musa cts release ASAN test"
    parameters:
      pkgName: "MUSA_Runtime_ASAN.tar.gz"
      testMark: "not musa_mtcc_lit and not musa_xorg and not musa_multiDev and not musa_hip_test and not musa_third_party and not musa_cuda_samples and not musa_mtcc"
  - job: "test.mtcc"
    name: "mtcc test"
    parameters:
      pkgName: "MUSA_Runtime.tar.gz"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_CHIP=qy2"
      testArgs: "--device=quyuan2"
      allurePackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/tools/allure-2.19.0.tar"
