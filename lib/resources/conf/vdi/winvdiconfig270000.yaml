pkg_version: "2.7.0-004"
mtapi_url: "https://sh-moss.mthreads.com/sw-build/mtapi/PR/139645/MTAPI_PR139645_B28.zip"
mtml_url: "https://sh-moss.mthreads.com/sw-build/management/windows/release_2.0/5ff78bb57_mt-management_2.0.0_windows.tar.gz"

vdi_wddm:
  product_paths:
    x64:
      common:
        release: "wddm/build/wddm/mtgpu/output/mt_kmd/binary/fre_win10_amd64"
        debug: "wddm/build/wddm/mtgpu/output/mt_kmd/binary/chk_win10_amd64"
      nohw:
        release: "wddm/build/wddm/nohw/output/mt_kmd/binary/fre_win10_amd64_pd"
      mtdxum:
        release: "wddm/mtdxum/cmake_build/Release"
      dxc:
        release: "wddm/dxc/build/Release"
      ogl:
        release: "wddm/ogl/build_x64/Release"
      directstream:
        release: "DirectStream/build/MTEncodeBuild/x64/Release"
      mtcc:
        release: "wddm/libgfxc/Win/musa_compiler64_shared/bin"
        release_branch: "wddm/libgfxc/Win_Release/musa_compiler64_shared/bin"
        ph1_release: "wddm/libgfxc/Win_PH1/musa_compiler64_shared/bin"
        ph1_release_branch: "wddm/libgfxc/Win_PH1_Release/musa_compiler64_shared/bin"
        hg_release: "wddm/libgfxc/Win_HG/musa_compiler64_shared/bin"
      Vulkan:
        release: "wddm/Vulkan/cmake_build/Release"
    Win32:
      common:
        release: "wddm/build/wddm/mtgpu/output/mt_kmd/binary/fre_win10_x86"
        debug: "wddm/build/wddm/mtgpu/output/mt_kmd/binary/chk_win10_x86"
      nohw:
        release: "wddm/build/wddm/nohw/output/mt_kmd/binary/fre_win10_x86_pd"
      mtdxum:
        release: "wddm/mtdxum/cmake_build32/Release"
      dxc:
        release: "wddm/dxc/build32/Release"
      ogl:
        release: "wddm/ogl/build_x86/Release"
      directstream:
        release: "DirectStream/build/MTEncodeBuild/Release"
      mtcc:
        release: "wddm/libgfxc/Win/musa_compiler32_shared/bin"
        release_branch: "wddm/libgfxc/Win_Release/musa_compiler32_shared/bin"
        ph1_release: "wddm/libgfxc/Win_PH1/musa_compiler32_shared/bin"
        ph1_release_branch: "wddm/libgfxc/Win_PH1_Release/musa_compiler32_shared/bin"
        hg_release: "wddm/libgfxc/Win_HG/musa_compiler32_shared/bin"

  # Module definitions with associated products
  modules: &modules
    x64:
      - name: mtdispkm
        products: ["mtdispkm64.sys", "mtdispkm64.pdb"]
      - name: mtvpukm
        products: ["mtvpukm64.sys", "mtvpukm64.pdb"]
      - name: mtkm
        products: ["mtkm64.sys", "mtkm64.pdb"]
      - name: mtdxum
        products: ["mtdxum64.dll", "mtdxum64.pdb"]
      - name: mtdxvaum
        products: ["mtdxvaum64.dll", "mtdxvaum64.pdb"]
      - name: mtvppum
        products: ["mtvppum64.dll", "mtvppum64.pdb"]
      - name: dxc
        products: ["mtdxc64.dll", "mtdh64.dll", "mtdxc64.pdb", "mtdh64.pdb"]
      - name: ogl
        products:
          ["mticdg64.dll", "mticdfbg64.dll", "mticdpxg64.dll", "mticdg64.pdb"]
      - name: pdump
        products: ["pdump.exe"]
      - name: directstream
        products: ["mtencodeapi64.dll", "mtencodeapi64.pdb"]
      - name: mtcc
        products: ["mtdxconv64.dll", "mtgfxc64.dll"]
    Win32:
      - name: mtdxum
        products: ["mtdxum32.dll", "mtdxum32.pdb"]
      - name: mtvppum
        products: ["mtvppum32.dll", "mtvppum32.pdb"]
      - name: mtdxvaum
        products: ["mtdxvaum32.dll", "mtdxvaum32.pdb"]
      - name: dxc
        products: ["mtdxc32.dll", "mtdh32.dll", "mtdxc32.pdb", "mtdh32.pdb"]
      - name: ogl
        products:
          ["mticdg32.dll", "mticdfbg32.dll", "mticdpxg32.dll", "mticdg32.pdb"]
      - name: directstream
        products: ["mtencodeapi32.dll", "mtencodeapi32.pdb"]
      - name: mtcc
        products: ["mtdxconv32.dll", "mtgfxc32.dll"]

  builds:
    hw:
      - pkgNames:
          - "wddm_vdi_release"
        buildModule: *modules
        buildScript: "vs_mtgpu_build_hw.bat"
        infFiles:
          - "MT-VGPU-FW-ENCODE-REL"

vdi_kmd:
  builds:
    common_cmd: >
      ./kmd_build.sh -b release -w nulldrmws -p mtgpu_linux -d 0 -r *******
      -v 1 -f 1 -o hw -s 0 -x ${pkg_version} SUPPORT_LOAD_WINDOWS_FIRMWARE=1 VGPU_COMPAT_CHECK_MODE_VERSION_LIST=1 -j32

    platforms:
      Ubuntu:
        - pkgName: "kmd_Ubuntu_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v11"
          cmd: "${common_cmd} VDI_PLATFORM=UBUNTU"

      Ruijie:
        - pkgName: "kmd_Ruijie_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-ruijie:v1.1"
          cmd: "${common_cmd} VDI_PLATFORM=RUIJIE"

      H3C_v101:
        - pkgName: "kmd_H3C_v101_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-h3c:v1.1"
          cmd: "${common_cmd} VDI_PLATFORM=H3C"

      H3C_v102:
        - pkgName: "kmd_H3C_v102_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-h3c:v1.2"
          cmd: "${common_cmd} VDI_PLATFORM=H3C"

      Sangfor:
        - pkgName: "kmd_Sangfor_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-sangfor:v1.2"
          cmd: "${common_cmd} VDI_PLATFORM=SANGFOR"

      ArcherOS:
        - pkgName: "kmd_ArcherOS_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-archeros:v1.1"
          cmd: "${common_cmd} VDI_PLATFORM=ARCHEROS"

      CStack:
        - pkgName: "kmd_CStack_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-cstack:v1.1"
          cmd: "${common_cmd} VDI_PLATFORM=CSTACK"

      CTYun:
        - pkgName: "kmd_CTYun_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-ctyun:v1.1"
          cmd: "${common_cmd} VDI_PLATFORM=CTYUN"

      NewStart_v418:
        - pkgName: "kmd_NewStart_v418_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-newstart:v1.2"
          cmd: "${common_cmd} VDI_PLATFORM=NEWSTART"

      NewStart_v510:
        - pkgName: "kmd_NewStart_v510_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-newstart:v2"
          cmd: "${common_cmd} VDI_PLATFORM=NEWSTART"

      NewStart_v666:
        - pkgName: "kmd_NewStart_v666_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-newstart:v5"
          cmd: "${common_cmd} VDI_PLATFORM=NEWSTART"

      NewStart_v669:
        - pkgName: "kmd_NewStart_v669_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-newstart:v6"
          cmd: "${common_cmd} VDI_PLATFORM=NEWSTART"

      ECloud:
        - pkgName: "kmd_ECloud_release"
          dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-ecloud:v1.3"
          cmd: "${common_cmd} VDI_PLATFORM=ECLOUD"

      rpm:
        - pkgName: "mtgpu-${pkg_version}.amd64"
          dockerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v53"
          cmd: "${common_cmd} -g rpm"

      deb:
        - pkgName: "mtgpu-${pkg_version}.amd64"
          dockerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v53"
          cmd: "${common_cmd} -g deb"
