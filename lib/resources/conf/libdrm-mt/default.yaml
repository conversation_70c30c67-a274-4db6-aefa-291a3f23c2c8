builds:
  - job: "build.linux-ddk"
    name: "jenkins/linux-ddk-build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v6"
      packageName: "ddk2.0"
      cmd: "./ddk_build.sh -r 0 -u 0 -m 0 -o 0"
      # add ddk info when triggered by submodule
      ddkBranch: "master"
      submoduleConfig:  '{"fec-linux":"head", "fec-trusted-firmware-a":"head", "gpu-fw":"head", "gr-kmd":"develop", "mt-media-driver":"head", "mt-video-drv":"head", "shared_include":"head"}'
tests:
  - job: "test.libdrm_ut"
    name: "jenkins/ddk2.0_libdrm_test"
    parameters:
      ddkBranch: "master"
      packageName: "ddk2.0.deb"
      caseListUrl: "https://sh-moss.mthreads.com/sw-build/linux-ddk/${branch}/test/libdrm_mr_test_list.txt"
