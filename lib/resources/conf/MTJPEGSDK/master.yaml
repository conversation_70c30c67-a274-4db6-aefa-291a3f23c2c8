builds:
  - job: "build.mtJPEG2000"
    buildAfterMerged: "true"
    name: "jenkins/linux-build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v16"
      cmd: "./mtjpeg_build.sh; tar czf MTJPEGSDK.tar.gz output"
      mtJpegPackageName: "MTJPEGSDK.tar.gz"
  - job: "build.mtJPEG2000"
    name: "jenkins/clang-tidy"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v16"
      cmd: "./mtjpeg_build.sh -s && ./clang_tidy.sh build_cmake/x86_64/"
tests:
  - job: "test.MTJPEGSDK"
    name: "jenkins/MTJPEGSDK_test"
    parameters:
      nodeLabel: "S90 && Ubuntu22.04 && x86_64"
      ddkBranch: "master"
      mtJpegPackageUrl: "https://sh-moss.mthreads.com/sw-pr/MTJPEGSDK/${branch}/${commitId}/MTJPEGSDK.tar.gz"
