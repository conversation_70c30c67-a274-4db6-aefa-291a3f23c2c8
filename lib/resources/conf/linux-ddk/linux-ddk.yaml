builds:
  - job: "build.linux-ddk"
    name: "linux-ddk build"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v6"
      pkgName: "ddk2.0"
      cmd: "./ddk_build.sh"
tests:
  - job: "test.m3d_cts"
    name: "m3d cts test"
    parameters:
      linuxDdkBranch: ${branch}
      linuxDdkcommitId: ${commitId}
      testLabel: "linux/m3d cts test"
      testMark: "-m 'drm_ut_test or m3d_ut_test or m3d_api_test'"
  - job: "test.m3d_musa_cts"
    name: "m3d musa cts test"
    parameters:
      branch: "m3d_master"
      testType: "smokeM3d"
      testLabel: "linux/m3d musa cts test"
  - job: "test.ogl_cts"
    name: "ogl cts test for gl"
    parameters:
      testLabel: "linux/ogl_cts_Test_gl"
      dependency : 'https://sh-moss.mthreads.com/sw-build/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-gl.tar.gz'
      testConfig: '{"gl": {"workdir": "build-mt-main","binary": "glcts","case": "https://sh-moss.mthreads.com/sw-build/m3d/ogl/master/passlist.txt"},"gl4.2": {"workdir": "build-mt-main","binary": "glcts","case": "https://sh-moss.mthreads.com/sw-build/m3d/ogl/master/passlist-4.2.txt"},"gl4.3": {"workdir": "build-mt-main","binary": "glcts","case": "https://sh-moss.mthreads.com/sw-build/m3d/ogl/master/passlist-4.3.txt"}}'
  - job: "test.ogl_cts"
    name: "ogl cts test for es"
    parameters:
      testLabel: "linux/ogl_cts_Test_es"
      dependency : 'https://sh-moss.mthreads.com/sw-build/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-es.tar.gz'
      testConfig: '{"es": {"workdir": "build-mt-opengl-cts-4.6.0.3","binary": "glcts","case": "https://sh-moss.mthreads.com/sw-build/m3d/ogl/master/passlist-es.txt"},"es4.2": {"workdir": "build-mt-opengl-cts-4.6.0.3","binary": "glcts","case": "https://sh-moss.mthreads.com/sw-build/m3d/ogl/master/passlist-es-4.2.txt"}}'
  - job: "test.vaapi_fits"
    name: "linux/vpu_Test"
    parameters:
      testLabel: "linux/vpu_Test"
      testType : 'daily'
  - job: "test.vaapi_fits"
    name: "linux/vpu_Test"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S90_vpu_Test"
        testType : 's4000'
        nodeLabel: 'S90_Ubuntu22.04_x86_64_101.16'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
  - job: "test.vaapi_fits"
    name: "linux/vpu_Test"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S4000_vpu_Test"
        testType : 's4000'
        nodeLabel: 'S4000_video_test'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
  - job: "test.vaapi_fits"
    name: "linux/vpu_Test"
    parameters:
        branch: "scj_dev"
        testLabel: "linux/S50_vpu_Test"
        testType : 'daily'
        nodeLabel: 'S50_Ubuntu22.04_x86_64_101.62'
        containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-video:v7"
