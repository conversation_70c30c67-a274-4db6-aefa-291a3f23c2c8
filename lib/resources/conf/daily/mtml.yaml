tests:
  - job: "CI_MTML_TEST"
    name: "mtml_qy2_test"
    parameters:
      testLabel: "mtml_qy2_test"
      mtmlBranch: "develop"
      gpuType: "S4000"
      testMtml: "true"
      testGmi: "false"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v11"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s4000;"
      cluster: "dailyFarm"
      runChoice: "pod"
      exclude_cases: "DeviceGetPciInfoTest.baseGetPci,VpuGetDecoderSessionMetricsTest.getSessionMetricsWithFfmpeg,
                      VpuGetUtilizationTest.withFfmpegDecoding,DeviceGetPowerUsageTest.*,MemoryGetVendorTest.*,
                      GpuGetMaxClockTest.baseGetMaxClock,DeviceGetSerialNumberTest*,DeviceResetTest*,DeviceGetNameTest.validData,
                      DeviceInitByPciSbdfTest*,VpuGetCodecCapacityTest.baseValidGetCodecCapcity,VpuGetDecoderSessionStatesTest.baseValidGetDecoderSessionStates"
  # - job: "CI_MTML_TEST"
  #   name: "mtml_ph1_test"
  #   parameters:
  #     testLabel: "mtml_ph1_test"
  #     mtmlBranch: "develop"
  #     gpuType: "S5000"
  #     testMtml: "true"
  #     testGmi: "false"
  #     containerImage: "sh-harbor.mthreads.com/sdk/management:v11"
  #     podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
  #     podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
  #     cluster: "dailyFarm"
  #     runChoice: "pod"
  #     exclude_cases: "DeviceGetPciInfoTest.baseGetPci,VpuGetDecoderSessionMetricsTest.getSessionMetricsWithFfmpeg,
  #                     VpuGetUtilizationTest.withFfmpegDecoding,DeviceGetPowerUsageTest.*,MemoryGetVendorTest.*,
  #                     GpuGetMaxClockTest.baseGetMaxClock,DeviceGetSerialNumberTest*,DeviceResetTest*,DeviceGetNameTest.validData,
  #                     DeviceInitByPciSbdfTest*"
  - job: "CI_MTML_TEST"
    name: "mtml_qy1_test"
    parameters:
      runChoice: "node"
      nodeLabel: "192.168.109.54"
      testLabel: "mtml_qy1_test"
      mtmlBranch: "develop"
      gpuType: "S80"
      testMtml: "true"
      testGmi: "false"
      exclude_cases: "DeviceGetPciInfoTest.baseGetPci,VpuGetDecoderSessionMetricsTest.getSessionMetricsWithFfmpeg,
                      VpuGetUtilizationTest.withFfmpegDecoding,DeviceGetPowerUsageTest.*,MemoryGetVendorTest.*,
                      GpuGetMaxClockTest.baseGetMaxClock,DeviceGetSerialNumberTest*,DeviceResetTest*"

      