tests:
  - job: "CI_msys_cli_test"
    name: "msys_qy2_test"
    parameters:
      testLabel: "msys_qy2_test"
      msysBranch: "develop"
      linuxDdkBranch: "master"
      gpuType: "S4000"
      testType: "compute"
      benchmark: "--compute"
      msys_cases: '-k "not graphics"'
      containerImage: "sh-harbor.mthreads.com/qa/musa_test-ubuntu-22-04:v2"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s4000;"
      cluster: "dailyFarm"
      runChoice: "pod"
      buildId: "${BUILD_ID}"
  # - job: "CI_msys_cli_test"
  #   name: "msys_ph1_test"
  #   parameters:
  #     testLabel: "msys_ph1_test"
  #     msysBranch: "develop"
  #     linuxDdkBranch: "master"
  #     gpuType: "S5000"
  #     testType: "compute"
  #     benchmark: "--compute"
  #     msys_cases: '-k "not graphics"'
  #     containerImage: "sh-harbor.mthreads.com/qa/musa_test-ubuntu-22-04:v2"
  #     podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
  #     podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
  #     cluster: "dailyFarm"
  #     runChoice: "pod"
  #     buildId: "${BUILD_ID}"
  - job: "CI_msys_cli_test_qy1"
    name: "msys_qy1_test"
    parameters:
      runChoice: "node"
      nodeLabel: "192.168.109.54"
      testLabel: "msys_qy1_test"
      msysBranch: "develop"
      linuxDdkBranch: "master"
      gpuType: "S80"
      testType: "all"
      benchmark: ""
      msys_cases: ""
      runChoice: "node"
      buildId: "${BUILD_ID}"

      