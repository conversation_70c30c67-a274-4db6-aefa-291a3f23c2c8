builds:
  - job: "build.linux-ddk"
    name: "linux/build_x64"
    parameters:
      containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v10"
      pkgName: "ddk2.0"
      cmd: "./ddk_build.sh -a 0 -o 1"
      ddkBranch: "master"
      submoduleConfig: '{"m3d": "head", "libdrm-mt": "head", "shared_include": "head"}'
      buildChoice: "tar.gz"
  # - job: "build.linux-ddk"
  #   name: "linux/build_x86"
  #   parameters:
  #     containerImage: "sh-harbor.mthreads.com/qa/linux-ddk:v10"
  #     pkgName: "x86"
  #     cmd: "./ddk_build.sh -a 0 -o 1 -A x86"
  #     ddkBranch: "master"
  #     submoduleConfig: '{"m3d": "head", "libdrm-mt": "head", "shared_include": "head"}'
  #     buildChoice: "tar.gz"
  - job: "upload-case"
    name: "jenkins/upload-ogl-case"
    parameters:
      repo: "ogl"
      branch: "${oglSourceBranch}"
      files: "cts/*"
      uploadPath: "sh-moss/sw-pr/ogl/${gitlabMergeRequestIid}/test/"

  - job: "upload-case"
    name: "jenkins/upload-gfx-case"
    parameters:
      repo: "mt-gfx-test"
      branch: "master"
      commitId: ""
      files: "VK-GL-CTS_caselist/linux/gles/gles_cts_pr_ddk2_m3d.txt"
      uploadPath: "sh-moss/sw-pr/ogl/${gitlabMergeRequestIid}/test/"

  # Windows OGL Hardware build
  - job: "build.ogl.hw"
    name: "win/build_hw_ogl"
    parameters:
      pkgName: wddm_hw
      script: "python genOglBuild.py -g 1 -b 1 -d 1 -r 1 && python genOglBuild.py -g 1 -a 32 -b 1 -d 1 -r 1"
      products: >-
        mticdg64.dll mticdfbg64.dll mticdpxg64.dll mticdg64.pdb
        mticdg32.dll mticdfbg32.dll mticdpxg32.dll mticdg32.pdb
      product_path: >-
        wddm/ogl/BuildNinja64/Release/Release
        wddm/ogl/BuildNinja32/Release/Release
        wddm/ogl/imported/pre-binary-kmd/hw
      dependencyRepo: '{"name": "wddm", "branch": "develop", "skip_submodules": "dxc,ogl,mtdxum,kmd,wddm/gpufw,vulkan"}'
      latest_url: "https://sh-moss.mthreads.com/sw-build/wddm/develop/latest_new.txt"
      needBuild: 'true'

  # Windows OGL MTCC 2.0 build
  - job: "build.ogl.mtcc20"
    name: "win/build_mtcc20_ogl"
    parameters:
      pkgName: wddm_mtcc20
      script: "python genOglBuild.py -g 1 -b 1 -m 1 -d 1 -r 1 && python genOglBuild.py -g 1 -a 32 -b 1 -m 1 -d 1 -r 1"
      products: >-
        mticdg64.dll mticdfbg64.dll mticdpxg64.dll mticdg64.pdb
        mticdg32.dll mticdfbg32.dll mticdpxg32.dll mticdg32.pdb
      product_path: >-
        wddm/ogl/BuildNinja64Mtcc/Release/Release
        wddm/ogl/BuildNinja32Mtcc/Release/Release
        wddm/ogl/imported/pre-binary-kmd/hw
      dependencyRepo: '{"name": "wddm", "branch": "develop", "skip_submodules": "dxc,ogl,mtdxum,kmd,wddm/gpufw,vulkan"}'
      latest_url: "https://sh-moss.mthreads.com/sw-build/wddm/develop/latest_new.txt"
      needBuild: 'true'

  # Windows OGL New API build
  - job: "build.ogl.newapi"
    name: "win/build_newapi_ogl"
    parameters:
      pkgName: wddm_newapi
      script: "python genOglBuild.py -g 1 -i 1 -a 64 -b 1 -d 1 -r 1 && python genOglBuild.py -g 1 -i 1 -a 32 -b 1 -d 1 -r 1"
      products: >-
        mticdg64.dll mticdfbg64.dll mticdpxg64.dll mticdg64.pdb
        mticdg32.dll mticdfbg32.dll mticdpxg32.dll mticdg32.pdb
      product_path: >-
        wddm/ogl/BuildNinja64_NewApi/Release/Release
        wddm/ogl/BuildNinja32_NewApi/Release/Release
        wddm/ogl/imported/pre-binary-kmd/hw
      dependencyRepo: '{"name": "wddm", "branch": "develop", "skip_submodules": "dxc,ogl,mtdxum,kmd,wddm/gpufw,vulkan"}'
      latest_url: "https://sh-moss.mthreads.com/sw-build/wddm/develop/latest_new.txt"
      needBuild: 'true'

  # Windows OGL nohw build
  - job: "build.ogl.nohw"
    name: "win/build_nohw_ogl"
    parameters:
      pkgName: wddm_nohw
      products: >-
        pdump.inf
      product_path: >-
        wddm/ogl/imported/pre-binary-kmd/hw
      dependencyRepo: '{"name": "wddm", "branch": "develop", "skip_submodules": "dxc,ogl,mtdxum,kmd,wddm/gpufw,vulkan"}'
      latest_url: "https://sh-moss.mthreads.com/sw-build/wddm/develop/latest_new.txt"
      needBuild: 'false'

tests:
  - job: "test.ogl_cts"
    name: "linux/gl_cts_mt-main_Test"
    parameters:
      dependency : 'https://sh-moss.mthreads.com/sw-build/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-gl.tar.gz'
      testConfig: '{"gl_cts":     {"workdir": "build-mt-main", "binary": "glcts", "case": "https://sh-moss.mthreads.com/sw-pr/ogl/${gitlabMergeRequestIid}/test/linux/passlist.txt"},
                    "gl_cts_4.3": {"workdir": "build-mt-main", "binary": "glcts", "case": "https://sh-moss.mthreads.com/sw-pr/ogl/${gitlabMergeRequestIid}/test/linux/passlist-4.3.txt"}}'
      ddkBranch: "master"
  - job: "test.ogl_cts"
    name: "linux/gl_cts_mt-4.6.0.3_Test"
    parameters:
      dependency : 'https://sh-moss.mthreads.com/sw-build/computeQA/m3d/m3d_ogl_cts_smoke_ci/vulkan-cts-es.tar.gz'
      testConfig: '{"gl_cts_gtf_Test":       {"workdir": "build-mt-opengl-cts-4.6.0.3", "binary": "glcts", "case": "https://sh-moss.mthreads.com/sw-pr/ogl/${gitlabMergeRequestIid}/test/linux/passlist-gtf.txt"}}'
      # testConfig: '{"gl_cts_mt_ported_Test": {"workdir": "build-mt-opengl-cts-4.6.0.3", "binary": "glcts", "case": "https://sh-moss.mthreads.com/sw-pr/ogl/${gitlabMergeRequestIid}/test/passlist-es.txt"},
      #               "gl_cts_gtf_Test":       {"workdir": "build-mt-opengl-cts-4.6.0.3", "binary": "glcts", "case": "https://sh-moss.mthreads.com/sw-pr/ogl/${gitlabMergeRequestIid}/test/linux/passlist-gtf.txt"}}'
      ddkBranch: "master"
  - job: "test.graphic_cts"
    name: "jenkins/ddk2.0_m3d_gles_cts"
    parameters:
      nodeLabel: "(S80 || X300) && Ubuntu22.04 && x86_64"
      testConfig: '{"gles2":{"workdir":"/root/cts_gles","binary":"glcts","case":"https://sh-moss.mthreads.com/sw-pr/ogl/${gitlabMergeRequestIid}/test/gles_cts_pr_ddk2_m3d.txt"}}'
      exportEnv: "export ENABLE_M3D_GLES=1"
      ddkBranch: "master"
