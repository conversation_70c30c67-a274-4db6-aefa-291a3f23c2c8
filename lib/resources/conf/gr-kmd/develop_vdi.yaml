dockerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v56"
hostKmdBuildexports: &hostKmdBuildEnv >
  {
    "VGPU_FW_1_0": "1",
    "SUPPORT_LOAD_WINDOWS_FIRMWARE": "0",
    "VGPU_COMPAT_CHECK_MODE_VERSION_LIST": "1"
  }
builds:
  - job: "build.linux_vdi"
    buildAfterMerged: true
    name: "jenkins/guest_gr-kmd"
    parameters:
      targetName: "guest_gr-kmd"
      umdDependency: ""
      mediaDependency: ""
      exports: >
        {
          "VGPU_FW_1_0": "1",
          "PVRSRV_APPHINT_DRIVERMODE": "1"
        }
      cmd: "./kmd_build.sh -b release -w xorg -p mtgpu_linux -d 0 -r ******* -o hw -g deb -v 1 -j8"
      binPath: "gr-kmd/binary_*/target_*/"
      packageName: "mtgpu-1.0.0.amd64.deb"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_gr-kmd/${mrBucket}/"
  - job: "build.linux_vdi"
    buildAfterMerged: true
    name: "jenkins/host_gr-kmd"
    parameters:
      targetName: "host_gr-kmd"
      umdDependency: ""
      mediaDependency: ""
      exports: *hostKmdBuildEnv
      cmd: "./kmd_build.sh -b release -w nulldrmws -p mtgpu_linux -d 0 -r ******* -o hw -s 0 -v 1 -j4"
      binPath: "host_gr-kmd/dist/*mtgpu_linux*/lib/modules/*/extra/"
      packageName: "mtgpu.ko"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/repoPackages/host_gr-kmd/${mrBucket}/"
  - job: "build.linux_vdi"
    buildAfterMerged: true
    name: "jenkins/host_gr-kmd_4.19.0"
    parameters:
      targetName: "host_gr-kmd_4.19.0"
      umdDependency: ""
      mediaDependency: ""
      exports: >
        {
          "KERNELVER": "4.19.0-**********.uelc20.x86_64",
          "KERNELDIR": "/root/4.19.0-**********.uelc20.x86_64/",
          "VGPU_FW_1_0": "1",
          "SUPPORT_LOAD_WINDOWS_FIRMWARE": "0",
          "VGPU_COMPAT_CHECK_MODE_VERSION_LIST": "1"
        }
      cmd: "./kmd_build.sh -b release -w nulldrmws -p mtgpu_linux -d 0 -r ******* -o hw -s 0 -v 1 -j4"
      binPath: "host_gr-kmd_4.19.0/dist/*mtgpu_linux*/lib/modules/*/extra/"
      packageName: "mtgpu.ko"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/repoPackages/host_gr-kmd_4.19.0/${mrBucket}/"
  - job: "build.linux_vdi"
    buildAfterMerged: true
    name: "jenkins/host_gr-kmd_bclinux"
    parameters:
      targetName: "host_gr-kmd_bclinux"
      umdDependency: ""
      mediaDependency: ""
      exports: *hostKmdBuildEnv
      cmd: "./kmd_build.sh -b release -w nulldrmws -p mtgpu_linux -d 0 -r ******* -o hw -s 0 -v 1 -j4"
      binPath: "host_gr-kmd_bclinux/dist/*mtgpu_linux*/lib/modules/*/extra/"
      packageName: "mtgpu.ko"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/repoPackages/host_gr-kmd_bclinux/${mrBucket}/"
      dockerImage: "sh-harbor.mthreads.com/build-env/kmd-vdi-ecloud:v1.3"
  - job: "build.linux_vdi_pack"
    buildAfterMerged: true
    async: true
    name: "jenkins/guest pack"
    parameters:
      packRepoBranch: "pack_rpm_by_url"
      artifactConfig: >
        {
          "kmd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_gr-kmd/${mrBucket}/", "commit": "${latestKmdCommit}", "packageName": "mtgpu-1.0.0.amd64.deb"},
          "umd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd/", "commit": "${latestUmdCommit}", "packageName": "x86_64-mtgpu_linux-xorg-release-umd.tar.gz"},
          "video": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_mt-media-driver/", "commit": "${latestMediaCommit}", "packageName": "video_release_vgpu.tar.gz"},
          "directstream": {"prefix": "https://sh-moss.mthreads.com/sw-build/DirectStream/${branch}/linux/", "commit": "", "packageName": "directstream.tar.gz"},
          "umd_path_wine_separate": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd_i386/", "commit": "${latestUmdCommit}", "packageName": "i386-mtgpu_linux-xorg-release-umd.tar.gz"},
          "mtml": {"prefix": "https://sh-moss.mthreads.com/sw-build/mtml/develop/", "pkgFilter": "*_mtml_*.*.*-linux-D_amd64.deb"}
        }
      exports: >
        {
          "glvnd_enable": "Y",
          "without_xorg": "Y"
        }
      packCmd: "./build_package.sh -t uos -v ${date}-vGPU-uos -p DEB -s false -b false -a x86_64"
      packageName: "musa_vGPU_amd64.deb"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/drivers/${mrBucket}/"
  - job: "build.linux_vdi_pack"
    buildAfterMerged: true
    async: true
    name: "jenkins/host pack"
    parameters:
      packRepoBranch: "pack_rpm_by_url"
      artifactConfig: >
        {
          "kmd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/host_gr-kmd/${mrBucket}/", "commit": "${latestKmdCommit}", "packageName": "mtgpu.ko"},
          "umd": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd/", "commit": "${latestUmdCommit}", "packageName": "x86_64-mtgpu_linux-xorg-release-umd.tar.gz"},
          "video": {"prefix": "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${branch}/repoPackages/guest_mt-media-driver/", "commit": "${latestMediaCommit}", "packageName": "video_release_vgpu.tar.gz"}
        }
      exports: ""
      packCmd: ""
      packageName: "xc-kmd_mtgpu_amd64_5.4.0-42-generic_release.tar.gz"
      ossPkgPath: "release-ci/VDI/XC-VDI/${branch}/drivers/${mrBucket}/"
tests:
  - job: "test.linux_vdi"
    name: "jenkins/linux vdi glmark2 test"
    parameters:
      nodeLabel: "Linux_VDI"
      insmodOptions: "mtgpu_load_windows_firmware=0 mtgpu_drm_major=1"
      vdiHostPackageName: "xc-kmd_mtgpu_amd64_5.4.0-42-generic_release.tar.gz"
      vdiGuestPackageName: "musa_vGPU_amd64.deb"
