builds:
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-release-hw"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o hw -j32 -b release -d 0 -g deb"
      exports: ""
      checkKernelHeader: "yes"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw"
      runCppcheck: "no"
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-release-vps"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o vps -j32 -b release -d 0 -g deb"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-release-vps"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-release-hw-pdump"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o hw -j32 -b release -d 1 -g deb"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-release-vps-pdump"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o vps -j32 -b release -d 1 -g deb"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-release-vps-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-debug-hw"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o hw -j32 -b debug -d 0"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-debug-hw"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-debug-vps"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o vps -j32 -b debug -d 0"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-debug-vps"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-debug-hw-pdump"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o hw -j32 -b debug -d 1"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-debug-hw-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-mtgpu_linux-xorg-debug-vps-pdump"
    parameters:
      cmd: "./kmd_build.sh -r 1.0.0.0 -p mtgpu_linux -w xorg -o vps -j32 -b debug -d 1"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-mtgpu_linux-xorg-debug-vps-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "arm64-mtgpu_linux-xorg-release-hw"
    parameters:
      cmd: "./kmd_build.sh -a arm64 -c aarch64-linux-gnu- -r 1.0.0.0 -o hw -j32 -w xorg -p mtgpu_linux -d 0 -b release -g kylin -g deb"
      exports: "KERNELVER=4.19.0-arm64-desktop\nKERNELDIR=/root/linux-headers-4.19.0-arm64-desktop"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v21"
      checkKernelHeader: "yes"
      packageName: "arm64-mtgpu_linux-xorg-release-hw"
      runCppcheck: "no"
  - job: "build.gr-kmd"
    name: "arm64-mtgpu_linux-xorg-release-hw-pdump"
    parameters:
      cmd: "./kmd_build.sh -a arm64 -c aarch64-linux-gnu- -r 1.0.0.0 -o hw -j32 -w xorg -p mtgpu_linux -d 1 -b release -g kylin -g deb"
      exports: "KERNELVER=4.19.0-arm64-desktop\nKERNELDIR=/root/linux-headers-4.19.0-arm64-desktop"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v21"
      packageName: "arm64-mtgpu_linux-xorg-release-hw-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "arm64-mtgpu_linux-xorg-debug-hw"
    parameters:
      cmd: "./kmd_build.sh -a arm64 -c aarch64-linux-gnu- -r 1.0.0.0 -o hw -j32 -w xorg -p mtgpu_linux -d 0 -b debug -g kylin"
      exports: "KERNELVER=4.19.0-arm64-desktop\nKERNELDIR=/root/linux-headers-4.19.0-arm64-desktop"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v21"
      packageName: "arm64-mtgpu_linux-xorg-debug-hw"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "arm64-mtgpu_linux-xorg-debug-hw-pdump"
    parameters:
      cmd: "./kmd_build.sh -a arm64 -c aarch64-linux-gnu- -r 1.0.0.0 -o hw -j32 -w xorg -p mtgpu_linux -d 1 -b debug -g kylin"
      exports: "KERNELVER=4.19.0-arm64-desktop\nKERNELDIR=/root/linux-headers-4.19.0-arm64-desktop"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v21"
      packageName: "arm64-mtgpu_linux-xorg-debug-hw-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "arm64-mtgpu_linux-nulldrmws-debug-hw"
    parameters:
      cmd: "./kmd_build.sh -a arm64 -c aarch64-linux-gnu- -r 1.0.0.0 -o hw -j32 -w nulldrmws -p mtgpu_linux -d 0 -b debug -g kylin"
      exports: "KERNELVER=4.19.0-arm64-desktop\nKERNELDIR=/root/linux-headers-4.19.0-arm64-desktop"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd-uos:v21"
      runCppcheck: "yes"
      packageName: "arm64-mtgpu_linux-nulldrmws-debug-hw"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "loongarch-mtgpu_linux-xorg-release-hw"
    parameters:
      cmd: "./kmd_build.sh -a loongarch -c loongarch64-linux-gnu- -r 1.0.0.0 -o hw -j32 -w xorg -p mtgpu_linux -d 0 -b release"
      exports: "KERNELVER=5.4.18-78-generic\nKERNELDIR=/root/kylin-loongarch-kernel-header-5.4.18-78-generic/linux-headers-5.4.18-78-generic"
      checkKernelHeader: "yes"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "loongarch-mtgpu_linux-xorg-release-hw"
      runCppcheck: "no"
  - job: "build.gr-kmd"
    name: "loongarch-mtgpu_linux-xorg-debug-hw"
    parameters:
      cmd: "./kmd_build.sh -a loongarch -c loongarch64-linux-gnu- -r 1.0.0.0 -o hw -j32 -w xorg -p mtgpu_linux -d 0 -b debug"
      exports: "KERNELVER=5.4.18-78-generic\nKERNELDIR=/root/kylin-loongarch-kernel-header-5.4.18-78-generic/linux-headers-5.4.18-78-generic"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "loongarch-mtgpu_linux-xorg-debug-hw"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-nohw_linux-debug-vps"
    parameters:
      cmd: "./kmd_build.sh -w nulldrmws -p nohw_linux -r 1.0.0.0 -o vps"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-nohw_linux-debug-vps"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-nohw_linux-release-vps-pdump"
    parameters:
      cmd: "./kmd_build.sh -w nulldrmws -p nohw_linux -r 1.0.0.0 -o vps -b release -d 1"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-nohw_linux-release-vps-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "x86_64-nohw_linux-debug-vps-pdump"
    parameters:
      cmd: "./kmd_build.sh -w nulldrmws -p nohw_linux -r 1.0.0.0 -o vps -b debug -d 1"
      exports: ""
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "x86_64-nohw_linux-debug-vps-pdump"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "arm64-mtgpu_android-release-hw"
    parameters:
      cmd: "./kmd_build.sh -p mtgpu_android -w android -a arm64 -c aarch64-linux-gnu- -d 0 -b release -r 1.0.0.0 -s 0 -j8"
      exports: "KERNELVER=5.4.119-1-0010+\nKERNELDIR=/root/acx-linux-headers-5.4.119-1-0010+"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "arm64-mtgpu_android-release-hw"
      runCppcheck: "no"
      checkKernelHeader: "no"
  - job: "build.gr-kmd"
    name: "arm64-mtgpu_android-debug-hw"
    parameters:
      cmd: "./kmd_build.sh -p mtgpu_android -w android -a arm64 -c aarch64-linux-gnu- -d 0 -b debug -r 1.0.0.0 -s 0 -j8"
      exports: "KERNELVER=5.4.119-1-0010+\nKERNELDIR=/root/acx-linux-headers-5.4.119-1-0010+"
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
      packageName: "arm64-mtgpu_android-debug-hw"
      runCppcheck: "no"
      checkKernelHeader: "no"
tests:
  - job: "test.graphic_cts"
    name: "jenkins/gl_cts_test"
    parameters:
      testConfig: '{"gl4.1":{"workdir":"/root/cts_gl","binary":"glcts","case":"https://sh-moss.mthreads.com/sw-build/gr-umd/${gitlabTargetBranch}/test/cts/gl_cts_pr.txt"}}'
  - job: "test.graphic_cts"
    name: "jenkins/gl_cs_cts_test"
    parameters:
      testConfig: '{"gl4.2+":{"workdir":"/root/cts_gl","binary":"glcts","case":"https://sh-moss.mthreads.com/sw-build/gr-umd/${gitlabTargetBranch}/test/cts/gl_cs_cts_pr.txt"}}'
  - job: "test.graphic_cts"
    name: "jenkins/gles_cts_test"
    parameters:
      testConfig: '{"gles":{"workdir":"/root/cts_gles","binary":"glcts","case":"https://sh-moss.mthreads.com/sw-build/gr-umd/${gitlabTargetBranch}/test/cts/gles_cts_pr.txt"}}'
  - job: "test.graphic_cts"
    name: "jenkins/vk_cts_test"
    parameters:
      testConfig: '{"vk":{"workdir":"/root/cts_vk","binary":"deqp-vk","case":"https://sh-moss.mthreads.com/sw-build/gr-umd/${gitlabTargetBranch}/test/cts/vk_cts_pr.txt"}}'
  - job: "test.vps"
    name: "jenkins/quyuan2_vps_test"
    parameters:
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      model: "cmodel"
      chipType: "quyuan2"
  - job: "test.vps"
    name: "jenkins/ph1_vps_test"
    parameters:
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-vps.tar.gz"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-vps-ph1.tar.gz"
      model: "amodel"
      chipType: "ph1"
  - job: "test.renderdoc"
    name: "jenkins/renderdoc_test"
  - job: "test.piglit"
    name: "jenkins/piglit_test"
    parameters:
      openGL41CaseListUrl: "https://sh-moss.mthreads.com/sw-build/gr-umd/${gitlabTargetBranch}/test/cts/piglit_pr.txt"
      openGL42CaseListUrl: "https://sh-moss.mthreads.com/sw-build/gr-umd/${gitlabTargetBranch}/test/cts/piglit_lianying_pr.txt"
  - job: "test.glmark2"
    name: "jenkins/ubuntu_x86_64_glmark2_test"
    parameters:
      nodeLabel: "piglit_test"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-hw-glvnd.tar.gz"
  - job: "test.glmark2"
    name: "jenkins/uos_arm64_glmark2_test"
    parameters:
      kmdPackageName: "arm64-mtgpu_linux-xorg-release-hw.deb"
      umdPackageName: "arm64-mtgpu_linux-xorg-release-hw-glvnd.tar.gz"
  - job: "CI_DDK_video_test_gitlab"
    name: "jenkins/S50_video_test"
    parameters:
      test_nodes: "S50_video_test"
      ddk_video_test: "true"
      branch: "${gitlabTargetBranch}"
  - job: "CI_DDK_video_test_gitlab"
    name: "jenkins/S80_video_test"
    parameters:
      test_nodes: "S80_video_test"
      ddk_video_test: "true"
      branch: "${gitlabTargetBranch}"
  - job: "test.pdump"
    name: "jenkins/pdump_test"
    parameters:
      kmdPackageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump.deb"
      umdPackageName: "x86_64-mtgpu_linux-xorg-release-hw-pdump-glvnd.tar.gz"
  - job: "test.dkms_intree"
    name: "jenkins/dkms_intree_test"
    parameters:
      containerImage: "sh-harbor.mthreads.com/build-env/gr-umd:v45"
  - job: "test.musa_cts"
    name: "jenkins/musa test"
    parameters:
      runChoice: "node"
      testMark: "not musa_xorg"
      testType: "ddksmk"
