import groovy.transform.Field
import groovy.json.JsonSlurper
import java.security.SecureRandom

@Field OSS_CI = 'https://swci-oss.mthreads.com'
@Field OSS_CI_USER = 'mtoss-swci'
@Field OSS_CI_PWD = 'H65KwcY1poayvgqTEReHBJu'
@Field OSS = 'https://oss.mthreads.com'
@Field OSS_USER = 'mtoss-swci'
@Field OSS_PWD = 'Z2Z4LWNp'
@Field MOSS = 'https://sh-moss.mthreads.com'

def verify() {
    try {
        sh 'mc -v'
        return true
    } catch (exc) {
        return false
    }
}

def checkOSSAlias(String host, String alias_name, String username, String password) {
    try {
        String content = utils.runCommandWithStdout("mc alias ls ${alias_name} --json")
        def lines = content.readLines()
        def jsonLine = lines.find { it.startsWith('{') }
        println(jsonLine)
        def jsonSlurper = new JsonSlurper()
        def data = jsonSlurper.parseText(jsonLine)
        return data.status == 'success' && data.URL == host && data.accessKey == username && data.secretKey == password
    } catch (exc) {
        return false
    }
}

def checkOSSAlias(String alias_name) {
    try {
        String content = utils.runCommandWithStdout("mc alias ls ${alias_name} --json")
        def lines = content.readLines()
        def jsonLine = lines.find { it.startsWith('{') }
        println(jsonLine)
        def jsonSlurper = new JsonSlurper()
        def data = jsonSlurper.parseText(jsonLine)
        return data.status == 'success'
    } catch (exc) {
        return false
    }
}

/**
 * Retries a function with random wait times until it succeeds or the maximum retry time is reached.
 *
 * @param maxRetryTimeSeconds Maximum time in seconds to keep retrying
 * @param maxIntervalSeconds Maximum interval in seconds between retries (actual interval will be random between 1 and this value)
 * @param closure The function to retry
 * @return The result of the function if successful, or throws the last error if all retries fail
 */
def retryWithRandomBackoff(int maxRetryTimeSeconds = 600, int maxIntervalSeconds = 10, int perTryTimeoutSeconds = 60, Closure closure) {
    def attempt = 0
    def rng = new SecureRandom()

    utils.waitUntilTimeout(time: maxRetryTimeSeconds, unit: 'SECONDS', quiet: true) {
        attempt++
        try {
            // Avoid deadlock in closure
            timeout(time: perTryTimeoutSeconds, unit: 'SECONDS') {
                closure.call()
            }
            echo "Function succeeded on attempt ${attempt}"
            return true
        } catch (exc) {
            def randomWaitSeconds = 1 + rng.nextInt(maxIntervalSeconds)
            echo "Attempt ${attempt} failed: ${exc.message}. Retrying in ${randomWaitSeconds} seconds..."
            sleep(time: randomWaitSeconds, unit: 'SECONDS')
            return false  // waitUntil
        }
    }
}

/**
 * Sets up MinIO client aliases for both OSS endpoints
 *
 * @param username Username for the OSS endpoint
 * @param password Password for the OSS endpoint
 * @return Map containing the status of each alias setup operation
 */
def setUp(String username=OSS_USER, String password=OSS_PWD) {
    def setupAlias = { String aliasName, String endpoint, String user, String pwd ->
        def cmd = "mc alias set ${aliasName} ${endpoint} ${user} ${pwd}"
        try {
            retryWithRandomBackoff(600, 10) {
                utils.runCommand(cmd)
                echo "Successfully set alias for ${aliasName}"
            }
            return true
        } catch (e) {
            utils.showErrorMessage("Failed to set alias for ${aliasName} after multiple retries: ${e.message}")
            return false
        }
    }

    def ossResult = true
    def ossciResult = true
    def mossResult = true
    // Execute both commands independently
    if (!checkOSSAlias(OSS, 'oss', username, password)) {
        ossResult = setupAlias('oss', OSS, username, password)
    }
    if (!checkOSSAlias(OSS_CI, 'swci-oss', OSS_CI_USER, OSS_CI_PWD)) {
        ossciResult = setupAlias('swci-oss', OSS_CI, OSS_CI_USER, OSS_CI_PWD)
    }
    credentials.runWithCredential('SH_MOSS') {
        def mossUser = USERNAME
        def mossPwd = PASSWORD
        def endpoint = MOSS
        // TODO: remove this once ZhiJiang network issue resolved
        if (env.jenkinsNode =~ '(10.116.|10.18.)') {
            endpoint = 'http://***********:56548'
        }
        if (!checkOSSAlias(MOSS, 'sh-moss', mossUser, mossPwd)) {
            mossResult = setupAlias('sh-moss', endpoint, mossUser, mossPwd)
        }
    }

    // Return a map with the status of each operation
    return [
        'oss': ossResult,
        'swci-oss': ossciResult,
        'sh-moss': mossResult
    ]
}

def install(String username=OSS_USER, String password=OSS_PWD) {
    try {
        if (!verify()) {
            def arch = sh(script: 'arch', returnStdout: true).trim()
            def mcUrl = constants.mcUrl[arch]
            retryWithRandomBackoff(600, 10, 180) {
                sh """
                    rm -rf /usr/local/bin/mc ||:
                    wget -q --no-check-certificate ${mcUrl} -O /usr/local/bin/mc
                    chmod 755 /usr/local/bin/mc
                """
            }
        }
        return setUp(username, password)
    } catch (exc) {
        utils.showErrorMessage("Failed to install mc: ${exc.message}")
        return false
    }
}

def getOssAlias(String ossPath) {
    def parts = ossPath.tokenize('/')
    return (parts && ['oss', 'swci-oss', 'sh-moss'].contains(parts[0])) ? parts[0] : null
}

def cp(String src, String dest = './', recursive = true, retries = 30) {
    // mc cp oss/xx//file will failed ...
    src = src.replaceAll('//+', '/')
    dest = dest.replaceAll('//+', '/')
    def attempt = 0
    int elapsed = 0
    int interval = 60
    def copyFailed = false
    def copyFinished = false
    def errorMessage = ''
    def ossAlias = getOssAlias(dest) ?: getOssAlias(src)
    stage('oss cp') {
        utils.waitUntilTimeout(time: env.ossTimeout ?: 60, unit: 'MINUTES', initialRecurrencePeriod: 30000, quiet: true) {
            attempt++
            copyFailed = false
            try {
                parallel(
                    cpTask: {
                        try {
                            println "🟢 Start copying from ${src} to ${dest}"
                            def cmd = recursive ? 'mc cp -r' : 'mc cp'
                            utils.runCommand("${cmd} ${src} ${dest}")
                            copyFinished = true
                            println '✅ Copy succeeded'
                        } catch (exc) {
                            println '❌ Copy failed'
                            copyFailed = true
                            throw exc
                        }
                    },
                    heartbeat: {
                        while (!copyFinished && !copyFailed) {
                            if (!checkOSSAlias(ossAlias)) {
                                utils.showErrorMessage("❌ OSS alias ${ossAlias} heartbeat failed at ${elapsed}s")
                                error "💔 OSS alias ${ossAlias} heartbeat failed"
                            }
                            println "💓 Heartbeat for ${ossAlias} passed at ${elapsed}s"
                            int slept = 0
                            while (slept < interval) {
                                if (copyFinished || copyFailed) {
                                    return
                                }
                                sleep(time: 1, unit: 'SECONDS')
                                slept += 1
                            }
                            elapsed += interval
                        }
                    },
                    failFast: true
                )
            } catch (exc) {
                if (exc instanceof org.jenkinsci.plugins.workflow.steps.FlowInterruptedException
                    || exc instanceof InterruptedException) {
                    throw exc
                    }
                utils.showErrorMessage("⚠️ Attempt ${attempt} failed: ${exc.message}")
                errorMessage = exc.message
            }
            return copyFinished || (attempt == retries)
        }
        if (!copyFinished) {
            error "❌ Failed after ${attempt} attempts: ${errorMessage}"
        }
    }
}

def ls(String src) {
    retryWithRandomBackoff(600, 10) {
        return utils.runCommandWithStdout("mc ls ${src}")
    }
}
