def shouldRunKmemleak() {
    def repoShouldRunKmemleak = ['gr-kmd', 'linux-ddk']
    return env.gitlabSourceRepoName in repoShouldRunKmemleak && fileExists('/sys/kernel/debug/kmemleak')
}

/**
 * Clear kmemleak before test execution
 * Should be called in setUpOnNode() or similar setup methods
 */
def clearKmemleak() {
    if (shouldRunKmemleak()) {
        sh 'echo clear > /sys/kernel/debug/kmemleak'
        echo '✅ Kmemleak cleared'
    }
}

/**
 * Check for memory leaks and ASAN issues after test execution
 * This method consolidates the common kmemleak and ASAN detection logic
 *
 * @param logAddress OSS path for uploading logs (optional, will generate default if not provided)
 * @param maxKmemleak Maximum number of kmemleak entries to capture (default: 500)
 */
def checkMemoryIssues(String logAddress = null, int maxKmemleak = 500) {
    if (!shouldRunKmemleak()) {
        echo '⏭️ Skipping memory leak check (not applicable for this repo/node)'
        return
    }

    // Generate default log address if not provided
    logAddress = logAddress ?: "sh-moss/sw-pr/${env.gitlabSourceRepoName}/${env.gitlabMergeRequestIid}/${env.BUILD_ID}/log"

    echo '🔍 Checking for memory leaks and ASAN issues...'

    // Check kmemleak
    def kmemleak = sh(
        script: "echo scan > /sys/kernel/debug/kmemleak; head -n ${maxKmemleak} /sys/kernel/debug/kmemleak | grep \"mtgpu\" | tee kmemleak.log",
        returnStdout: true
    ).trim()

    // Check dmesg for ASAN issues
    def dmesgOut = sh(
        script: 'dmesg -T | tee dmesgOut.log',
        returnStdout: true
    ).trim()

    // Process kmemleak results
    if (kmemleak) {
        artifact.uploadTestReport('kmemleak.log', logAddress, 'kmemleak log')
        error("❌ Memory leak detected!\nkmemleak output:\n${kmemleak}\n")
    }

    // Process ASAN results
    if (dmesgOut =~ /BUG: ASAN:/) {
        artifact.uploadTestReport('dmesgOut.log', logAddress, 'dmesgOut log')
        error("❌ ASAN issue detected in dmesg!\ndmesg output:\n${dmesgOut}\n")
    }

    echo '✅ No memory leaks or ASAN issues detected'
}
