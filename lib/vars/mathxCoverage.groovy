import groovy.transform.Field

@Field WORK_SPACE_MUSATOOLKIT = '/go/build/musa_toolkit_master_gcov/'
@Field WORK_SPACE_MTCC = '/go/build/mtcc_master_gcov/'

@Field Map llvm_cov_plan = [
    'muBLAS':['llvm_cov_directory':'/usr/local/musa/lib', 'src_remove':"""\
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/include/internal/mublas_bfloat16.h \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_asum_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_asum_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_axpy_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_copy_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_copy_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_dot_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_dot_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_iamax_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_iamax_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_iamin_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_iamin_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_nrm2_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_nrm2_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rot_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rot_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rotg_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rotg_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rotm_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rotm_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rotmg_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_rotmg_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_scal_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_scal_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_swap_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas1/mublas_swap_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_gbmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_gbmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_gemv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_ger_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_ger_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hbmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hbmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hemv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hemv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_her2_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_her2_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_her_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_her_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hpmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hpmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hpr2_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hpr2_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hpr_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_hpr_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_sbmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_sbmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_spmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_spmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_spr2_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_spr2_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_spr_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_spr_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_symv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_symv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_syr_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_syr2_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_syr2_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_syr_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tbmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tbmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tbsv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tbsv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tpmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tpmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tpsv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_tpsv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_trmv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_trmv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_trsv_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_trsv_kernels.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas2/mublas_trsv_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_dgmm.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_dgmm_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_dgmm_kernels.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_dgmm_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_geam.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_geam_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_geam_kernels.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_geam_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_hemm_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_hemm_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_her2k_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_her2k_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_herk_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_herk_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_herkx_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_herkx_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_symm_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_symm_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_syr2k_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_syr2k_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_syrk_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_syrk_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_syrkx_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_syrkx_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trmm_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trmm_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trsm_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trsm_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trtri.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trtri.hpp  \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trtri_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/mublas_trtri_strided_batched.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas3/trtri_trsm.hpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/blas_ex/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/include/logging.hpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muBLAS/library/src/include/tuple_helper.hpp""",
                                                            'so_path':[], 'so_name':'libmublas.so',],
    'muBLASLt':['llvm_cov_directory':'/usr/local/musa/lib', 'src_remove':'', 'so_path':[], 'so_name':'libmublasLt.so',],
    'muFFT':['llvm_cov_directory':'/usr/local/musa/lib', 'src_remove':'''\
                                                                /usr/local/musa/include/channel_descriptor.h \
                                                                /usr/local/musa/include/driver_functions.h \
                                                                /usr/local/musa/include/muComplex.h \
                                                                /usr/local/musa/include/musa_runtime.h \
                                                                /usr/local/musa/include/vector_functions.hpp \
                                                                /usr/local/musa/include/vector_types.h''',
                                                            'so_path':[], 'so_name':'fft',],
    'muSPARSE':['llvm_cov_directory':'/usr/local/musa/lib', 'src_remove':"""\
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muSPARSE/3rdparty/muThrust/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muSPARSE/3rdparty/muAlg/*""",
                                                            'so_path':[], 'so_name':'musparse',],
    'muSOLVER':['llvm_cov_directory':'/usr/local/musa/lib', 'src_remove':'', 'so_path':[], 'so_name':'musolver',],
    'muPP':['llvm_cov_directory':'/usr/local/musa/lib', 'src_remove':"""\
                                                                /usr/local/musa/include/musa.h \
                                                                /usr/local/musa/include/channel_descriptor.h \
                                                                /usr/local/musa/include/driver_functions.h \
                                                                /usr/local/musa/include/musa_runtime.h \
                                                                /usr/local/musa/include/vector_functions.hpp \
                                                                /usr/local/musa/include/vector_types.h \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/Core/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/Core/arch/Default/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/Core/arch/SSE/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/Core/functors/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/Core/products/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/Core/util/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/Geometry/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/IterativeLinearSolvers/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/LU/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/OrderingMethods/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/SparseCore/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/SparseLU/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/eigen/Eigen/src/plugins/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/spdlog/include/spdlog/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/spdlog/include/spdlog/details/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/spdlog/include/spdlog/fmt/bundled/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/3rdparty/spdlog/include/spdlog/sinks/* \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/mupps \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppitc \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_average_error.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_average_relative_error.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_count_in_range.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_dot_prod.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_histogram_range.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_image_norms.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_image_proximity.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_integral.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_iqabatch_advanced.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_iqabatch.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_iqa.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_max.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_max_every.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_maximum_error.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_maximu_relative_error.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_maxindx.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_mean.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_min.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_min_every.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_minindx.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_minmax.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_minmaxindx.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_quality_index.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_sqr_integral.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppist/muppi_sum.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppim/muppi_complex_morphology.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppig/muppi_mirror_batch.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppig/muppi_resize_sqr_pixel.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppif/muppi_1d_linear_filter.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppif/muppi_1d_window_sum.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppif/muppi_convolution.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppidei/muppi_convert_bit_depth.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppicc/muppi_color_gamma_correction.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppicc/muppi_complement_color_key.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppicc/muppi_color_sampling_format_conversion.cpp \
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muPP/src/interface/mu/muppial/muppial_custom_operations.cpp""",
                                                            'so_path':[], 'so_name':'mupp',],
    'muRAND':['llvm_cov_directory':'/usr/local/musa/lib', 'src_remove':"""\
                                                                ${WORK_SPACE_MUSATOOLKIT}musa_toolkit/muRAND/library/include/*""",
                                                            'so_path':[], 'so_name':'murand',],
    'muThrust':['llvm_cov_directory':'muThrust/build', 'src_remove':'', 'so_path':[], 'so_name':'thrust',],
    'muAlg':['llvm_cov_directory':'muAlg/build', 'src_remove':'', 'so_path':[], 'so_name':'alg',],
    'muDNN':['llvm_cov_directory':'', 'src_remove':''' \
                                                                3rdparty/mutlass/include/mutlass \
                                                                3rdparty/mutlass/include/mutlass/detail \
                                                                3rdparty/mutlass/include/mutlass/gemm \
                                                                3rdparty/mutlass/include/mutlass/gemm/collective/builders \
                                                                3rdparty/mutlass/include/mutlass/layout \
                                                                3rdparty/mutlass/include/mutlass/platform \
                                                                include/mudnn/api/config/version.md \
                                                                ''',
                                                            'so_list':[], 'so_name':'mudnn',],
]

@Field Map gcov_plan = [
    'mtcc':['gcov_directory':"${WORK_SPACE_MTCC}mtcc/build;", 'src_remove':"${WORK_SPACE_MTCC}mtcc/build*"],
]

// 使用 LLVM/Clang 工具链
// 使用 llvm-cov、llvm-profdata 等工具处理覆盖率数据
// 在编译时通过 -fprofile-instr-generate -fcoverage-mapping 参数生成覆盖率信息，运行时生成 .profraw 文件，然后使用 llvm-profdata 合并这些文件为 .profdata 格式，最后用 llvm-cov 导出为 lcov 格式供 genhtml 使用。
def mathxGenerateCoverage(llvm_cov_directory, src_remove, so_name, repo) {
    try {
        sh '''
            pwd
            ls -l
            ls -l llvm_cov_data
            ls -l /usr/local/musa ||:
            sudo apt update -y
            sudo apt install lcov -y
        '''

        def so_path = []
        so_path = sh(script:"find ${llvm_cov_directory} -name '*.so' | grep -i ${so_name} ||:", returnStdout:true).trim().split('\n')
        println "so_path: ${so_path.join(',')}"

        PATH_GCOV = 'export PATH=/usr/local/musa/bin:${PATH}'
        // 步骤1： 根据 llvm_cov_data/*.profraw， 生成多个 ./tmp_${idx}.profdata
        def profraw_list = []
        profraw_list = sh(script:'find ./llvm_cov_data/ -name *.profraw ||:', returnStdout:true).trim().split('\n')
        println "profraw_list: ${profraw_list.join(',')}"
        profraw_list.eachWithIndex { profraw_file, idx ->
            sh """
                ${PATH_GCOV}
                llvm-profdata merge --sparse ${profraw_file} -o ./tmp_${idx}.profdata ||:
            """
        }

        def gpuType = utils.getGpuTypeFromSelector(env.podNodeSelector)
        println "gpuType: ${gpuType}"
        // 步骤2： 根据多个 ./tmp_${idx}.profdata， 生成./${gpuType}_${repo}_coverage.profdata
        sh """
            ${PATH_GCOV}
            llvm-profdata merge --sparse tmp_*.profdata -o ./${gpuType}_${repo}_coverage.profdata
            tar -czvf ${gpuType}_${repo}_coverage.tar.gz ${gpuType}_${repo}_coverage.profdata
        """
        // 上传${gpuType}_${repo}_coverage.profdata  供后续合并多架构代码覆盖率使用
        artifact.uploadTestReport("${gpuType}_${repo}_coverage.tar.gz", env.reportOssPath)

        // 步骤3： 循环遍历so_path内的*.so 和*.profdata，生成若干 *_coverage_tmp_${index}.lcov
        if (so_path) {
            so_path.eachWithIndex { so_file, index ->
                sh """
                    ls -l ${so_file}
                    ${PATH_GCOV}
                    llvm-cov export --format=lcov --instr-profile ./${gpuType}_${repo}_coverage.profdata --show-branch-summary ${so_file} > ./${repo}_coverage_tmp_${index}.lcov
                """
                if (src_remove) {
                    sh """
                        ${PATH_GCOV}
                        lcov --rc lcov_branch_coverage=1 --remove ./${repo}_coverage_tmp_${index}.lcov ${src_remove} -o ./${repo}_coverage_last_${index}.lcov
                        rm -rf ./${repo}_coverage_tmp_${index}.lcov
                    """
                }
            }
        }

        // 步骤4：生成代码覆盖率报告
        sh """
            ${PATH_GCOV}
            genhtml --rc genhtml_branch_coverage=1 ./${repo}_coverage*.lcov --output-directory ${repo}_llvm_coverage_report
            ls -l ${repo}_llvm_coverage_report
        """
        return "${repo}_llvm_coverage_report"
    }
    catch (e) {
        println e
        return ''
    }
}

// 使用 GCC 工具链和 gcov 工具
// 直接使用 lcov 命令处理覆盖率数据
// 在编译时插入覆盖率检测代码，运行后生成 .gcda 和 .gcno 文件，然后使用 lcov 直接处理这些文件生成覆盖率报告。
def mtccGenerateCoverage(gcov_directory, src_remove, repo) {
    try {
        sh '''
            pwd
            ls -l
            sudo apt update -y
            sudo apt install lcov -y
        '''
        def lcov_cmd = '--rc lcov_branch_coverage=1'
        def directory_args = ''
        gcov_directory.split(';').each {
            if (it.trim()) {
                directory_args += ' -d ' + it.trim()
            }
        }
        sh """
            rm -rf gcov_all.info gcov.info report_gcov coverage
            lcov ${lcov_cmd} -c ${directory_args} -o coverage --gcov-tool /usr/bin/gcov
            lcov ${lcov_cmd} -e coverage '*MTGPU/*' -o coverage --gcov-tool /usr/bin/gcov
            lcov ${lcov_cmd} --remove coverage '${src_remove}' -o coverage --gcov-tool /usr/bin/gcov
            genhtml --rc genhtml_branch_coverage=1 coverage -o ${repo}_coverage_report
        """
        return "${repo}_coverage_report"
    }
    catch (e) {
        print e
        return ''
    }
}

def mudnnGenerateCoverage(repo, llvm_cov_item) {
    try {
        //step0: find mudnn so list
        llvm_cov_item.so_list = sh(
            script: "find ${llvm_cov_item.llvm_cov_directory} -name '*.so' 2>/dev/null || echo ''",
            returnStdout: true
        ).trim().split('\n').findAll { !it.isEmpty() }
        println "llvm_cov_item: ${llvm_cov_item}" // only for debug

        PATH_GCOV = 'export PATH=/usr/local/musa/bin:${PATH}'
        // 步骤1： 根据 llvm_cov_data/*.profraw， 生成多个 ./tmp_${idx}.profdata
        def profraw_list = []
        profraw_list = sh(script:'find ./llvm_cov_data/ -name *.profraw ||:', returnStdout:true).trim().split('\n')
        println "profraw_list: ${profraw_list.join(',')}"// only for debug
        profraw_list.eachWithIndex { profraw_file, idx ->
            sh """
                ${PATH_GCOV}
                llvm-profdata merge --sparse ${profraw_file} -o ./tmp_${idx}.profdata ||:
            """
        }

        // 步骤2： 根据多个 ./tmp_${idx}.profdata， 生成./${repo}_coverage.profdata 并上传prodata数据
        def gpuType = utils.getGpuTypeFromSelector(env.podNodeSelector)
        println "gpuType: ${gpuType}"  // only for debug
        sh """
            ${PATH_GCOV}
            llvm-profdata merge --sparse tmp_*.profdata -o ./${gpuType}_${repo}_coverage.profdata
            tar -czvf ${gpuType}_${repo}_coverage.tar.gz ${gpuType}_${repo}_coverage.profdata
        """
        artifact.uploadTestReport("${gpuType}_${repo}_coverage.tar.gz", env.reportOssPath)

        // 步骤3： 循环遍历so_path内的*.so 和*.profdata，生成若干 *_coverage_tmp_${index}.lcov
        if (llvm_cov_item.so_list) {
            llvm_cov_item.so_list.eachWithIndex { so_file, index -> // --path-equivalence=${build_path},${test_path}
                sh """
                    ls -l ${so_file}
                    ${PATH_GCOV}
                    llvm-cov export --format=lcov --instr-profile ./${gpuType}_${repo}_coverage.profdata --show-branch-summary ${so_file} > ./${gpuType}_${repo}_coverage_tmp_${index}.lcov
                """
                if (llvm_cov_item.src_remove) {
                    // 将src_remove字符串按行分割成数组，并过滤掉空行
                    def srcRemoveList = llvm_cov_item.src_remove.split('[\r\n]+').findAll { it.trim() }
                    def removePaths = srcRemoveList.collect { path ->
                        "${llvm_cov_item.llvm_cov_directory}/${path.trim()}"
                    }.join(' ')

                    sh """
                        ${PATH_GCOV}
                        lcov --rc lcov_branch_coverage=1 --remove ./${gpuType}_${repo}_coverage_tmp_${index}.lcov ${removePaths} -o ./${gpuType}_${repo}_coverage_last_${index}.lcov
                        rm -rf ./${gpuType}_${repo}_coverage_tmp_${index}.lcov
                    """
                }
        }
    }

        // 步骤4：生成代码覆盖率报告
        sh """
            ${PATH_GCOV}
            genhtml --ignore-errors source --rc genhtml_branch_coverage=1 ./${gpuType}_${repo}_coverage*.lcov --output-directory ${gpuType}_${repo}_llvm_coverage_report
            ls -l ${gpuType}_${repo}_llvm_coverage_report
        """
        return "${gpuType}_${repo}_llvm_coverage_report"
}
    catch (e) {
        println e
        return ''
    }
}
