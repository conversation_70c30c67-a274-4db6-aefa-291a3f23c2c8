def updateAptSourcesList() {
    def osType = utils.runCommandWithStdout('cat /etc/os-release').toLowerCase()
    def arch = utils.runCommandWithStdout('arch').toLowerCase()

    if (!osType.contains('ubuntu')) { return }

    def osVersion = utils.runCommandWithStdout("awk -F'=' '/VERSION_CODENAME/ {print \$2}' /etc/os-release").trim()
    def targetDomain = 'nexus.shg1.mthreads.com'
    def proxy = 'apt-proxy'
    // internal proxy is different for x86 and arm64
    if (arch == 'aarch64' || arch == 'arm64') {
        proxy = 'apt-proxy-mt'
    }
    def newSourcesContent = """
deb https://${targetDomain}/repository/${proxy}/ ${osVersion} main restricted universe multiverse
deb https://${targetDomain}/repository/${proxy}/ ${osVersion}-security main restricted universe multiverse
deb https://${targetDomain}/repository/${proxy}/ ${osVersion}-updates main restricted universe multiverse
"""
    // revert this when all nodes are updated
    // def sourcesContent = utils.runCommandWithStdout('cat /etc/apt/sources.list')
    // def needsUpdate = !sourcesContent.contains(targetDomain) || !sourcesContent.contains(osVersion)
    def needsUpdate = true
    sh 'rm -rf /etc/apt/sources.list.d/* ||:'
    sh 'sed -i "/^[^#]/ {/nexus\\.shg1\\.mthreads\\.com/! s/^/# /}" /etc/apt/sources.list'
    if (needsUpdate) {
        timeout(time: 15, unit: 'MINUTES') {
            sh """
                cat > /etc/apt/sources.list << EOF
${newSourcesContent}
EOF
            apt-get update ||:
            """
            println('sources.list updated')
        }
    } else {
        println('sources.list all good.')
    }
    def updatedSource = utils.runCommandWithStdout('cat /etc/apt/sources.list')
    println(updatedSource)
}

def installPackage(String packageName, int timeoutSeconds = 300, int maxRetries = 3) {
    def attempt = 0
    def logFile = '/tmp/apt_install.log'

    utils.waitUntilTimeout(time: timeoutSeconds, unit: 'SECONDS', initialRecurrencePeriod: 5000, quiet: true) {
        attempt++
        echo "Attempt ${attempt}: Installing ${packageName}"
        sh(script: "echo '' > ${logFile}", returnStatus: true)
        def exitCode = sh(script: "apt-get install -y ${packageName} > ${logFile} 2>&1", returnStatus: true)
        def aptOutput = sh(script: "cat ${logFile}", returnStdout: true).trim()
        if (exitCode == 0) {
            println(aptOutput)
            echo "Package ${packageName} installed successfully."
            return true // end waitUntil
        }
        echo "Attempt ${attempt} failed. Checking logs..."
        handleAptFailure(aptOutput)

        if (attempt >= maxRetries) {
            error("Max retries reached (${maxRetries}). Failed to install ${packageName}.")
        }

        return false // waitUntil
    }
}

def handleAptFailure(String errorMessage) {
    echo "APT Failure Output: ${errorMessage}"

    if (errorMessage.contains('held broken packages')) {
        echo 'Detected broken package issue. Running --fix-broken.'
        sh(script: 'apt-get install -f -y', returnStatus: true)
    }
    if (errorMessage.contains('could not get lock') || errorMessage.contains('dpkg status database is locked')) {
        echo 'Detected APT lock issue. Retrying.'
        // sh(script: 'rm -rf /var/lib/dpkg/lock /var/lib/dpkg/lock-frontend /var/lib/apt/lists/lock', returnStatus: true)
        sh(script: 'sleep 5', returnStatus: true)
    }
    if (errorMessage.contains('dpkg: error processing package')) {
        echo 'Detected dpkg configuration issue. Running dpkg --configure -a.'
        sh(script: 'dpkg --configure -a', returnStatus: true)
    }
    if (errorMessage.contains('Hash sum mismatch')) {
        echo 'Detected hash sum mismatch. Cleaning and updating package lists.'
        sh(script: 'rm -rf /var/lib/apt/lists/*', returnStatus: true)
        sh(script: 'apt-get clean', returnStatus: true)
    }
    if (errorMessage.contains('404 Not Found')) {
        echo 'Detected 404 Not Found. Removing cache and retrying.'
        sh(script: 'apt-get clean', returnStatus: true)
    }
    if (errorMessage.contains('Unable to locate package')) {
        echo 'Package not found in repositories. Checking sources list.'
        updateAptSourcesList()
    }
    if (errorMessage.contains('temporary failure resolving')) {
        echo 'Network issue detected. Restarting networking service.'
        sh(script: 'systemctl restart NetworkManager', returnStatus: true)
        sh(script: 'sleep 5', returnStatus: true)
    }

    sh(script: 'apt-get update', returnStatus: true)
}
