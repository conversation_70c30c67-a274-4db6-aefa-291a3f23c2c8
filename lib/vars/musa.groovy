def installMusaRuntime(musaRuntimePackageUrl, targetPath = '') {
    currentBuild.description += "musaRuntime: ${musaRuntimePackageUrl}<br>"
    constants.downloadAndUnzipPackage(musaRuntimePackageUrl)
    sh "cd ./MUSA-Runtime && ./install.sh ${targetPath}"
}

def installMtshmem(mtshmemPackageUrl) {
    currentBuild.description += "mtshmem: ${mtshmemPackageUrl}<br>"
    constants.downloadAndUnzipPackage(mtshmemPackageUrl)
    sh 'cd ./mtshmem && ./install.sh'
}

def installMtcc(mtccPackageUrl) {
    currentBuild.description += "mtcc: ${mtccPackageUrl}<br>"
    constants.downloadAndUnzipPackage(mtccPackageUrl)
    sh './install.sh'
    sh 'which clang-tidy ||:'
    sh 'clang-tidy --version ||:'
}

def installMusaToolkits(musaToolkitsPackageUrl) {
    currentBuild.description += "musa toolkits: ${musaToolkitsPackageUrl}<br>"
    constants.downloadAndUnzipPackage(musaToolkitsPackageUrl)
    sh 'cd ./musa_toolkits_install && ./install.sh'
}

def installMusify(musifyPackageUrl) {
    currentBuild.description += "musify: ${musifyPackageUrl}<br>"
    constants.downloadAndUnzipPackage(musifyPackageUrl)
}

def installmuAlg(muAlgPackageUrl) {
    currentBuild.description += "muAlg: ${muAlgPackageUrl}<br>"
    constants.downloadAndUnzipPackage(muAlgPackageUrl, "${env.WORKSPACE}/muAlg")
    sh """
        cd ${env.WORKSPACE}/muAlg/package
        ls *.deb | xargs -n1 dpkg -i
    """
}

def installmuThrust(muThrustPackageUrl) {
    currentBuild.description += "muThrust: ${muThrustPackageUrl}<br>"
    constants.downloadAndUnzipPackage(muThrustPackageUrl, "${env.WORKSPACE}/muThrust")
    sh """
        cd ${env.WORKSPACE}/muThrust/package
        ls *.deb | xargs -n1 dpkg -i
    """
}

def installMusaCmake(musaCmakePackageUrl) {
    currentBuild.description += "musa_cmake: ${musaCmakePackageUrl}<br>"
    constants.downloadAndUnzipPackage(musaCmakePackageUrl, "${env.WORKSPACE}/musa_cmake")
    dir('musa_cmake') {
        sh 'mv cmake/ /usr/local/musa'
    }
}
