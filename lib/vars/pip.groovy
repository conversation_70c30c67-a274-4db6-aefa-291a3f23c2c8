def installPackage(String packageName, String pipCmd = 'pip3', String mirrorUrl = '', int timeoutSeconds = 300, int maxRetries = 3) {
    def attempt = 0
    def logFile = '/tmp/pip_install.log'
    def pipInstallCmd = "${pipCmd} install ${packageName} --no-cache-dir"

    if (mirrorUrl?.trim()) {
        def mirrorHost = mirrorUrl.replaceAll('https?://', '').split('/')[0]
        pipInstallCmd += " -i ${mirrorUrl}"

        if (mirrorUrl.startsWith('http://') || !mirrorUrl.contains('pypi.org')) {
            pipInstallCmd += " --trusted-host ${mirrorHost}"
        }
    }

    utils.waitUntilTimeout(time: timeoutSeconds, unit: 'SECONDS', initialRecurrencePeriod: 5000, quiet: true) {
        attempt++
        echo "Attempt ${attempt}: Installing Python package '${packageName}' using '${pipCmd}'"
        sh(script: "echo '' > ${logFile}", returnStatus: true)
        def checkInstalled = sh(script: "${pipCmd} show ${packageName}", returnStatus: true)
        if (checkInstalled == 0) {
            echo "Package '${packageName}' is already installed. Skipping installation."
            return true
        }

        def exitCode = sh(script: "${pipInstallCmd} > ${logFile} 2>&1", returnStatus: true)
        def pipOutput = sh(script: "cat ${logFile}", returnStdout: true).trim()
        if (exitCode == 0) {
            println(pipOutput)
            echo "Package ${packageName} installed successfully."
            return true // end waitUntil
        }
        echo "Attempt ${attempt} failed. Checking logs..."
        handlePipFailure(pipOutput, packageName, pipCmd)

        if (attempt >= maxRetries) {
            error("Max retries reached (${maxRetries}). Failed to install Python package '${packageName}'.")
        }

        return false // waitUntil
    }
}

def handlePipFailure(String errorMessage, String packageName, String pipCmd) {
    echo "PIP Failure Output:\n${errorMessage}"

    if (errorMessage.contains('Could not find a version')) {
        echo "Package '${packageName}' might not exist. Checking package name..."
        sh(script: "${pipCmd} search ${packageName}", returnStatus: true)
    }
    if (errorMessage.contains('No matching distribution found')) {
        echo 'No compatible version found. Checking Python version...'
        sh(script: 'python3 --version || python --version', returnStatus: true)
    }
    if (errorMessage.contains('Could not install packages due to an OSError')) {
        echo 'Permission issue detected. Trying installation with sudo.'
        sh(script: "sudo ${pipCmd} install ${packageName} --no-cache-dir", returnStatus: true)
    }
    if (errorMessage.contains("Consider upgrading via 'pip install --upgrade pip'")) {
        echo 'Outdated pip version detected. Upgrading pip.'
        sh(script: "${pipCmd} install --upgrade pip setuptools wheel", returnStatus: true)
    }
    if (errorMessage.contains('SSL: CERTIFICATE_VERIFY_FAILED')) {
        echo 'SSL verification error detected. Retrying with trusted hosts.'
        sh(script: "${pipCmd} install ${packageName} --trusted-host nexus.infra.shg1.mthreads.com", returnStatus: true)
    }
    if (errorMessage.contains('ConnectionError')) {
        echo 'Network issue detected. Retrying after resetting network.'
        sh(script: 'systemctl restart NetworkManager', returnStatus: true)
        sh(script: 'sleep 5', returnStatus: true)
    }
    if (errorMessage.contains('ReadTimeoutError') || errorMessage.contains('RemoteDisconnected')) {
        echo 'Connection timeout detected. Retrying with a longer timeout.'
        sh(script: "${pipCmd} install ${packageName} --timeout 100", returnStatus: true)
    }
}
