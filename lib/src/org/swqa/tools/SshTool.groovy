package org.swqa.tools

class SshTool implements Serializable {

    private static final long serialVersionUID = 1L
    Script steps
    String user
    String ip
    int port = 22
    String sshOpts

    SshTool(steps, Map config = [:]) {
        this.steps = steps
        this.user = config.user ?: 'root'
        this.ip = config.ip ?: '127.0.0.1'
        this.port = config.port ?: 22
        this.sshOpts = config.sshOpts ?: this.sshOpts
    }

    String getSshCmd() {
        return "ssh ${sshOpts} ${user}@${ip} -p ${port}"
    }

    String getScpCmd() {
        return "scp -P ${port} ${sshOpts.replaceAll('-o', '-o')}"
    }

    /**
     * Execute a command on the remote host.
     * Supports multi-line strings.
     */
    void exec(String command) {
        steps.echo "[ssh][${user}@${ip}] ${command}"
        steps.sh """
            ${getSshCmd()} <<'EOF'
${command}
EOF
        """
    }

    /**
     * Execute a command on the remote host and return stdout.
     * Supports multi-line strings.
     */
    String execWithOutput(String command) {
        steps.echo "[ssh][${user}@${ip}] ${command} (with output)"
        return steps.sh(script: """
            ${getSshCmd()} <<'EOF'
${command}
EOF
        """, returnStdout: true).trim()
    }

    void scpTo(String localPath, String remotePath) {
        steps.echo "[scp -> ${user}@${ip}] ${localPath} => ${remotePath}"
        steps.sh "${getScpCmd()} ${localPath} ${user}@${ip}:${remotePath}"
    }

    void scpFrom(String remotePath, String localPath) {
        steps.echo "[scp <- ${user}@${ip}] ${remotePath} => ${localPath}"
        steps.sh "${getScpCmd()} ${user}@${ip}:${remotePath} ${localPath}"
    }

}
