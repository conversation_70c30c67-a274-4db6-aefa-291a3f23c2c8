@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

// mtmlBranch(develop) repo branch commitId mtmlPackageName isX86 cmd clangTidyCmd

env.repo = 'mt-management'
version = ''
repoPath = env.mtmlPackageName ? "${env.mtmlPackageName}/${env.repo}" : "buildDir/${env.repo}"

def fetchCode() {
    dir(env.mtmlPackageName ?: 'buildDir') {
        if (env.gitlabSourceRepoName == 'mt-management') {
            gitLib.fetchCode(env.repo, env.branch, env.commitId)
        } else {
            gitLib.fetchCode(env.repo, env.mtmlBranch)
        }
    }
}

def build() {
    credentials.runWithCredential('SSH_GITLAB') {
        sh 'mkdir sdk_build_pkg'
        dir(repoPath) {
            version = utils.runCommandWithStdout("egrep -A1 'mtml_version' config.ini | grep 'version = ' | awk -F '=' '{print \$2}'")
            if (gitLib.triggeredByMR()) {
                def buildCmd = env.clangTidyCmd ?: "${env.cmd} ${version} ${env.isX86}"
                timeout(time: env.timeout ?: 15, unit: 'MINUTES') {
                    sh """
                        ${buildCmd}
                        sleep 3
                    """
                }
            } else {
                def date = env.date ?: new Date().format('yyyy.MM.dd')
                version = env.gitlabTargetBranch == 'develop' ? date : version
                def architectures = ['sw64', 'loongarch64', 'i386', 'aarch64', 'arm32', 'x86_64']
                def buildTypes = ['RELEASE', 'DEBUG']
                architectures.each { arch ->
                    buildTypes.each { type ->
                        sh """
                            set -e
                            ./build_ci.sh ${type} ${arch} ${version} NO
                            mv sdk ${env.WORKSPACE}/sdk_build_pkg/${type}_${arch}_sdk
                        """
                    }
                }
                sh "cp -rf install.sh ${env.WORKSPACE}/sdk_build_pkg/"
            }
        }
    }
}

def upload() {
    if (env.clangTidyCmd) {
        dir(repoPath) {
            artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/clang.html')
            artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/tidy.log')
        }
    } else if (env.isX86 == 'YES') {
        dir(repoPath) {
            def packageName = "${constants.formatCommitID(env.commitId)}_${env.mtmlPackageName}.tar.gz"
            sh "tar czf ${packageName} build sdk"
            artifact.upload(env.gitlabSourceRepoName ?: env.repo, env.branch, env.commitId, packageName)
        }
    } else if (!gitLib.triggeredByMR()) {
        def packageName = "${constants.formatCommitID(env.commitId)}_${env.repo}_${version}.tar.gz"
        sh "cd ${env.WORKSPACE}; tar czf ${packageName} sdk_build_pkg"
        def packageUrl = constants.genPackageUrl(env.gitlabSourceRepoName ?: env.repo, env.branch, env.commitId, packageName, true, true)
        artifact.upload(env.gitlabSourceRepoName ?: env.repo, env.branch, env.commitId, packageName)
        sh """
            echo ${packageUrl} > latestUrl.txt
            mc cp latestUrl.txt sh-moss/sw-build/mt-management/${env.gitlabTargetBranch ?: env.branch}
        """
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() } ],
        'upload': [closure: { upload() }]
    ]

    runPipeline(workflow)
}
