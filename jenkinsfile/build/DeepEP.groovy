@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * repo (String) - ''
 * branch (String) - ''
 * commitId (String) - ''
 * linuxDdkPackageUrl (String) - ''
 * mtshmemPackageUrl (String) - ''
 * exports (Multiline String) default ''
    {
        "key" : "value"
    }
 * cmd (String) - ''
 * artifacts(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - ''
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/mcctest/musa-train:4.3.1_20251009
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def envSetUp() {
    if (env.mtshmemPackageUrl) {
        musa.installMtshmem(env.mtshmemPackageUrl)
    }
}

def build() {
    String envExport = env.exports ? commonLib.parseExportVars(env.exports).join(';') : ''
    dir(env.repo) {
        sh """
            ${envExport}
            ${env.cmd}
        """
    }
}

def upload() {
    def artifacts = readJSON(text: env.artifacts)
    artifact.upload(artifacts)
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: { build() }],
    ]
    if (env.artifacts) { workflow['upload'] = [closure: { upload() }] }
    runPipeline(workflow)
}
