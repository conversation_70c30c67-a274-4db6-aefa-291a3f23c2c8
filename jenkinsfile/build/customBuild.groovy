@Library('swqa-ci')

import org.swqa.tools.git

/*
 * parameters
 * repo (String) - null
 * branch (String) - null
 * commitId (String) - null
 * scripts (String) - null
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v62
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
 * triggerInfo
*/

gitLib = new git()

def fetchCode() {
    Map gitOptions = env.disableSubmodules?.toBoolean()
        ? [disableSubmodules: true, noTags: true, updateBuildDescription: true]
        : [submoduleShallow: true, noTags: true, updateBuildDescription: true]
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, gitOptions)
}

def customBuild() {
    credentials.runWithCredential('SSH_GITLAB') {
        def scripts = readJSON(text: env.scripts)
        dir(env.repo) {
            utils.runScripts(scripts)
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'customBuild': [closure: { customBuild() }, setGitlabStatus: true, statusName: env.testLabel]
    ]
    runPipeline(workflow)
}
