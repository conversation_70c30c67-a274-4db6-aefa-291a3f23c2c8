@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * repo (String) - mtJPEG2000
 * branch (String) - develop
 * commitId (String) - ''
 * linuxDdkPackageUrl (String) - ''
 * mtccPackageUrl (String) - 'https://sh-moss.mthreads.com/sw-build/computeQA/musa/newest/mtcc-nightly-x86_64-linux-gnu-ubuntu-20.04.tar.gz'
 * exports (Multiline String) default ''
    {
        "key" : "value"
    }
 * cmd (String) - ./build.sh -a 21,22; tar czf mtJPEG2000.tar.gz mtjpeg2k_sdk mtjpeg2k_test
 * mtJpegPackageName(String) - 'mtJPEG2000.tar.gz'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/linux-ddk:v14
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def envSetUp() {
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    if (env.mtccPackageUrl) {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }
    if (env.musaToolkitPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitPackageUrl)
    }
}

def build() {
    String envExport = env.exports ? commonLib.parseExportVars(env.exports).join(';') : ''
    dir(env.repo) {
        sh """
            ${envExport}
            ${env.cmd}
        """
    }
}

def upload() {
    dir(env.repo) {
        artifact.upload(env.repo, env.branch, env.commitId, env.mtJpegPackageName)
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: { build() }],
    ]
    if (env.mtJpegPackageName) { workflow['upload'] = [closure: { upload() }] }
    runPipeline(workflow)
}
