@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * packageName (String) - muPP.tar.gz
 * linuxDdkPackageUrl (String) - ''
 * mtccPackageUrl (String) - ''
 * anacondaPackageUrl(String) - ''
 * exports (Multiline String) default '', split by '\n'
 * compileArgs (String) -DMUSA_ARCHS=21 -DOPENCV_INC:PATH=/usr/local/include/opencv4 -DOPENCV_LIB:PATH=/usr/local/lib -DIPP_INC=/home/<USER>/ipp/latest/include -DIPP_LIB=/home/<USER>/ipp/latest/lib/intel64 -DINSTALL_DIR=`pwd`/install
 * compileParallel (String) -16
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/mupp_compile-ubuntu-22-04:v1
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'musa_asm'

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://***********:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_${env.packageName}"
}

def envSetUp() {
    sh '''
        apt update ||:
        apt install libcunit1 -y ||:
        rm -rf /usr/local/musa
    '''
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }else {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    if (env.anacondaPackageUrl) {
        constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    }
}

def build() {
    dir(env.repo) {
        sh """
            ${constants.genCondaActivate('mtdnn_mtgpu')}
            python musaasm_build.py build --build_type ${env.buildType} --tar ON
            python3 musaasm_build.py install
            python3 musaasm_build.py test --tar ON
            mv musa_asm.tar.gz ${packageName}
            mv asm_lit_test.tar.gz "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_asm_lit_test.tar.gz"
        """
    }
}

def upload() {
    dir(env.repo) {
        def repo = env.gitlabSourceRepoName && env.gitlabSourceRepoName != 'null' ? env.gitlabSourceRepoName : env.repo
        def branch = env.gitlabSourceBranch ?: env.branch
        def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
        if (env.packagePath) {
            artifact.upload("${packageName}", env.packagePath)
            artifact.upload("${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_asm_lit_test.tar.gz", env.packagePath)
        } else {
            artifact.upload(repo, branch, commitId, "${packageName}")
            artifact.upload(repo, branch, commitId, "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_asm_lit_test.tar.gz")
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
