@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * packageName (String) - muBLAS.tar.gz
 * linuxDdkPackageUrl (String) - ''
 * mtccPackageUrl (String) - 'https://sh-moss.mthreads.com/sw-build/computeQA/musa/newest/mtcc-nightly-x86_64-linux-gnu-ubuntu-20.04.tar.gz'
 * anacondaPackageUrl(String) - 'https://sh-moss.mthreads.com/sw-build/computeQA/tools/musify.tar;sh-moss/sw-build/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * gcov(String) - [ON | OFF]
 * exports (Multiline String) default '', split by ';'
 * compileArgs (String) -DMUSA_ARCHS=21 -DOPENCV_INC:PATH=/usr/local/include/opencv4 -DOPENCV_LIB:PATH=/usr/local/lib -DIPP_INC=/home/<USER>/ipp/latest/include -DIPP_LIB=/home/<USER>/ipp/latest/lib/intel64 -DINSTALL_DIR=`pwd`/install
 * compileParallel (String) -16
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'muBLAS'
env.branch = env.muBLASBranch ? env.muBLASBranch : env.branch
env.commitId = env.muBLASBranch ? '' : env.commitId
envExport = utils.generateEnvExport(env, true)

List envs = env.exports ? env.exports.split(';') : []
if (env.gcov && env.gcov.toString().toUpperCase() == 'ON') {
    envs.add('GCOV_TEST=ON')
}
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://***********:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }

    if (env.gitlabSourceRepoName != env.repo) {
        env.branch = commonLib.findMrDependency(env.repo, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: '')) ?: env.branch
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_${env.packageName}"
}

def envSetUp() {
    sh '''
        apt update ||:
        apt install libcunit1 -y ||:
    '''
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    def dependencies = ['mtcc': env.mtccPackageUrl]
    installDependency(dependencies)
    if (env.musifyPackageUrl) {
        musa.installMusify(env.musifyPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.musaAsmPackageUrl)
    sh '''
        cp musa_asm/build/bin/* /usr/local/musa/bin/
    '''
}

def build() {
    dir(env.repo) {
        if (env.cmd) {
            sh """
                ${envExport}
                ${env.cmd}
            """
        }else {
            sh """
                ${envExport}
                export COMPILERVARS_ARCHITECTURE=intel64
                mkdir build && cd build
                cmake .. ${compileArgs}
                make -j ${compileParallel}
                make install
            """
        }
    }
    sh "tar czf ${packageName} ${env.repo}/build ${env.repo}/install.sh"
}

def upload() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.branch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    if (env.packagePath) {
        artifact.upload("${packageName}", env.packagePath)
    } else {
        artifact.upload(repo, branch, commitId, "${packageName}")
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: { build() }],
    ]
    if (env.packageName) { workflow['upload'] = [closure: { upload() }] }
    runPipeline(workflow)
}
