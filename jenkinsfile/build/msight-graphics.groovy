@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

env.repo = 'msight-graphics'

// runChoice cluster podNodeSelector podResources containerImage branch commitId triggerInfo
def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def build() {
    dir(env.repo) {
        if (env.systemPlatform == 'Linux') {
            credentials.runWithCredential('SSH_GITLAB') {
                sh """
                    ${env.buildScript}
                """
            }
        } else if (env.systemPlatform == 'Windows') {
            bat """
                ${env.buildScript}
            """
        }
    }
}

def upload() {
    def version = ''
    utils.catchErrorContinue {
        def versionMatcher = env.buildScript =~ /--version=([\w.]+)/
        version = versionMatcher[0][1]
        echo "Parsed version: ${version}"
    }

    dir("${env.repo}\\target\\Release\\package") {
        artifact.upload(env.repo, env.gitlabTargetBranch, env.commitId, "msight_graphics_${version}_Windows_AMD64.tar.gz")
    }
}

runner.start(env.runChoice) {
    def workFlow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
    ]
    if (env.systemPlatform == 'Windows' && env.needUpload == 'true') {
        workFlow['upload'] =  [closure: { upload() }]
    }
    runPipeline(workFlow)
}
