@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * exports (Multiline String) default 'usual export', split by '\n'
 * musaToolkitsPackageUrl (String) - default 'musatoolkit url'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

ccachePath = '/home/<USER>/ccache_mu'
env.mountParms = "-v ${ccachePath}:${ccachePath}"
env.repo = 'muDNN'
List envs = env.exports ? env.exports.split('\n') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
mudnn_bench_repo = 'muDNN-Bench'

gnu_dir = env.buildArch == 'x86' ? 'x86_64-linux-gnu' : 'aarch64-linux-gnu'
conda_name = env.buildArch == 'x86' ? 'mtdnn_mtgpu' : 'mudnn_m1000'
env.branch = env.muDNNBranch ? env.muDNNBranch : env.branch
env.commitId = env.muDNNBranch ? '' : env.commitId

def installMusaAsm(musaAsmPackageUrl) {
    currentBuild.description += "musaAsm: ${musaAsmPackageUrl}<br>"
    constants.downloadAndUnzipPackage(musaAsmPackageUrl)
}

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://***********:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    def commitString = env.gitlabMergeRequestLastCommit ? "${constants.formatCommitID(env.gitlabMergeRequestLastCommit)}_" : ''
    packageName = env.packageName ? "${commitString}${env.packageName}" : 'mudnn_dev3.1.0.tar.gz'
    def benchCommit = env.benchCommit ?: ''
    bench_commitId = gitLib.fetchCode(mudnn_bench_repo, 'master', benchCommit)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])

    try {
        if (env.packagePath && !env.packagePath.endsWith('others')) {
            utils.appendVersionInfo(env.repo, env.branch, env.commitId, "${env.packagePath}/others", 'musa_sdk_commit.txt')
        }
    } catch (e) {
        println "追加版本信息文件到OSS失败: ${e.getMessage()}"
    }
}

def envSet() {
    if (env.linuxDdkPackageUrl.contains('deb')) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl.replace('+', '%2B'))
    }
    else {
        def ddk_file = env.linuxDdkPackageUrl.split('/')[-1]
        sh """
            wget -q ${env.linuxDdkPackageUrl} --no-check-certificate
            rpm -e musa
            rpm -ivh ${ddk_file}
        """
    }
    dir('dependencyDir') {
        oss.install()
        for (_ in env.dependcy.split(';')) {
            if (_) {
                oss.cp(_.replace('\n', '').trim(), './')
            }
        }
        utils.installConda()
        if (env.musaToolkitsPackageUrl) {
            musa.installMusaToolkits(env.musaToolkitsPackageUrl)
        }
        else {
            if (env.musaRuntimePackageUrl) {
                musa.installMusaRuntime(env.musaRuntimePackageUrl)
            }
            if (env.mtccPackageUrl) {
                def dependencies = ['mtcc': env.mtccPackageUrl]
                installDependency(dependencies)
            }
            constants.downloadAndUnzipPackage(env.muRANDPackageUrl)
            sh 'cd muRAND && chmod -R 777 . && ./install.sh ||:'
            dir("${env.WORKSPACE}/musa_toolkit") {
                sh 'cp -r cmake /usr/local/musa/'
            }
        }

        if (env.cmd.contains('mp_31')) {
            installMusaAsm(env.musaAsmPackageUrl)
            sh '''
                tar -xf musa_asm.tar.gz
                cd musa_asm/build/bin && cp musaasm /usr/local/musa/bin/
            '''
        }
    }
}

def build() {
    // String buildPath = "${env.WORKSPACE}/muDNN/build"
    dir(env.repo) {
        if (env.buildArch == 'x86' && env.containerImage.contains('ubuntu')) {
            def ccachedCmd = commonLib.ccachedCmd(ccachePath, '15G')
            sh """
                export PATH=/usr/local/musa/bin:/usr/local/bin:\${PATH}
                export LD_LIBRARY_PATH=/usr/lib/${gnu_dir}/musa/:/usr/lib/${gnu_dir}/:/usr/local/musa/lib:\${LD_LIBRARY_PATH}
                ${envExport}
                ${constants.genCondaActivate("${conda_name}")}
                chmod +x ./build.sh
                ${ccachedCmd}
                ./build.sh ${env.cmd}
            """
        }
        else {
            sh """
                export PATH=/usr/local/musa/bin:/usr/local/bin:\${PATH}
                export LD_LIBRARY_PATH=/usr/lib/${gnu_dir}/musa/:/usr/lib/${gnu_dir}/:/usr/local/musa/lib:\${LD_LIBRARY_PATH}
                ${envExport}
                cp /usr/local/gcc-9.3.0/lib64/libstdc++.* /usr/lib64
                ${constants.genCondaActivate("${conda_name}")}
                sed -i -e '/code_check()/,/^}/ {
                    s/cmd_check "clang-format-12"/# &/
                    s/cmd_check "clang-tidy-12"/# &/
                    s/cmd_check "black"/# &/
                    s/cmd_check "pre-commit"/# &/
                }' build.sh
                chmod +x ./build.sh
                ./build.sh ${env.cmd}
            """
    }
        sh '''
            cd build
            cat clang-tidy.txt ||:
        '''
}
    dir(mudnn_bench_repo) {
        sh """
            export PATH=/usr/local/musa/bin:/usr/local/bin:\${PATH}
            export LD_LIBRARY_PATH=/usr/lib/${gnu_dir}/musa/:/usr/lib/${gnu_dir}/:/usr/local/musa/lib:\${LD_LIBRARY_PATH}
            ${envExport}
            ${constants.genCondaActivate("${conda_name}")}
            sed -i \'11c\\MUDNN_ROOT=${env.WORKSPACE}/${env.repo}/build/mudnn\' env_mudnn.sh.example
            cat env_mudnn.sh.example
            . ${env.WORKSPACE}/${mudnn_bench_repo}/env_mudnn.sh.example
            ./bench.sh
            mv ./build/mudnn_bench/ ${env.WORKSPACE}/${env.repo}/build/mudnn/mudnn_bench/
        """
    }
    dir(env.repo) {
        if (env.cmd.contains('-g')) {
            sh """
                tar czf ${packageName} ./build
                mv ${packageName} ${env.WORKSPACE}/
            """
        } else {
            sh """
                cd build
                rm mudnn
                mv ./mp_*/mudnn ./
                tar czf ${packageName} ./mudnn
                mv ${packageName} ${env.WORKSPACE}/
            """
        }
    }
}

def upload() {
    if (env.packagePath) {
        artifact.upload(packageName, env.packagePath)
    } else {
        def repo = env.gitlabSourceRepoName ?: env.repo
        def branch = env.gitlabSourceBranch ?: env.branch
        def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
        artifact.upload(repo, branch, commitId, packageName)
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice, [
    pre: {
        if (env.runChoice == 'node' && env.buildArch == 'arm') {
            sh 'docker run --rm --privileged sh-harbor.mthreads.com/compute/qemu-user-static --reset -p yes'
        }
    },
    main: {
        def workflow = [
            'checkout': [closure: { fetchCode() }],
            'env-setup': [closure: { envSet() }],
            'build': [closure: { build() }],
            'upload-package': [closure: { upload() }],
        ]
        runPipeline(workflow)
    }
])
