@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
repo = 'mt-pfm-controller'

def fetchCode() {
    env.commitId = new git().fetchCode(repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    dir(repo) {
        if (isUnix()) {
            credentials.runWithCredential('SSH_GITLAB') {
                sh "${env.cmd}"
            }
        }else {
            bat "${env.cmd}"
        }
        oss.install()
        dir('build') {
            def bucket = env.gitlabActionType == 'PUSH' ? 'sw-build' : 'sw-pr'
            oss.cp("${env.pkgName}", "sh-moss/${bucket}/${repo}/${env.branch}/${env.commitId}/${env.commitId}_${env.pkgName}")
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [ closure: { fetchCode() } ],
        'build': [ closure: { build() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    ]

    runPipeline(workflow)
}
