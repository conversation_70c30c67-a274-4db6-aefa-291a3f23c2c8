@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - master
 * commitId (String) - ''
 * packageName (String) - mtcc-x86_64-linux-gnu-ubuntu.tar.gz
 * compileArgs (String) --build_type release
 * compileArgs (String) --build_type release
 * exports (Multiline String) default '', split by '\n'
 * compileArgs (String) --build_type release
 * compileParallel (String) -16
 * litUtTest (Choice) - 'false'
 * linuxDdkPackageUrl (String) - ''
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_compile:v1
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'mtcc'
envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muRANDPackageUrl = env.muRANDPackageUrl?.trim() ? env.muRANDPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = "${env.commitId}_${env.packageName}"
    mtccLitpackageName = env.mtccLitPackageName ? "${env.commitId}_${env.mtccLitPackageName}" : 'mtcc_lit.tar.gz'
}

def build() {
    // String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    dir(env.repo) {
        if (env.cmd) {
            try {
                sh """
                    ${envExport}
                    ${env.cmd}
                """
            } catch (exc) {
                if (env.cmd =~ 'clang-tidy') {
                    publishHTML(target:[
                        allowMissing:true,
                        alwaysLinkToLastBuild:false,
                        keepAll:true,
                        reportDir: "${env.WORKSPACE}/${env.repo}/llvmsrc",
                        reportFiles: 'clang.html',
                        reportName: 'clangTidyReport'
                    ])
                }
                throw exc
            }

            if (env.cmd =~ 'clang-tidy') {
                publishHTML(target:[
                    allowMissing:true,
                    alwaysLinkToLastBuild:false,
                    keepAll:true,
                    reportDir: "${env.WORKSPACE}/${env.repo}/llvmsrc",
                    reportFiles: 'clang.html',
                    reportName: 'clangTidyReport'
                ])
            }
        }else {
            sh """
                ${envExport}
                ./mtcc_build.py build --prefix=./mtcc ${env.compileArgs} -j${env.compileParallel}
                ./mtcc_build.py install && cp utils/install.sh build/
                tar czf build/${packageName} -C build mtcc/ install.sh
                tar -czvf ${mtccLitpackageName} build/lib/Target/MTGPU/ llvmsrc/llvm/lib/Target/MTGPU/ llvmsrc/ build/tools/clang/ build/test build/bin/llvm-lit build/bin/FileCheck build/bin/not build/bin/count
                mv ${mtccLitpackageName} build/
            """
            if (envExport.contains('export GCOV_TEST=ON')) {
                dir(env.WORKSPACE) {
                    sh 'pwd && ls -l && tar -czvf mtcc_src.tar.gz mtcc'
                }
            }
        }
    }
}

def litUtTest() {
    // String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    dir(env.repo) {
        sh """
            mkdir pkg
            tar -zxf build/${packageName} -C ./pkg
        """
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        sh """
            ${envExport}
            export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH
            ./mtcc_build.py test --lit 2>&1 | tee lit_run_log.txt
            cat lit_run_log.txt | grep \"lit success\"
        """
    }
}

def upload() {
    dir("${env.repo}/build") {
        if (env.rename_pkg) {
            sh "mv ${packageName} ${env.rename_pkg}"
            packageName = env.rename_pkg
        }
        commonLib.retryByRandomTime({
            if (env.packagePath) {
                artifact.upload("${packageName}", env.packagePath)
                artifact.upload("${mtccLitpackageName}", env.packagePath)
            } else {
                artifact.upload(env.repo, env.branch, env.commitId, "${packageName}")
                artifact.upload(env.repo, env.branch, env.commitId, "${mtccLitpackageName}")
            }
        }, 20)
    }
    if (envExport.contains('export GCOV_TEST=ON')) {
        dir(env.WORKSPACE) {
            artifact.upload('mtcc_src.tar.gz', env.packagePath)
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'build': [closure: { build() }],
    ]
    if (env.litUtTest.toBoolean()) { workflow['litUtTest'] = [closure: { litUtTest() }] }
    if (env.packageName) { workflow['upload'] = [closure: { upload() }] }
    runPipeline(workflow)
}
