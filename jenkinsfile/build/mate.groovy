@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repo = 'mate'
envExport = env.exports ? commonLib.parseExportVars(env.exports).join(';') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
    packageName = env.packageName ? "${env.commitId}_${env.packageName}" : 'mate.tar.gz'
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true, updateBuildDescription: true])
}

def setEnv() {
    // ddk
    if (env.linuxDdkPackageUrl.contains('deb')) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        dir('mt-ddk') {
            sh 'cp -r linux-ddk/usr/lib/x86_64-linux-gnu/* /usr/lib/x86_64-linux-gnu ||:'
        }
    }

    // musa_toolki || MUSA-Runtime + mtcc
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
    } else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
    }

    //muDNN
    if (env.muDNNPackageUrl) {
        constants.downloadAndUnzipPackage(env.muDNNPackageUrl)
        sh 'cd mudnn; ./install_mudnn.sh -i'
    }

    //mccl
    if (env.mcclPackageUrl) {
        constants.downloadAndUnzipPackage(env.mcclPackageUrl)
        sh 'cd mccl; ./install.sh -i'
    }
}

def installDependency() {
    // muAlg
    if (env.muAlgPackageUrl) {
        musa.installmuAlg(env.muAlgPackageUrl)
    } else {
        gitLib.fetchCode('muAlg', 'develop', null, [disableSubmodules: true, updateBuildDescription: true])
        dir('muAlg') { sh './mt_build.sh -i' }
    }

    // muThrust
    if (env.muThrustPackageUrl) {
        musa.installmuThrust(env.muThrustPackageUrl)
    } else {
        gitLib.fetchCode('muThrust', 'develop', null, [disableSubmodules: true, updateBuildDescription: true])
        dir('muThrust') { sh './mt_build.sh -i' }
    }

    // conda
    constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')

    // torch_musa
    constants.downloadPackage(env.torchPackageUrl)
    constants.downloadPackage(env.torchMusaPackageUrl)
    sh """
        ${constants.genCondaActivate(env.condaEnv)}
        pip install --force-reinstall ./torch*.whl
    """
}

def build() {
    dir(env.repo) {
        sh """
            ${constants.genCondaActivate(env.condaEnv)}
            ${envExport}
            ${env.compileCmd}
            tar -czf ${packageName} ./dist
        """
    }
}

def upload() {
    dir(env.repo) {
        if (env.packagePath) {
            artifact.upload(packageName, env.packagePath)
        } else {
            artifact.upload(env.repo, env.branch, env.commitId, packageName)
        }
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'env-setup': [closure: { setEnv() }],
        'install-rely': [closure: { installDependency() }],
        'build': [closure: { build() }],
        'upload-package': [closure: { upload() }]
    ]
    runPipeline(workflow)
}
