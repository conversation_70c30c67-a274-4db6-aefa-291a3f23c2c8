@Library('swqa-ci')
import org.swqa.tools.git
import org.swqa.tools.common

env.repo = 'mtcc'
env.baseUrl = constants.OSS.MOSS_URL_PREFIX

env.isReleaseBranch = false
env.isDebug = false
env.mtccPathinWddm = 'wddm\\mtcc\\Win'
env.wddmBuildType = 'release'
env.forceRebuild = false
env.isNeedBuild = true

def init() {
    env.ossRealPath = "${env.ossPath}/${env.ossRelPath}"
    if (env.cmd.contains('--dumps OFF') || env.cmd.contains('--dumps=OFF') || env.cmd.contains('-d OFF') || env.cmd.contains('-d=OFF')) {
        env.isReleaseBranch = true
        env.mtccPathinWddm = 'wddm\\mtcc\\Win_Release'
        env.wddmBuildType = 'release_branch'
    }
    if (env.cmd.contains('Debug')) {
        env.isDebug = true
        env.wddmBuildType = 'debug'
        if (env.isReleaseBranch.toBoolean()) {
            error 'Debug build is not supported for release branch'
        }
    }

    def packagee = readJSON text: env.package
    def packageName = packagee.name
    env.distPath = "${env.WORKSPACE}/${env.repo}/${packagee.path}"
    env.fullPackageName = "${packageName}.tar.gz"
    env.packageReName = "${env.prefix}${env.packageReName}.tar.gz"
    env.packagepdb64Name = "${env.prefix}${env.packageReName}_pdb64.tar.gz"
    env.packagepdb32Name = "${env.prefix}${env.packageReName}_pdb32.tar.gz"
    env.libgfxcPkgUrl = "${env.baseUrl}/${env.ossRealPath}/${env.packageReName}"

    if (env.forceRebuild.toBoolean()) {
        print('Force rebuild package')
        env.isNeedBuild = true
    } else {
        def checkUrlCommand = "curl -k --silent --head --fail ${env.libgfxcPkgUrl}"
        def urlExists = sh(script: checkUrlCommand, returnStatus: true) == 0
        if (!urlExists) {
            env.isNeedBuild = true
        } else {
            print("Package(${env.libgfxcPkgUrl}) already exists, skip build.")
            print("Note that if you want to rebuild package, please set 'forceRebuild' to true.")
            env.isNeedBuild = false
        }
    }
}

def fetchCode() {
    def parallelJobs = [:]
    def git = new git()
    if (env.isNeedBuild.toBoolean()) {
        parallelJobs['fetchCode mtcc'] = {
            env.mtccCommitId = git.fetchCode(env.repo, env.buildBranch, env.mtccCommitId, [updateBuildDescription: true])
            env.mtccCommitId = env.mtccCommitId[0..8]
        }
    }
    parallelJobs['fetch wddm'] = {
        env.wddmCommitId = git.fetchCode('wddm', env.buildWddmBranch, env.wddmCommitId, [preBuildMerge: false, disableSubmodules: true, shallow: false, updateBuildDescription: true])
        env.wddmCommitId = env.wddmCommitId[0..8]
        git.updateSubmodule('wddm', 1, 'shared_include mtcc')
    }
    parallel parallelJobs
}

def buildLibgfxc() {
    if (!env.isNeedBuild.toBoolean()) {
        return
    }
    if (isUnix()) {
        new common().runRelyNetwork(3, 10) {
            sh 'apt install -y libncurses5'
        }
    }
    env.shared_include_path = "${env.WORKSPACE}/wddm/shared_include"
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            sh "cd libgfxc && ${env.cmd}"
        }
    }
}

def buildDriverWddm(Map wddm_config, String driverKey) {
    def productPaths = wddm_config.product_paths
    def productModules = wddm_config.modules
    def wddmDriverName = "${env.prefix}${env.wddmDriverName}_${driverKey}"
    def driverPath = "${env.WORKSPACE}\\${wddmDriverName}"

    def parallelJobs = [:]
    def git = new git()

    dir(env.distPath) {
        if (!env.isNeedBuild.toBoolean()) {
            bat "wget -q ${env.libgfxcPkgUrl} -O ${env.packageReName} --no-check-certificate"
        }
        bat"""
            tar xvzf ${env.packageReName} -C ${env.WORKSPACE}\\${env.mtccPathinWddm}\\
            echo "" > ${env.WORKSPACE}\\${env.mtccPathinWddm}\\extracted.txt
        """
    }

    parallelJobs["Get wddm driver ${driverKey}"] = {
        def wddmPkgName = "${env.wddmCommitId}_wddm_${driverKey}.tar.gz"
        def wddmPkgURL = ''
        if (env.wddmUrl) {
            wddmPkgURL = "${env.wddmUrl}_${driverKey}.tar.gz"
        } else {
            wddmPkgURL = "${env.wddmBaseUrl}/${env.buildWddmBranch}/${env.wddmCommitId}/${env.wddmCommitId}_${driverKey}.tar.gz"
        }

        def checkUrlCommand = "curl -k --silent --head --fail ${wddmPkgURL}"
        new common().runRelyNetwork(3, 10) {
            def urlExists = sh(script: checkUrlCommand, returnStatus: true) == 0
            if (!urlExists) {
                error 'One or more URLs do not exist yet. Waiting...'
            }
            dir(driverPath) {
                bat"""
                    wget -q ${wddmPkgURL} -O ${wddmPkgName} --no-check-certificate
                    tar xvzf ${wddmPkgName} -C ${driverPath}\\
                """
            }
        }
    }

    parallelJobs['build mtdxum'] = {
        stage("mtcc build mtdxum ${driverKey}") {
            git.updateSubmodule('wddm', 1, 'mtdxum')
            winBuild.build_mtdxum('x64', 'sudi', env.wddmBuildType)
            winBuild.build_mtdxum('win32', 'sudi', env.wddmBuildType)
        }
    }
    parallelJobs['build dxc'] = {
        stage("build dxc ${driverKey}") {
            git.updateSubmodule('wddm', 1, 'dxc')
            winBuild.build_dxc(env.wddmBuildType, 'x64')
            winBuild.build_dxc(env.wddmBuildType, 'win32')
        }
    }
    parallelJobs['build ogl mtcc'] = {
        stage("ogl_mtcc build ${driverKey}") {
            git.updateSubmodule('wddm', 1, 'ogl')
            // not support debug and release_branch build for ogl
            winBuild.build_ogl(driverKey, 'x64', '-DBUILD_WITH_COMPILER_2_0=ON')
            winBuild.build_ogl(driverKey, 'x86', '-DBUILD_WITH_COMPILER_2_0=ON')
        }
    }

    parallel parallelJobs

    stage("upload libgfxc driver ${driverKey}") {
        dir(driverPath) {
            productModules.each { platform, modules ->
                modules.each { module ->
                    module.products.each { product ->
                        if (['mtdxum', 'dxc', 'ogl', 'mtcc'].contains(module.name.toLowerCase())) {
                            def modifiedSourceDir = productPaths[platform][module.name].release
                            if (product.contains('mticdfbg') || product.contains('mticdpxg')) {
                                modifiedSourceDir = 'wddm/ogl/imported/pre-binary-kmd/hw'
                            }

                            if (module.name.toLowerCase() == 'mtcc' && env.isReleaseBranch.toBoolean()) {
                                modifiedSourceDir = productPaths[platform][module.name].release_branch
                            }
                            modifiedSourceDir = modifiedSourceDir.replace('/', '\\')

                            def targetDir = '.'

                            if (product.toLowerCase().contains('pdb')) {
                                targetDir = 'symbols'
                            }

                            def sourceFilePath = "${env.WORKSPACE}\\${modifiedSourceDir}\\${product}"
                            if (!fileExists(sourceFilePath)) {
                                sourceFilePath = "${env.WORKSPACE}\\${modifiedSourceDir}\\symbols\\${product}"
                            }

                            bat """
                                del ${targetDir}\\${product}
                                xcopy /s /e /y ${sourceFilePath} ${targetDir}
                            """
                            if (module.name.toLowerCase() == 'mtcc') {
                                bat " xcopy /s /e /y ${WORKSPACE}\\${env.mtccPathinWddm}\\mustpass\\* ."
                            }
                        }
                    }
                }
            }
        }
        def mrWddmPkgName = "${wddmDriverName}.tar.gz"
        bat "tar -cvzf ${mrWddmPkgName} -C ${driverPath}\\  *"
        artifact.upload(mrWddmPkgName, env.ossRealPath)
    }
}

// build driver
def buildDriver() {
    if (!isUnix()) {
        def wddmConfig = null
        dir('wddm/ci') {
            wddmConfig = new common().loadPipelineConfig('CIConfig.yaml', '')
        }
        driverType = env.buildDriver.split(',')
        driverType.each { dtype ->
            buildDriverWddm(wddmConfig, dtype)
        }
    } else {
        error 'Not support yet'
    }
}

def mssymuploader() {
    def symUploaderUrl = 'https://sh-moss.mthreads.com/sw-build/ci_tool/symuploader.exe'
    def uploadUrl = 'https://sh-symstore.mthreads.com/upload/'
    def symUploaderPath = "${env.WORKSPACE}\\symUploaderPath"
    dir("${env.repo}\\libgfxc\\gfx_install") {
        bat """
            md ${symUploaderPath}\\
            tar -xf ${env.packageReName}
            for /r . %%f in (*.pdb) do (
                copy %%f ${symUploaderPath}\\
            )
        """
    }
    dir(symUploaderPath) {
        pdbExists = sh(script: 'ls |grep pdb', returnStatus: true) == 0
    }
    dir(env.repo) {
        if (pdbExists) {
            new common().runRelyNetwork(3, 10) {
                bat """
                    wget -q ${symUploaderUrl} --no-check-certificate
                    symuploader.exe -d ${symUploaderPath}\\ -u ${uploadUrl} -p release
                """
            }
        }
    }
}

def upload() {
    if (!env.isNeedBuild.toBoolean()) {
        return
    }
    dir(env.distPath) {
        sh "mv ${env.fullPackageName} ${env.packageReName}"
        artifact.upload(env.packageReName, env.ossRealPath)
    }
    if (!env.isMr.toBoolean() && !isUnix()) {
        mssymuploader()
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'init': [closure: { init() }],
        'checkout': [closure: { fetchCode() }, setGitlabStatus: env.isMr.toBoolean(), statusName: env.testLabel],
        'buildLibgfxc': [closure: { buildLibgfxc() }, setGitlabStatus: env.isMr.toBoolean(), statusName: env.testLabel],
        'upload': [closure: { upload() }, setGitlabStatus: env.isMr.toBoolean(), statusName: env.testLabel],

    ]
    if (env.buildDriver && !env.buildDriver.isEmpty()) {
        workflow['buildDriver'] = [closure: { buildDriver() }, setGitlabStatus: env.isMr.toBoolean(), statusName: env.testLabel]
    }

    runPipeline(workflow)
}
