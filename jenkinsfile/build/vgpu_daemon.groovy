@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'vgpu_daemon'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def buildAndUpload() {
    dir(env.repo) {
        oss.install()
        oss.cp('sh-moss/sw-build/vgpu_daemon/icon.ico', './deps/')
        oss.cp('sh-moss/sw-build/vgpu_daemon/icon.png', './deps/')
        sh 'ls -l ./deps'
        def pkgName = "${env.commitId}_${env.packageName}"
        def folderName = env.packageName.split('\\.')[0]
        sh 'python3 ./build.py'
        if (env.needUpload == 'true') {
            sh """
                mkdir ${folderName}
                cp ./output/vgpu_daemon_upgrade ./linux_conf/mtvgpu_daemon_upgrade.service ./${folderName}
                tar czf ${pkgName} ./${folderName}
            """
            artifact.upload(env.repo, env.branch, env.commitId, pkgName)
        }
    }
}

runner.start(env.runChoice, [
    pre: {
        if (env.isAarch64 == 'true') {
            println 'reset host'
            sh 'docker run --rm --privileged sh-harbor.mthreads.com/compute/qemu-user-static --reset -p yes'
        } else {
            println 'no need to reset host'
        }
    },
    main: {
        runPipeline([
            'fetch code': [ closure: { fetchCode() } ],
            'build': [ closure: { buildAndUpload() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
        ])
    }
])
