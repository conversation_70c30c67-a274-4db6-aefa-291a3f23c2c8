@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * repo (String) - mthreads-gmi
 * branch (String) - develop
 * commitId (String) - null
 * date (String) - null
 * branchReqMtml (String) - release_1.12,release_1.9_cmcc
 * clangTidyCmd (String) - null
 * packagePath (String) - null
 * testLabel (String) - jenkins/build
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/sdk/management:v10
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
*/

gitLib = new git()
commonLib = new common()
version = ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    dir(env.repo) {
        version = utils.runCommandWithStdout("egrep -A1 'gmi_version' ./config.ini | grep 'version = ' | awk -F '=' '{print \$2}'")
        def date = env.date ?: new Date().format('yyyy.MM.dd')
        version = env.gitlabTargetBranch == 'develop' ? date : version
        def architectures = ['sw64', 'loongarch64', 'i386', 'aarch64', 'arm32', 'x86_64']
        if (env.branchReqMtml.contains(env.gitlabTargetBranch)) {
            def mtmlPackageUrl = utils.runCommandWithStdout("curl --insecure http://sh-moss.mthreads.com/sw-build/mt-management/${env.gitlabTargetBranch}/latestUrl.txt")
            dir('sdk') {
                mtmlPackagePath = constants.downloadAndUnzipPackage(mtmlPackageUrl)
                sh "mkdir -p ${architectures.collect { "lib/${it}" }.join(' ')}"
                architectures.each { arch ->
                    sh "cp ${mtmlPackagePath}/RELEASE_${arch}_sdk/LINUX/${arch}/RELEASE/lib/libmtml.a lib/${arch}"
                }
            }
        }
        if (env.clangTidyCmd) {
            sh "${env.clangTidyCmd}"
        } else {
            architectures.each { arch ->
                def flag = (env.gitlabActionType == 'PUSH') ? 'NO' : (arch == 'x86_64' ? 'YES' : 'NO')
                def isRelease = (gitLib.triggeredByMR() && arch == 'x86_64') ? 'DEBUG' : 'RELEASE'
                sh "./build_ci.sh ${isRelease} ${arch} ${version} ${flag}"
            }
        }
    }
}

def upload() {
    dir(env.repo) {
        if (env.clangTidyCmd) {
            artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/clang.html')
            artifact.upload(env.repo, env.branch, env.commitId, 'build/clang-tidy/tidy.log')
        } else {
            def packageName = "${env.commitId}_${env.repo}_${version}.tar.gz"
            def targetPath = gitLib.triggeredByMR() ? 'build bin test/bin' : 'bin'
            sh "tar czf ${packageName} ${targetPath}"
            if (gitLib.triggeredByMR()) {
                artifact.upload(env.repo, env.branch, env.commitId, packageName)
            } else {
                def packageUrl = constants.genPackageUrl(env.gitlabSourceRepoName ?: env.repo, env.branch, env.commitId, packageName, true, true)
                artifact.upload(env.repo, env.branch, env.commitId, packageName)
                sh """
                    echo ${packageUrl} > latestUrl.txt
                    mc cp latestUrl.txt sh-moss/sw-build/mthreads-gmi/${env.gitlabTargetBranch ?: env.branch}
                """
            }
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }, setGitlabStatus: true, statusName: env.testLabel],
        'upload': [closure: { upload() }]
    ]
    runPipeline(workflow)
}
