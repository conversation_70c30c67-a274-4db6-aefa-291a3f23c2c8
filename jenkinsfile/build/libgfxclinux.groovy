@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - master
 * commitId (String) - ''
 * linuxDdkBranch (String) - master
 * linuxDdkCommitId (String) - ''
 * packageName (String) - libgfxc.tar.gz
 * exports (Multiline String) default '', split by '\n'
 * compileArgs (String) --build_type release
 * compileParallel (String) -16
 * litUtTest (Choice) - 'false'
 * linuxDdkPackageUrl (String) - ''
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_compile:v1
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'mtcc'
env.ccachePath = '/home/<USER>/libgfxc_ccache'

def fetchCode() {
    if (!env.libgfxcPackageUrl) {
        env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
        libgfxcpackageName = "${env.commitId}_${env.libgfxcpackageName}"
    }
    env.linuxDdkCommitId = gitLib.fetchCode('linux-ddk', env.linuxDdkBranch, env.linuxDdkCommitId)
    env.shared_include_path = "${env.WORKSPACE}/linux-ddk/shared_include"
    linuxDdkPackageName = "${constants.formatCommitID(env.commitId)}_${env.linuxDdkPackageName}"
}

def buildlibgfxc() {
    envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    if (isUnix()) {
        def ccachedCmd = new common().ccachedCmd(env.ccachePath, '50G')
        new common().retryByRandomTime({
            sh """
                apt install -y libncurses5
                ${ccachedCmd}
                ${envExport}
                export PATH=$PATH:${env.WORKSPACE}/mtcc/libgfxc/gfx/Linux/musa_compiler64_shared/bin
                export LD_LIBRARY_PATH=${env.WORKSPACE}/mtcc/libgfxc/gfx/Linux/musa_compiler64_shared/lib
            """
        }, 20)
    }
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            sh """
                cd libgfxc
                ${env.libgfxcBuildCmd} -s ${env.shared_include_path} --ccache --ccache_dir ${ccachePath}
            """
        }
    }
}

def buildlinuxDdk() {
    if (env.libgfxcPackageUrl) {
        constants.downloadAndUnzipPackage(env.libgfxcPackageUrl)
    }
    dir('linux-ddk') {
        if (env.libgfxcPackageUrl) {
            sh """
                rm -rf ./libgfxc/Linux/musa_compiler_shared.tar.gz ./libgfxc/Linux_Release/musa_compiler_shared.tar.gz
                cp -r ${env.WORKSPACE}/gfx/Linux/musa_compiler_shared.tar.gz ./libgfxc/Linux/
                cp -r ${env.WORKSPACE}/gfx/Linux_Release/musa_compiler_shared.tar.gz ./libgfxc/Linux_Release/
            """
        } else {
            sh """
                rm -rf ./libgfxc/Linux/musa_compiler_shared.tar.gz ./libgfxc/Linux_Release/musa_compiler_shared.tar.gz
                cp -r ${env.WORKSPACE}/${env.repo}/libgfxc/gfx/Linux/musa_compiler_shared.tar.gz ./libgfxc/Linux/
                cp -r ${env.WORKSPACE}/${env.repo}/libgfxc/gfx/Linux_Release/musa_compiler_shared.tar.gz ./libgfxc/Linux_Release/
            """
        }
        sh "${env.linuxDdkCmd}"
    }
}

def updategfxc() {
    gitLib.fetchCode(env.libgfxcPakcageRepo, env.libgfxcPakcagebBranch, '', [preBuildMerge: false, lfs: true, shallow: false, updateBuildDescription: true])
    dir(env.WORKSPACE) {
        new common().retryByRandomTime({
            sh "cp -r ${env.WORKSPACE}/${env.repo}/libgfxc/gfx/* ${env.WORKSPACE}/${env.libgfxcPakcageRepo}/"
        }, 20)
    }

    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.libgfxcPakcageRepo) {
            sh """
                git config --global user.email "<EMAIL>"
                git config --global user.name "git-robot"
                git status
                git lfs track "*.tar.gz" || :
                git add . || :
                git commit -m "daily build mtcc:${commitId} linux-ddk:${linuxDdkCommitId}" || :
            """
            new common().retryByRandomTime({
                sh """
                git lfs push origin ${env.libgfxcPakcagebBranch} || :
                git push  origin ${env.libgfxcPakcagebBranch} || :
                """
            }, 20)
        }
    }
}

def isSubmoduleExists(String submodule) {
    return utils.runCommandWithStatus("git config --file .gitmodules --get-regexp path | grep '${submodule}'") == 0
}

def shouldUpdateCaseList(caseListPath) {
    def submoduleName = caseListPath.split('/')[0]
    def shouldUpdate = false
    credentials.runWithCredential('SSH_GITLAB') {
        def submoduleExists = isSubmoduleExists(submoduleName)
        if (fileExists(caseListPath)) {
            shouldUpdate = true
        } else if (submoduleExists) {
            sh "git submodule update --init --depth 1 ${submoduleName}"
            shouldUpdate = fileExists(caseListPath)
        } else {
            println "⚠️ Submodule ${submoduleName} does not exist, skipping update for ${caseListPath}"
        }
    }
    return shouldUpdate
}

def uploadLinuxDdk() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.branch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    gitLib.fetchCode('mt-gfx-test', 'master', null, [disableSubmodules: true, updateBuildDescription: true])
    oss.install()
    def ossPackagePath = constants.genOssPath(repo, branch, commitId)
    def ossAlias = constants.genOssAlias()
    def caseOssPath = "${ossAlias}/${ossPackagePath}/test/cts/"
    dir('mt-gfx-test/VK-GL-CTS_caselist/linux') {
        oss.cp('gles/gles_cts_pr_ddk2.txt', caseOssPath)
        oss.cp('gles/gles_cts_pr_ddk2_m3d.txt', caseOssPath)
        oss.cp('gles/gles_cts_pr_newapi_mtcc.txt', caseOssPath)
        oss.cp('vulkan/vk_cts_pr_ddk2.txt', caseOssPath)
    }
    dir('linux-ddk') {
        if (shouldUpdateCaseList('vulkan/ci/linux')) {
            oss.cp('vulkan/ci/linux/cts_ci_linux.txt', caseOssPath)
        }
    }
    commonLib.retryByRandomTime({
        sh "tar -czf ${linuxDdkPackageName} linux-ddk/build/"
        if (env.packagePath) {
            artifact.upload("${linuxDdkPackageName}", env.packagePath)
        } else {
            artifact.upload(repo, branch, commitId, "${linuxDdkPackageName}")
        }
    }, 20)
}

def uploadlibgfxc() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.branch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    commonLib.retryByRandomTime({
        dir("${env.WORKSPACE}/${env.repo}/libgfxc/") {
            sh "tar -czf ${libgfxcpackageName} gfx/"
            if (env.packagePath) {
                artifact.upload("${libgfxcpackageName}", env.packagePath)
            } else {
                artifact.upload(repo, branch, commitId, "${libgfxcpackageName}")
            }
        }
    }, 20)
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
    ]
    if (!env.libgfxcPackageUrl) {
        workflow['buildlibgfxc'] = [closure: { buildlibgfxc() }]
    }
    if (env.uploadlibgfxc == 'true') {
        workflow['uploadlibgfxc'] = [closure: { uploadlibgfxc() }]
    }
    if (env.linuxDdkBuild == 'true') {
        workflow['buildlinuxDdk'] = [closure: { buildlinuxDdk() }]
        workflow['uploadLinuxDdk'] = [closure: { uploadLinuxDdk() }]
    }
    if (env.updategfxc == 'true') {
        workflow['updategfxc'] = [closure: { updategfxc() }]
    }
    runPipeline(workflow)
}
