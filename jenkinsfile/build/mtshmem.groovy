@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * packageName (String) - mtshmem.tar.gz
 * linuxDdkPackageUrl (String) - ''
 * mtccPackageUrl (String) - ''
 * musaToolkitsPackageUrl(String) - 'https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/musa_toolkits_install_full.tar.gz'
 * mtmlPackageUrl (String) - 'https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/mccl_mtshmem_rely/79e98a02a_mtml_2.2.0-linux-R_amd64.deb'
 * exports (Multiline String) default '', split by '\n'
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/ubuntu-22-04-mtshmem-build-image:v0
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'mtshmem'
env.branch = env.mtshmemBranch ? env.mtshmemBranch : env.branch
env.commitId = env.mtshmemBranch ? '' : env.commitId

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_${env.packageName}"
}

def envSetUp() {
    apt.installPackage('libcunit1 openmpi-bin libopenmpi-dev')
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    if (env.mtccPackageUrl) {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }
    constants.downloadPackage(env.mtmlPackageUrl)
    sh "dpkg -i ${env.mtmlPackageUrl.split('/').last()}"
}

def build() {
    String envExport = env.exports ? commonLib.parseExportVars(env.exports).join(';') : ''
    dir(env.repo) {
        sh """
            ${envExport}
            ${env.cmd}
            export CPACK_THREADS=\$(nproc)
            cpack -G TGZ
            mkdir -p pkg
            mv libmtshmem*.tar.gz ./pkg/
            cd pkg
            tar -xzvf libmtshmem*.tar.gz
            rm -rf libmtshmem*.tar.gz
            mv libmtshmem* mtshmem
            cd mtshmem
            chmod u+x install.sh
            cd ..
            tar -zcf ${packageName} mtshmem
            mv ${packageName} ${env.WORKSPACE}/
        """
    }
}

def upload() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.branch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    if (env.packagePath) {
        artifact.upload(packageName, env.packagePath)
    } else {
        artifact.upload(repo, branch, commitId, packageName)
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }],
    ]
    runPipeline(workflow)
}
