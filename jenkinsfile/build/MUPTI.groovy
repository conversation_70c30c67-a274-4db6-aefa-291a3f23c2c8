@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * branch (String) - master
 * buildBranch (Choice) - master [master | release_KUAE_2.0_for_PH1_M3D]
 * commitId (String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * exports (Multiline String) - LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:\${LD_LIBRARY_PATH};
 * linuxDdkPackageUrl (String) - https://sh-moss.mthreads.com/sw-build/linux-ddk/release_KUAE_2.0_for_PH1_M3D/f0a99fc9e/xxx.deb
 * packageName (String) - MUPTI
 * compileParallel (String) - 16
 * compileArgs (String) - -DCMAKE_BUILD_TYPE=Release
 * compileTargetArch (Choice) - x86 [x86 | arm]
 * packagePath (String) - ''
 * triggerInfo (String) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_compile:v1
 * podNodeSelector (String) - In=mt=buildserver
 * podResources (String) - requests=cpu=20;requests=memory=64Gi;limits=cpu=20;limits=memory=64Gi
*/

gitLib = new git()
commonLib = new common()

env.repo = 'MUPTI'

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def deploy() {
    apt.installPackage('dkms')
    if (env.linuxDdkPackageUrl.startsWith('oss')) {
        linuxDdkPackageUrl = constants.ossPathToUrl(env.linuxDdkPackageUrl)
    }
    ddk.installLinuxDdk(linuxDdkPackageUrl)

    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
    } else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        } else {
            error('no musa-runtime pkg!')
        }
    }

    if (env.usingLatestMtperf == 'true') {
        latest_mt_perf = sh(returnStdout: true, script: 'wget -qO- http://sh-moss.mthreads.com/sw-build/mt-perf/develop/latest.txt --no-check-certificate').trim()
        def branchInfo = env.gitlabTargetBranch ?: env.buildBranch
        if (branchInfo == 'master' && env.compileTargetArch == 'x86') {
            latest_mt_perf += '_linux_x86_64.tar.gz'
        } else if (branchInfo == 'master' && env.compileTargetArch == 'arm') {
            latest_mt_perf += '_linux_aarch64.tar.gz'
        } else if (branchInfo == 'release_KUAE_2.0_for_PH1_M3D' && env.compileTargetArch == 'x86') {
            latest_mt_perf += '_linux_x86_64_kuae.tar.gz'
        } else if (branchInfo == 'release_KUAE_2.0_for_PH1_M3D' && env.compileTargetArch == 'arm') {
            latest_mt_perf += '_linux_aarch64_kuae.tar.gz'
        } else {
            print('failed get mt-perf')
        }
        print(latest_mt_perf)
        sh "wget ${latest_mt_perf} --no-check-certificate"
        sh 'tar -xvf ./*mt-perf*.tar.gz'
        sh 'mkdir /usr/local/mt-perf'
        sh 'cp -r ./*mt-perf*/* /usr/local/mt-perf'

        compileArgs = env.compileArgs +  ' -DMT_PERF_PATH=/usr/local/mt-perf/'
    }
}

def compile() {
    List envExport = env.exports ? commonLib.parseExportVars(env.exports) : []
    credentials.runWithCredential('SSH_GITLAB') {
        def scripts = readJSON(text: env.scripts)
        dir(env.repo) {
            utils.runScripts(envExport + scripts)
        }
    }
}

def upload() {
    def artifacts = readJSON(text: env.artifacts)
    artifact.upload(artifacts)
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'deploy': [closure: { deploy() }],
        'build': [closure: { compile() }],
        'upload-package': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
