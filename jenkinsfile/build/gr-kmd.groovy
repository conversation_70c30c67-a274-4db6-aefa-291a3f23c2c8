@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - null
 * packageName (String) - x86_64-mtgpu_linux-xorg-release-hw
 * cmd (String) - ./kmd_build.sh -w xorg -p mtgpu_linux -d 0 -o hw -b release -g deb -j8
 * exports (Multiline String)
    'KERNELVER=5.4.0-42-generic'
    'KERNELDIR=/lib/modules/5.4.0-42-generic/build'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v57
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
*/

commonLib = new common()
gitLib = new git()
env.kmdRepo = 'gr-kmd'

def fetchCode() {
    //env.kmdBranch is for submodule MR with multi-repository dependencies
    env.kmdBranch = commonLib.findMrDependency(env.kmdRepo, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: '')) ?: env.kmdBranch
    if (env.gitlabSourceRepoName == 'gr-kmd' || env.repo == 'gr-kmd') {
        env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
    } else {
        env.kmdCommit = gitLib.fetchCode(env.kmdRepo, env.kmdBranch, env.kmdCommit, [updateBuildDescription: true])
    }
    dir(env.kmdRepo) {
        Map baseMap = [
        'mt-rm'          : 'mt-rm',
        'mt-audio-driver': 'mtsnd',
        'mt-video-drv'   : 'mtvpu',
        'shared_include' : 'shared_include'
        ]
        Map userConfig = readJSON text: env.submoduleConfig
        Map mergedMap = baseMap + userConfig
        mergedMap.each { submodule, submoduleRename ->
            if (env.gitlabSourceRepoName == submodule) {
                sh "rm -rf ${submoduleRename}"
                env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
                if (submodule != submoduleRename) { sh "mv ${submodule} ${submoduleRename}" }
            }
            def newCommit = commonLib.findMrDependency(submodule, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
            if (newCommit) {
                sh "rm -rf ${submoduleRename}"
                gitLib.fetchCode(submodule, newCommit, null, [updateBuildDescription: true])
                if (submodule != submoduleRename) { sh "mv ${submodule} ${submoduleRename}" }
            }
        }
    }

    sh """
        cp -rf ${env.kmdRepo} /root/
        ls -l /root ||:
    """
// check if this commit is the latest, and set updateLatest to true/false
}

def build() {
    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    String distPath = "/root/${env.kmdRepo}/dist"
    credentials.runWithCredential('SSH_GITLAB') {
        def buildCmd = env.clangTidyCmd ? "bear ${env.cmd}" : env.cmd
        sh """
            cd /root/${env.kmdRepo}
            ${envExport}
            ${buildCmd} -i ${distPath}
            ${env.cmd} -i ${distPath} install
        """
        if (env.clangTidyCmd) { sh "cd /root/${env.kmdRepo}; ${env.clangTidyCmd}" }
        if (env.runCppcheck == 'yes') {
            gitLib.setGitlabStatus('cppcheck', 'running')
            sh """
                cd /root/${env.kmdRepo}
                echo 'cppcheck'
                ls -la ./
                cd ./binary_*/target_*/mtgpu
                /root/cppcheck-1.89/cppcheck --version
                /root/cppcheck-1.89/cppcheck src/ > /root/${env.kmdRepo}/check.log 2>&1
                cat /root/${env.kmdRepo}/check.log
            """
            def checkData = sh(script: "cat /root/${env.kmdRepo}/check.log | grep '(error)' | wc -l", returnStdout: true).trim()
            if (checkData != '0') {
                gitLib.setGitlabStatus('cppcheck', 'failed')
                throw new Exception('build failed for cppcheck error!')
            } else {
                gitLib.setGitlabStatus('cppcheck', 'success')
            }
        }
        if (env.checkKernelHeader == 'yes') {
            gitLib.setGitlabStatus('jenkins/kernel_header_check', 'running')
            sh 'echo checkKernelHeader'
            def script = """
                cd /root/${env.kmdRepo}/scripts
                ./header_check.sh
            """
            def output = sh(script: script, returnStdout: true).trim()
            log.info(output)
            if (output.contains('ERROR')) {
                gitLib.setGitlabStatus('jenkins/kernel_header_check', 'failed')
                throw new Exception('build failed for check kernel header error!')
            } else {
                gitLib.setGitlabStatus('jenkins/kernel_header_check', 'success')
            }
        }
    }
// if updateLatest is true, upload package to env.ossPath
}

def upload() {
    String packageName = "${env.commitId ?: env.kmdCommit}_${env.packageName}"
    String distPath = "/root/${env.kmdRepo}/dist"
    String folderName = sh(script: "ls ${distPath}", returnStdout: true).trim()
    sh """
        cd ${distPath}
        tar czf ${packageName}.tar.gz ${folderName}
    """
    artifact.upload(env.repo, env.branch, env.commitId, "${distPath}/${packageName}.tar.gz")
    if (env.cmd =~ '-g deb') {
        sh """
            cd /root/${env.kmdRepo}/binary_*/target_*/
            cp mtgpu*.deb ${distPath}/${packageName}.deb
        """
        artifact.upload(env.repo, env.branch, env.commitId, "${distPath}/${packageName}.deb")
    } else if (env.cmd =~ '-g rpm') {
        sh """
            cd /root/${env.kmdRepo}/binary_*/target_*/
            cp mtgpu*.rpm ${distPath}/${packageName}.rpm
        """
        artifact.upload(env.repo, env.branch, env.commitId, "${distPath}/${packageName}.rpm")
    } else if (env.cmd =~ '-g kylin') {
        sh """
            cd /root/${env.kmdRepo}/binary_*/target_*/
            zip -r ${packageName}.kylin.zip mtgpu
            mv ${packageName}.kylin.zip ${distPath}/
        """
        artifact.upload(env.repo, env.branch, env.commitId, "${distPath}/${packageName}.kylin.zip")
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
