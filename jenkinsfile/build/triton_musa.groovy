@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

// nodeLabel runChoice branch commitId umdPackageUrl triggerInfo compileArgs compileParallel packageName

gitLib = new git()
commonLib = new common()
env.repo = 'triton_musa'

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muRANDPackageUrl = env.muRANDPackageUrl?.trim() ? env.muRANDPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)

    try {
        if (env.packagePath && !env.packagePath.endsWith('others')) {
            utils.appendVersionInfo(env.repo, env.branch, env.commitId, "${env.packagePath}/others", 'musa_sdk_commit.txt')
        }
    } catch (e) {
        println "追加版本信息文件到OSS失败: ${e.getMessage()}"
    }
}

def build() {
    dir("${env.repo}") {
        def envExport = env.exports ? commonLib.parseExportVars(env.exports) : []

        def script = []
        if (env.cmd?.trim()) {
            script += readJSON(text: env.cmd)
        }

        if (env.buildScripts?.trim()) {
            script += readJSON(text: env.buildScripts)
        }

        script = envExport + script
        utils.runScripts(script)
    }
}

def buildTest() {
    if (env.testUtScipts?.trim()) {
        dir("${env.repo}") {
            def envExport = env.exports ? commonLib.parseExportVars(env.exports) : []
            def script = []
            if (env.cmd?.trim()) {
                script += readJSON(text: env.cmd)
            }

            if (env.buildScripts?.trim()) {
                script += readJSON(text: env.testUtScipts)
            }

            script = envExport + script
            utils.runScripts(script)
        }
    }
}

def upload() {
    dir("${env.repo}/python") {
        if (env.packagePath) {
            artifact.upload(env.packageName, "${env.packagePath}")
        } else {
            def repo = env.gitlabSourceRepoName ?: env.repo
            def branch = env.gitlabSourceBranch ?: env.branch
            def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
            artifact.upload(repo, branch, commitId, env.packageName)
        }
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice, [
    pre: {
        if (env.compileTargetArch == 'arm') {
            println 'reset host'
            sh 'docker run --rm --privileged sh-harbor.mthreads.com/compute/qemu-user-static --reset -p yes'
        }
    },
    main: {
        def workflow = [
                'checkout': [closure: { fetchCode() }],
                'build': [closure: { build() }, maxWaitTime: [time: env.timeTolerance, unit: 'MINUTES']],
                'buildTest': [closure: { buildTest() }, maxWaitTime: [time: env.timeTolerance, unit: 'MINUTES']],
                'upload': [closure: { upload() }]
            ]
        runPipeline(workflow)
    }
])
