@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

commonLib = new common()
gitLib = new git()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [disableSubmodules: false])
}

def setupEnv() {
    // install wddm latest driver
    ddk.installWinDriver()
}

def build() {
    dir(env.repo) {
        bat """
            ${env.cmd}
        """
    }
}

def upload() {
    dir(env.repo) {
        if (env.packageName) {
            if (fileExists(env.packageName)) {
                artifact.upload(env.repo, env.branch, env.commitId, env.packageName)
            } else {
                echo "The file ${env.packageName} does not exist, skipping upload steps."
            }
        }
    }

    //utils.catchErrorContinue { deleteDir() }
    sh "rm -rf ${env.WORKSPACE}/*"
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'setup env': [closure: { setupEnv() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    ]

    runPipeline(workflow)
}
