@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

commonLib = new common()
gitLib = new git()

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [disableSubmodules: false])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        sh '''
            apt update ||:
            apt install dkms -y ||:
        '''
        def linuxDdkPackageUrl = env.linuxDdkPackageUrl ?: constants.genLatestPackageUrl('linux-ddk', 'master', 'ddk2.0.deb')
        ddk.installLinuxDdkAndSetup(linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    // install umd
    //ddk.installUmd()
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
}

def runPerfBuild() {
    credentials.runWithCredential('SSH_GITLAB') {
        timeout(env.TIMEOUT) {
            dir(env.repo) {
                sh """
                    ${env.cmd}
                """
                if (env.coverageReportPath) {
                    commonLib.publishHTML(env.coverageReportPath, '*.html', 'Coverage Report')
                }
            }
        }
    }
}

def upload() {
    dir(env.repo) {
        if (env.packageName) {
            if (fileExists(env.packageName)) {
                artifact.upload(env.repo, env.branch, env.commitId, env.packageName)
            } else {
                echo "The file ${env.packageName} does not exist, skipping upload steps."
            }
        }
    }

    //utils.catchErrorContinue { deleteDir() }
    sh "rm -rf ${env.WORKSPACE}/*"
}

runner.start(env.runChoice, [main: {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpinDocker() }],
        'perf build': [closure: { runPerfBuild() }],
    ]

    workflow['upload'] = [closure: { upload() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    runPipeline(workflow, [disablePost: true])
}, pre: {
    runPipeline([
        'setup on node': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
