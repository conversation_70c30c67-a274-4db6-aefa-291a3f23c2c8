@Library('swqa-ci')

import org.swqa.tools.git

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * packageName (String) - x86_64-mtgpu_linux-xorg-release-hw
 * cmd (String) - ./umd_build.sh -w xorg -p mtgpu_linux -r 1.0.0.0 -d 0 -o hw -b release -j9
 * exports (Multiline String) default '', split by '\n'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

env.repo = 'gr-umd'

def fetchCode() {
    env.commitId = new git().fetchCode(env.repo, branch, commitId, [updateBuildDescription: true, submoduleShallow: false])
    sh """
        cp -rf ${env.repo} /
        ls -l / ||:
    """
}

def build() {
    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    String distPath = "/${env.repo}/dist"
    credentials.runWithCredential('SSH_GITLAB') {
        sh """
            cd /${env.repo}
            ${envExport}
            ${env.cmd} -i ${distPath}
            ${env.cmd} -i ${distPath} install
        """
    }
// if updateLatest is true, upload package to env.ossPath
}

def upload() {
    String packageName = "${env.commitId}_${env.packageName}"
    String distPath = "/${env.repo}/dist"
    String folderName = sh(script: "ls ${distPath}", returnStdout: true).trim()
    sh """
        cd ${distPath}
        tar czf ${packageName}.tar.gz ${folderName}
    """
    if (folderName.contains('debug')) {
        artifact.upload(env.repo, env.branch, env.commitId, "${distPath}/${packageName}.tar.gz")
    } else {
        artifact.upload(env.repo, env.branch, env.commitId, "${distPath}/${packageName}.tar.gz")
        String ossPackagePath = constants.genOssPath(env.repo, env.branch, env.commitId)
        // String ossPackagePath = "${ossBranchPath}/${commitId}"
        utils.uploadDbgSym("${distPath}/${folderName}", 'sh-moss/dependency/debug-symbols/scripts/upload_umd_dbgsym')
        sh """
            cd ${distPath}
            tar czf ${packageName}.tar.gz ${folderName}
            mc cp ${packageName}.tar.gz sh-moss/${ossPackagePath}/
            tar czf ${packageName}_dbg.tar.gz /${env.repo}/binary_*/*/*.dbg
            mc cp ${packageName}_dbg.tar.gz sh-moss/${ossPackagePath}/
        """
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
