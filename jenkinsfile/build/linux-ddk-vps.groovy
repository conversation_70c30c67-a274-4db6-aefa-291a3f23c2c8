@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()
repo_ddk = 'linux-ddk'
repo_musa_cts = 'musa_cts'
repo_mtcc_test = 'mtcc_test'

WORKTMP = '/home/<USER>'

// 根据 MTGPU_ARCH 的值映射到对应的 device
deviceMap = [
    'mp_31': 'ph1',
    'mp_32': 'ph1s',
    'mp_41': 'hg',
    'mp_42': 'hs',
]

// 获取 MTGPU_ARCH 参数值
MTGPU_ARCH = env.MTGPU_ARCH
echo "******** MTGPU_ARCH: ${MTGPU_ARCH} ********"
// 根据映射关系确定 device 的值
device = deviceMap.get(MTGPU_ARCH, 'mp_32')
echo "******** device: ${device} ********"

def fetchCode() {
    commitId_linux_ddk = gitLib.fetchCode(repo_ddk, env.branch_ddk, env.commitId)
    if (env.build_musa_cts == 'true') {
        commitId_musa_cts = gitLib.fetchCode(repo_musa_cts, env.branch_musa_cts)
    }
    if (env.build_mtcc_test == 'true') {
        commitId_mtcc_test = gitLib.fetchCode(repo_mtcc_test, env.branch_mtcc_test)
    }
}

def build() {
    // 从 env.cmd 中提取 -p 参数的值
    def platform = 'unknown'
    def cmdParts = env.cmd.tokenize()
    def pIndex = cmdParts.indexOf('-p')
    if (pIndex != -1 && pIndex + 1 < cmdParts.size()) {
        platform = cmdParts[pIndex + 1]
    }

    // 构建最终包名，添加平台后缀
    packageName = "${commitId_linux_ddk}_${platform}_${env.packageName}"

    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    credentials.runWithCredential('SSH_GITLAB') {
        echo '******** build linux-ddk ********'
        echo "******** ${env.WORKSPACE} ********"
        sh """
            pwd && ls -l
            echo ${env.WORKSPACE} ||:
            cp -r ${env.WORKSPACE} ${WORKTMP}
            ls -l ${WORKTMP}
            cd ${WORKTMP}/${repo_ddk}
            pwd && ls -l
            ${envExport}
            ${env.cmd}
            pwd
        """
        sh "cd ${WORKTMP} && tar czvf ${packageName} ${repo_ddk}/build"

        if (env.build_musa_cts == 'true' || env.build_mtcc_test == 'true') {
            // 安装linux-ddk
            sh """
                cd ${WORKTMP}
                rm -rf /usr/local/musa*
                cd ${repo_ddk}/build
                ./install.sh -a 0 -m 1 -l 1
            """
            // 安装mtcc
            def dependencies = ['mtcc': env.mtccPackageUrl]
            installDependency(dependencies)
            // 安装muAlg
            if (env.muAlgPackageUrl) {
                musa.installmuAlg(env.muAlgPackageUrl)
            }
            // 安装muThrust
            if (env.muThrustPackageUrl) {
                musa.installmuThrust(env.muThrustPackageUrl)
            }
            // 安装musify
            if (env.musifyPackageUrl) {
                musa.installMusify(env.musifyPackageUrl)
            }
            // 安装miniforge
            constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')

            constants.downloadAndUnzipPackage(env.musaAsmPackageUrl)
            sh '''
                cd ${WORKTMP}
                cd musa_asm/build/bin
                cp musaasm /usr/local/musa/bin/
            '''

            sh """
                cd ${WORKTMP}
                wget -q --no-check-certificate ${env.mtcToolUrl} -O mtc_tool
                chmod +x mtc_tool
                cp mtc_tool /usr/local/bin/
            """

            if (env.build_musa_cts == 'true') {
                echo '******** build musa_cts ********'
                // sh """
                //     ${envExport}
                //     ls -l /home/<USER>/
                //     . /home/<USER>/miniforge/etc/profile.d/conda.sh && conda activate mathx
                //     cd ${WORKTMP}/musa_cts/
                //     make install-all-deps
                //     make porting-cuda-to-musa
                //     sleep 12000
                //     make build-all

                //     cd ${WORKTMP}/musa_cts/pytest/test_mtcc
                //     mkdir -p ${WORKTMP}/musa_cts/log
                //     pytest -v -n 48 test_musa_mtcc.py test_musa_mtcc_fuzz.py ||:
                //     # cd ${WORKTMP}/musa_cts/mtcc/demo
                //     # mcc -o axpy axpy.cu -lmusart --cuda-gpu-arch=${MTGPU_ARCH} -mtgpu -O2
                // """
                sh """
                    cd ${WORKTMP}
                    ${envExport}
                    apt-get install -y pciutils --fix-missing
                    . /home/<USER>/miniforge/etc/profile.d/conda.sh && conda activate mathx
                    cd ${WORKTMP}/musa_cts/scripts && ./porting_without_compile.sh > /dev/null
                    # 编译musa_cts
                    mkdir -p ${WORKTMP}/musa_cts/build
                    cd ${WORKTMP}/musa_cts/build
                    cmake .. -D ENABLE_HIP_TEST=OFF
                    make -j48
                    # 编译musa_samples
                    cd ${WORKTMP}/musa_cts/musa_samples
                    ./compile_elf.sh --cuda-gpu-arch=${MTGPU_ARCH}
                    rm -rf build
                    mkdir -p build
                    cd build
                    cmake .. -D ENABLE_MCC=ON
                    make -j48
                    # 编译elf
                    cd ${WORKTMP}/musa_cts/elf
                    ./compile_elf.sh --cuda-gpu-arch=${MTGPU_ARCH}
                    # 编译test_mtcc
                    cd ${WORKTMP}/musa_cts/pytest/test_mtcc
                    mkdir -p ${WORKTMP}/musa_cts/log
                    pytest -v -n 24 test_musa_mtcc.py test_musa_mtcc_fuzz.py ||:
                    cd ${WORKTMP}/musa_cts/
                    rm -rf .git* mtcc/mtcc_test
                """

                sh "cd ${WORKTMP} && tar czvf ${commitId_musa_cts}_musa_cts.tar.gz musa_cts"
            }
            if (env.build_mtcc_test == 'true') {
                echo '******** build mtcc_test ********'
                sh """
                    cd ${WORKTMP}
                    ${envExport}
                    ls -l /home/<USER>/
                    . /home/<USER>/miniforge/etc/profile.d/conda.sh && conda activate mathx
                    cd ${WORKTMP}/mtcc_test/tools && ./porting_for_ci.sh > /dev/null
                    mkdir -p ${WORKTMP}/mtcc_test/compile_log
                    cd ${WORKTMP}/mtcc_test/
                    lit.py cuda/ testsuit/ -Ddevice=${device} -Dplatform=vps -Dtag=daily --show-tests > temp.list
                    cat temp.list | grep ':: ' | cut -d' ' -f5 > test_case.list
                    lit.py @test_case.list -Ddevice=${device} -Dplatform=vps -Dtag=daily -Dtest_run=false -j32 -o \$TEMP_LOG  ||:
                    cd ${WORKTMP}/mtcc_test/
                    rm -rf .git*
                """
                sh "cd ${WORKTMP} && tar czvf ${commitId_mtcc_test}_mtcc_test.tar.gz mtcc_test"
            }
        }
    }
}

def upload() {
    sh 'pwd && ls -l'
    if (env.packagePath) {
        artifact.upload("${WORKTMP}/${packageName}", env.packagePath)
        if (env.build_musa_cts == 'true') {
            artifact.upload("${WORKTMP}/${commitId_musa_cts}_musa_cts.tar.gz", env.packagePath)
        }
        if (env.build_mtcc_test == 'true') {
            artifact.upload("${WORKTMP}/${commitId_mtcc_test}_mtcc_test.tar.gz", env.packagePath)
        }
    } else {
        echo '******** Please input packagePath! ********'
    }
}

runner.start(env.runChoice) {
    def workFlow = [
        'fetchCode': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }]
    ]
    runPipeline(workFlow)
}
