@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

properties([
    parameters([
        choice(name: 'runChoice',
            choices: ['node', 'pod'],
            description: 'Run environment choice'),
        string(name: 'linuxDdkPackageUrl',
            defaultValue: '',
            trim: true,
            description: 'HTTP URL of the deb package to download and sign firmware in'),
        string(name: 'nodeLabel',
            defaultValue: 'Ubuntu22.04',
            trim: true,
            description: 'Node label for execution'),
        string(name: 'containerImage',
            defaultValue: 'sh-harbor.mthreads.com/qa/ubuntu:20.04',
            trim: true,
            description: 'Container image for pod execution'),
        string(name: 'firmwarePattern',
            defaultValue: 'mtfw-gen*.bin',
            trim: true,
            description: 'Pattern to match firmware files'),
        string(name: 'excludePattern',
            defaultValue: 'model',
            trim: true,
            description: 'Pattern to exclude from firmware files')
    ])
])

def validateParameters() {
    if (!env.linuxDdkPackageUrl || env.linuxDdkPackageUrl.trim().isEmpty()) {
        error 'linuxDdkPackageUrl parameter is required'
    }

    if (!env.linuxDdkPackageUrl.startsWith('http://') && !env.linuxDdkPackageUrl.startsWith('https://')) {
        error 'linuxDdkPackageUrl must be a valid HTTP/HTTPS URL'
    }

    echo 'Validated parameters:'
    echo "  - Linux DDK Package URL: ${env.linuxDdkPackageUrl}"
    echo "  - Firmware Pattern: ${env.firmwarePattern}"
    echo "  - Exclude Pattern: ${env.excludePattern}"
}

def downloadDebPackage() {
    echo "Downloading deb package from: ${env.linuxDdkPackageUrl}"

    def originalDebName = env.linuxDdkPackageUrl.split('/')[-1]
    if (!originalDebName.endsWith('.deb')) {
        error "URL does not point to a .deb file: ${env.linuxDdkPackageUrl}"
    }

    // Download the deb package
    sh """
        wget --no-check-certificate -O original.deb "${env.linuxDdkPackageUrl}"
        ls -la original.deb
    """

    echo 'Successfully downloaded deb package: original.deb'
    return 'original.deb'
}

def signFirmwareInDebPipeline() {
    validateParameters()

    def debFile = downloadDebPackage()

    // Generate signed package name based on original name
    def originalUrl = env.linuxDdkPackageUrl
    def originalFileName = originalUrl.split('/')[-1]
    def signedPackageName = originalFileName.replace('.deb', '.signed.deb')

    echo "Original file name: ${originalFileName}"
    echo "Signed package name: ${signedPackageName}"

    // Login to MTBios signing service
    credentials.runWithCredential('LDAP-SWCI') {
        mtbiosSign.login(USERNAME, PASSWORD, 'SW')

        // Use the mtbiosSign.signFirmwareInDeb method to sign firmware and repackage
        def results = mtbiosSign.signFirmwareInDeb(
            debFile,
            signedPackageName,
            env.firmwarePattern,
            env.excludePattern
        )

        echo 'Firmware signing completed!'
        echo "Total files found: ${results.totalFiles}"
        echo "Successfully signed: ${results.signedCount}"
        echo "Failed to sign: ${results.failedFiles.size()}"

        if (results.failedFiles.size() > 0) {
            echo 'Failed files:'
            results.failedFiles.each { file ->
                echo "  - ${file}"
            }
        }

        if (results.failedFiles.size() > 0 && results.signedCount == 0) {
            error 'All firmware signing operations failed!'
        }

        if (results.signedCount == 0) {
            echo 'Warning: No firmware files were signed. This might be expected if no firmware files were found.'
        }

        // Upload signed deb package
        uploadSignedDeb(results.outputPath)
    }

    echo '=== Firmware signing pipeline completed successfully ==='
    echo "Input: ${env.linuxDdkPackageUrl}"
    echo "Output: ${signedPackageName}"

    // Cleanup
    sh '''
        rm -f original.deb
        rm -rf extracted-deb
        rm -f *.cookie
    '''
}

def uploadSignedDeb(String signedDebPath) {
    echo 'Uploading signed deb package...'

    // Parse the original URL to extract upload path
    def originalUrl = env.linuxDdkPackageUrl
    def uploadPath = extractUploadPath(originalUrl)

    echo "Original URL: ${originalUrl}"
    echo "Extracted upload path: ${uploadPath}"

    // Upload to artifact storage using the same path as original
    if (uploadPath) {
        echo 'Uploading to same path as original package...'
        artifact.upload(signedDebPath, uploadPath, false)
    } else {
        echo 'Could not extract upload path, using default upload method...'
        // Fallback to default upload method
        artifact.upload(
            env.gitlabSourceRepoName ?: 'firmware-signing',
            env.gitlabSourceBranch ?: 'master',
            env.gitlabMergeRequestLastCommit ?: 'manual-trigger',
            signedDebPath
        )
    }

    echo "Successfully uploaded signed deb package: ${signedDebPath}"
}

def extractUploadPath(String url) {
    // Example: http://sh-moss.mthreads.com/sw-build/linux-ddk/master/4d12d7a11/4d12d7a11_ddk2.0.deb
    // Should extract: sh-moss/sw-build/linux-ddk/master/4d12d7a11/

    // 检查URL有效性
    if (!url || !url.startsWith('http')) {
        echo "Invalid URL format: ${url}"
        return null
    }

    // 移除协议部分(http:// 或 https://)
    def urlWithoutProtocol = url.replaceFirst(/^https?:\/\//, '')

    // 查找第一个斜杠位置以分离主机名和路径
    def firstSlashIndex = urlWithoutProtocol.indexOf('/')
    if (firstSlashIndex == -1) {
        echo "No path separator found in URL: ${url}"
        return null
    }

    // 提取主机名和完整路径部分
    def hostname = urlWithoutProtocol.substring(0, firstSlashIndex)
    def fullPath = urlWithoutProtocol.substring(firstSlashIndex + 1)

    // 从主机名中提取第一部分（例如从sh-moss.mthreads.com提取sh-moss）
    def hostnamePart = hostname.split('\\.')[0]

    // 查找完整路径中的最后一个斜杠以移除文件名
    def lastSlashIndex = fullPath.lastIndexOf('/')
    def pathWithoutFileName = fullPath
    if (lastSlashIndex != -1) {
        pathWithoutFileName = fullPath.substring(0, lastSlashIndex)
    }

    // 构造最终路径：主机名第一部分 + 路径（不含文件名）
    def uploadPath = hostnamePart + '/' + pathWithoutFileName + '/'

    echo "Extracted upload path: ${uploadPath}"
    return uploadPath
}

runner.start(env.runChoice) {
    runPipeline([
        'Sign Firmware in Deb Package': [
            closure: { signFirmwareInDebPipeline() },
            setGitlabStatus: true,
            statusName: 'firmware-signing'
        ]
    ])
}
