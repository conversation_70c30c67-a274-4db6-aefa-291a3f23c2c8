@Library('swqa-ci')

import org.swqa.tools.git

def repoName = 'd3dtests'
def oss_path_d3d_bin = 'dependency/swci/d3d'

descriptionUpdates = []

def normalizeCommit(String commitId) {
    if (!commitId) {
        return new Date().format('yyyyMMdd') + '_master'
    }
    return commitId.matches(/[a-fA-F0-9]{40}/) ? commitId[0..8] : commitId
}

node(env.Build_Nodes) {
    deleteDir()
    currentBuild.description = "Build Node: ${env.NODE_NAME}<br>"

    def d3dcommitId = normalizeCommit(env.d3dCommit)

    stage('Prepare Repo') {
        def commitId = new git().fetchCode(repoName, 'master', env.d3dCommit, [preBuildMerge: false, updateBuildDescription: true])
        new git().updateSubmodule(repoName)
        descriptionUpdates << "${repoName}: ${commitId}<br>"
    }

    dir('d3dtests\\function\\src') {
        try {
            stage('Build d3d') {
                bat 'vs_m3d_build.bat'
            }
        } catch (e) {
            echo 'Build d3d failed!'
            currentBuild.result = 'FAIL'
            throw e
        }

        stage('Upload d3dtest binaries') {
            oss.setUp()

            bat """
                tar -czf ${d3dcommitId}_d3dtest_x64.tar.gz -C x64/Release .
                tar -czf ${d3dcommitId}_d3dtest_x86.tar.gz -C x86/Release .
            """

            def packages = [
                "${d3dcommitId}_d3dtest_x64.tar.gz",
                "${d3dcommitId}_d3dtest_x86.tar.gz"
            ]

            packages.each { packageName ->
                def targetPath = "sh-moss/${oss_path_d3d_bin}/${packageName}"
                bat "mc cp \"${packageName}\" \"${targetPath}\""
            }

            descriptionUpdates << "d3d x64 package: https://swci-oss.mthreads.com/${oss_path_d3d_bin}/${d3dcommitId}_d3dtest_x64.tar.gz<br>"
            descriptionUpdates << "d3d x86 package: https://swci-oss.mthreads.com/${oss_path_d3d_bin}/${d3dcommitId}_d3dtest_x86.tar.gz<br>"
        }
    }

    currentBuild.description += descriptionUpdates.join('')
}
