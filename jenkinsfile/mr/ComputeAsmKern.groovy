@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'ComputeAsmKern'
mtccCommitIdMap = [:]

def build(config) {
    def buildTasks = [:]
    def builds = config.builds
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            triggerInfo: env.triggerInfo,
            linuxDdkPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
            musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_toolkits_install_full.tar.gz",
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        dir(repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "ComputeAsmKern/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
            commitId: env.gitlabMergeRequestLastCommit])
        }
    }
    mtccCommitIdMap = constants.getCommitIdfromciConfig(config, 'mtccBranch')
    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true ]
    }
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            linuxDdkPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
            musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_toolkits_install_full.tar.gz",
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            statusName: parameters['testLabel'],
            async: true
        ]
    }

    runPipeline(workflow)
}
