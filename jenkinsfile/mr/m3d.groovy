@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'm3d'
ddkCommitIdMap = [:]
env.buildLabel = 'build'

def build(config) {
    def buildTasks = [:]
    def builds = config.builds

    for (buildConfig in builds) {
        if (buildConfig.job?.endsWith('.win')) { continue }
        if (buildConfig.buildOnlyMerged == 'true') { continue }
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.repo,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            ddkCommitId: ddkCommitIdMap[defaultParameters['ddkBranch']] ?: '',
            testLabel: _buildConfig.name ?: _buildConfig.job
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters,
            ])
        }
    }

    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    gitLib.fetchCode(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    dir(env.repo) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "m3d/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
        ])
    }
    ddkCommitIdMap = constants.getCommitIdfromciConfig(config)
    env.umdPackageUrl = constants.genLatestPackageUrl('gr-umd', 'develop', 'x86_64-mtgpu_linux-xorg-release-hw-m3d.tar.gz')
    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]

    def tests = config.tests ?: []
    for (testConfig in tests) {
        if (!testConfig.job?.endsWith('.win')) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def name = _testConfig.name ?: _testConfig.job
            def ddkBranch = defaultParameters['ddkBranch'] ?: ''
            def vulkanPackageName = defaultParameters?.vulkanPackageName ?: ''
            def parameters = [
                vulkanPackageUrl: vulkanPackageName ? constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, vulkanPackageName) : '',
                linuxDdkPackageUrl: ddkBranch ? constants.genPackageUrl('linux-ddk', ddkBranch, ddkCommitIdMap[ddkBranch], 'ddk2.0.deb', true) : '',
                "${constants.linuxDdkSubmodulePackageVarName[_testConfig.job]}" : constants.genPackageUrl(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.musaRuntimePackageName ?: (defaultParameters.packageName ?: 'ddk2.0.tar.gz')),
                triggerInfo: env.triggerInfo,
                testLabel: name
            ]
            if (constants.musaChangeBranches.contains(env.gitlabTargetBranch)) {
                parameters.musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', env.gitlabTargetBranch, defaultParameters.musaPackageName ?: 'musaRuntime.tar.gz')
            }
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            workflow["${_testConfig.name ?: _testConfig.job}"] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                async: true
            ]
        }
    }

    runPipeline(workflow)
}
