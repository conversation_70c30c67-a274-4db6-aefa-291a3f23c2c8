@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
env.buildLabel = 'jenkins/linux_vdi build'

def getLatestCommit(String dependencyUrl) {
    def latestUrl = dependencyUrl.substring(0, dependencyUrl.lastIndexOf('/') + 1) + 'latest.txt'
    def latestContent = utils.runCommandWithStdout("curl --insecure ${latestUrl}").with {
        it.endsWith('_') ? it[0..-2] : it
    }
    return latestContent.split('/')[-1]
}

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabSourceRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit[0..8],
            updateLatest: 'false',
            triggerInfo: env.triggerInfo,
            containerImage: defaultParameters.dockerImage ?: config.dockerImage,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[defaultParameters?.targetName ?: name] = [
            closure : {
                runPipeline.runJob([
                    job: _buildConfig.job,
                    parameters: parameters,
                ])
            },
            async: _buildConfig?.async ?: false
        ]
    }
    runPipeline.runSyncThenAsyncTasks(buildTasks)
}

def pack(config) {
    def packs = config.packs
    def packTasks = [:]
    for (packConfig in packs) {
        Map _packConfig = packConfig
        def name = _packConfig.name ?: _packConfig.job
        def defaultParameters = _packConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabSourceRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit[0..8],
            updateLatest: 'false',
            triggerInfo: env.triggerInfo,
            containerImage: defaultParameters.dockerImage ?: config.dockerImage,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        packTasks[defaultParameters?.packageName ?: name] = {
            runPipeline.runJob([
                job: _packConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel packTasks
}

runner.start(env.runChoice) {
    def umdDependency = "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/guest_gr-umd/"
    def kmdDependency = "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/guest_gr-kmd/"
    def mediaDependency = "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/guest_mt-media-driver/"
    def latestUmdCommit = env.gitlabSourceRepoName == 'gr-umd' ? env.gitlabMergeRequestLastCommit[0..8] : getLatestCommit(umdDependency)
    def latestKmdCommit = env.gitlabSourceRepoName == 'gr-kmd' ? env.gitlabMergeRequestLastCommit[0..8] : getLatestCommit(kmdDependency)
    def latestMediaCommit = env.gitlabSourceRepoName == 'mt-media-driver' ? env.gitlabMergeRequestLastCommit[0..8] : getLatestCommit(mediaDependency)

    env.configName = '.vdiConfig.yaml'
    mrBucket = "PR/${env.gitlabSourceRepoName}${env.gitlabMergeRequestIid}"
    def config = null
    stage('checkout') {
        gitLib.fetchCode(env.gitlabSourceRepoName, env.branch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, noTags: true, updateBuildDescription: true])
        dir(env.gitlabSourceRepoName) {
            config = commonLib.loadPipelineConfig(env.configName, "${env.gitlabSourceRepoName}/${env.gitlabTargetbranch}_vdi.yaml", [
                date: env.date ?: new Date().format('yyyy.MM.dd'),
                mrBucket: mrBucket,
                branch: env.gitlabTargetBranch,
                commit: env.gitlabMergeRequestLastCommit[0..8],
                latestUmdCommit: latestUmdCommit,
                latestKmdCommit: latestKmdCommit,
                latestMediaCommit: latestMediaCommit
            ])
        }
        println config
    }

    def workflow = [:]
    workflow['buildPkg'] = [closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel]
    if (config.packs?.size() > 0) { workflow['packPkg'] = [closure: { pack(config) }, setGitlabStatus: true, statusName: 'jenkins/linux_vdi pack'] }

    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def vdiHostPackageName = defaultParameters?.vdiHostPackageName ?: ''
        def vdiGuestPackageName = defaultParameters?.vdiGuestPackageName ?: ''
        // TO DO: constants.genPackageUrl(env.repo, env.branch, env.commitId, vdiHostPackageName)
        def parameters = [
            vdiHostPackageUrl: vdiHostPackageName ? "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/drivers/${mrBucket}/${env.gitlabMergeRequestLastCommit[0..8]}_${vdiHostPackageName}" : '',
            vdiGuestPackageUrl: vdiGuestPackageName ? "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/drivers/${mrBucket}/${env.gitlabMergeRequestLastCommit[0..8]}_${vdiGuestPackageName}" : '',
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow[name] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            statusName: parameters.testLabel,
            async: true
        ]
    }

    runPipeline(workflow)
}
