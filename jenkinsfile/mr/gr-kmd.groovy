@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()
env.buildLabel = 'jenkins/build'

def build(config) {
    def builds = config.builds ?: []
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabTargetRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            uploadCase: 'true',
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: utils.toStringParams(parameters)
            ])
        }
    }
    parallel buildTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    // get config from repo gr-kmd
    // def config = gitLib.getFileContentByApi('gr-kmd', env.branch, 'buildConfig.yaml', 'yaml')
    gitLib.fetchCode('gr-kmd', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir('gr-kmd') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "gr-kmd/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            gitlabTargetBranch: env.gitlabTargetBranch,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID
        ], 'gr-kmd/default.yaml')
    }

    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build kmd'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]
        if (config.separateBuildAndTest == 'true' && env.gitlabActionType == 'MERGE') {
            gitLib.addCommentForMR('Only the build tasks will be excuted, add the comment "runtest" if you want to run the test tasks.')
        }
    }

    def umdBranch = env.gitlabSourceRepoName == 'gr-kmd' ? env.gitlabTargetBranch : null
    // def umdBranch = env.gitlabSourceRepoName == 'gr-kmd' ? 'develop' : null
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def testLabel = defaultParameters.testLabel ?: name
        if (env.gitlabActionType == 'MERGE' && config.separateBuildAndTest == 'true') {
            gitLib.setGitlabStatus(testLabel, 'canceled')
            gitLib.setGitlabStatus(testLabel, 'pending')
        } else {
            def umdPackageUrl = constants.genLatestPackageUrl('gr-umd', umdBranch,  defaultParameters.umdPackageName ?: 'x86_64-mtgpu_linux-xorg-release-hw-glvnd.tar.gz')
            // TODO: @蒋波: branch commitId ???
            def parameters = [
                branch: env.gitlabSourceBranch,
                commitId: env.gitlabMergeRequestLastCommit,
                ossTestResultsSavePath: "sw-pr/${_testConfig.name.replaceAll(' ', '_')}/${env.gitlabMergeRequestIid ? 'PR' : 'verify'}",
                musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
                triggerInfo: env.triggerInfo,
                testLabel: name
            ]
            // check if  defaultParameters.packageName exists
            if (defaultParameters.packageName) {
                parameters.linuxDdkPackageUrl = constants.genPackageUrl('gr-kmd', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.packageName ?: 'ddk2.0.deb')
            } else {
                parameters.kmdPackageUrl = constants.genPackageUrl('gr-kmd', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.kmdPackageName ?: 'x86_64-mtgpu_linux-xorg-release-hw.deb')
                parameters.umdPackageUrl = umdPackageUrl
            }
            if (defaultParameters.compatDdkBranch) {
                parameters.compatDdkPackageUrl = constants.genLatestPackageUrl('linux-ddk', defaultParameters.compatDdkBranch, defaultParameters.packageName ?: 'ddk2.0.deb')
            }

            if (_testConfig.job == 'CI_DDK_video_test_gitlab') {
                parameters['ghprbActualCommit'] = env.gitlabMergeRequestLastCommit
            }
            defaultParameters.each { key, value ->
                parameters[key] = value
            }
            workflow[name] = [
                job: _testConfig.job,
                parameters: utils.toStringParams(parameters),
                setGitlabStatus: true,
                async: true
            ]
        }
    }

    runPipeline(workflow)
}
