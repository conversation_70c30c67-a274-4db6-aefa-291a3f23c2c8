@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'ogl'
ddkCommitIdMap = [:]
env.buildLabel = 'jenkins/build'

def build(config) {
    def buildTasks = [:]
    def builds = config.builds
    for (buildConfig in builds) {
        if (buildConfig.job?.endsWith('.win')) { continue }
        def _buildConfig = buildConfig

        // Skip jobs starting with 'build.ogl.' and name does NOT contain 'clang-tidy'
        echo "Skipping job: ${_buildConfig.job}, name: ${_buildConfig.name}"
        if (_buildConfig.job?.startsWith('build.ogl.') && !_buildConfig.name?.contains('clang-tidy')) {
            echo "Skipping job: ${_buildConfig.job}"
            continue
        }

        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            testLabel: _buildConfig.name,
            repo: env.repo,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            ddkCommitId: ddkCommitIdMap[defaultParameters['ddkBranch']] ?: '',
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters,
            ])
        }
    }
    parallel buildTasks
}

def uploadCase() {
    gitLib.fetchCode(env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    dir(env.repo) {
        if (fileExists('cts')) {
            oss.install()
            oss.cp('cts/linux/*', "sh-moss/sw-build/ogl/${env.gitlabSourceBranch}/test/")
        }
    }
}

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        dir(env.repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "ogl/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
            commitId: constants.formatCommitID(env.gitlabMergeRequestLastCommit),
            oglSourceBranch: env.gitlabSourceBranch])
        }
    }
    ddkCommitIdMap = constants.getCommitIdfromciConfig(config)
    stage('upload case') {
        uploadCase()
    }

    def workflow = [:]
    workflow['jenkins/linux-ddk-build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: env.buildLabel ]

    def tests = config.tests ?: []
    for (testConfig in tests) {
        if (!testConfig.job?.endsWith('.win')) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def name = _testConfig.name ?: _testConfig.job
            def ddkBranch = defaultParameters['ddkBranch'] ?: ''
            def parameters = [
                linuxDdkPackageUrl:ddkBranch ? constants.genPackageUrl('linux-ddk', ddkBranch, ddkCommitIdMap[ddkBranch], defaultParameters.ddkPackageName ?: 'ddk2.0.deb', true) : '',
                oglSourceBranch: env.gitlabSourceBranch,
                "${constants.linuxDdkSubmodulePackageVarName[_testConfig.job]}": constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.packageName ?: 'ddk2.0.tar.gz'),
                triggerInfo: env.triggerInfo,
                testLabel: name
            ]
            defaultParameters.each { key, value ->
                // HOTFIX: ogl .ciConfig.yaml has a key 'oglPackageUrl' which is not used in the pipeline
                if (key != 'oglPackageUrl') {
                    parameters[key] = value
                }
            }
            workflow["${name}"] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                statusName: parameters['testLabel'],
                async: true
            ]
        }
    }
    runPipeline(workflow)
}
