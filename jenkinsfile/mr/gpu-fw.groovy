@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build(config) {
    def builds = config.builds
    def buildTasks = [:]
    for (buildConfig in builds) {
        if (buildConfig.job?.endsWith('.win')) { continue }
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            repo: env.gitlabTargetRepoName,
            uploadCase: 'true',
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    // get config from repo gpu-fw
    // def config = gitLib.getFileContentByApi('gpu-fw', env.branch, 'buildConfig.yaml', 'yaml')
    gitLib.fetchCode('gpu-fw', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir('gpu-fw') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "gpu-fw/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch,
            gitlabTargetBranch: env.gitlabTargetBranch,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID
        ], 'gpu-fw/default.yaml')
    }

    def workflow = [:]
    // workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true, statusName: 'jenkins/linux-ddk-build' ]
    workflow['build'] = [ closure: { build(config) } ]
    def tests = config.tests ?: []
    for (testConfig in tests) {
        if (!testConfig.job?.endsWith('.win')) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def name = _testConfig.name ?: _testConfig.job
            def parameters = [
                linuxDdkPackageUrl: constants.genPackageUrl('gpu-fw', env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.packageName ?: 'ddk2.0.deb'),
                triggerInfo: env.triggerInfo,
                testLabel: name
            ]
            // in develop branch, musa-runtime was already split
            if (env.gitlabTargetBranch == 'develop') {
                parameters.musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz')
            } else if (['release_musa_4.3.0'].contains(env.gitlabTargetBranch)) {
                parameters.musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', env.gitlabTargetBranch, 'musaRuntime.tar.gz')
            }
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            workflow["${name}"] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                async: true
            ]
        }
    }

    runPipeline(workflow)
}
