@Library('swqa-ci')

//author: liya
//email: <EMAIL>

import org.swqa.tools.common

commonLib = new common()

yesterdayTag = new Date().previous().format('yyyyMMdd')
vpsTag = env.tag ?: yesterdayTag

def getHostIP(String nodeName) {
    def nodesPerGroup = 25
    def baseNodeName = 'CI_GFX_WIN_VPS_'
    def nodeConfigs = []
    def hostIPs = [
        '************',
        '************',
        '************',
        '************',
        '************',
        '************'
    ]

    def groupCounter = 1
    (0..(hostIPs.size() - 1)).each { groupIndex ->
        def startNode = (groupIndex * nodesPerGroup) + 1
        def endNode = startNode + nodesPerGroup - 1
        def nodes = (startNode..endNode).collect { i -> "${baseNodeName}${i.toString().padLeft(3, '0')}" }

        def groupLabel = "CI_GFX_WIN_${groupCounter.toString().padLeft(2, '0')}"
        nodeConfigs << [nodes: nodes, label: groupLabel, ip: hostIPs[groupIndex]]
        groupCounter++
    }

    nodeConfigs.each { config ->
        /* groovylint-disable-next-line UnnecessaryCollectCall */
        if (config.nodes.collect { it.trim() }.contains(nodeName.trim())) {
            env.hostLabel = config.label
            env.hostIP = config.ip
            println("Match found! Assigning hostIP: ${env.hostIP}")
    }
}
}

def sshExecute(String command, boolean exitOnFailure = true) {
    def port = env.nodeName[-3..-1]
    def sshpass = "sshpass -p 'MTvps123' "
    def ssh_cmd = "ssh vpsmt@${env.hostIP} -p 20${port} -o StrictHostKeyChecking=no"

    if (exitOnFailure) {
        sh """ ${sshpass} ${ssh_cmd} "${command}" || exit 1 """
    } else {
        sh """ ${sshpass} ${ssh_cmd} "${command}" || echo "Command failed but continuing..." """
    }
}

def timeSync(int maxRetries = 10, int retryDelay = 10) {
    int retryCount = maxRetries
    while (retryCount > 0) {
        try {
            sshExecute('net start w32time', false)

            def date_value = sh(script: 'date +%Y-%m-%d', returnStdout: true).trim()
            def time_value = sh(script: 'date +%H:%M:%S', returnStdout: true).trim()

            sshExecute("date ${date_value} && time ${time_value}")
            sshExecute('w32tm /resync /rediscover')

            println('Time synchronization successful.')
            sh 'sleep 2'
            break
        } catch (e) {
            retryCount -= 1
            if (retryCount > 0) {
                println("Failed to run command. Retrying... ${retryCount} attempts remaining. Sleeping for ${retryDelay} seconds...")
                sh "sleep ${retryDelay}"
            } else {
                error 'Max retry attempts reached. Exiting.'
            }
        }
    }
}

def restart(String nodeName) {
    // Set default ipc_type based on chip_type
    if (env.chip_type in ['quyuan1', 'quyuan2']) {
        env.ipc_type = 'shm'
    }

    echo "Using ipc_type: ${env.ipc_type} for chip_type: ${env.chip_type}"

    def versionFloat = env.vps_version as Float
    def scriptName = (versionFloat >= 20.0) ? 'start_vps_vnc.sh' : 'start_vps.sh'
    sh """
        docker restart ${nodeName}
        docker exec -t ${nodeName} /bin/bash -c "nohup ./vps/${scriptName} ${env.vps_version} ./vps/${nodeName}.img ${env.chip_type} ${env.model_type} ${env.ipc_type} > vm.log"
    """
}

def shutdown(String nodeName) {
    sh "docker stop ${nodeName}"
}

def getNodeSecret(String nodeName) {
    def node = Jenkins.instance.getNode(nodeName)
    def computer = node?.toComputer()

    if (computer) {
        return computer.getJnlpMac()
    }
    error node ? "No computer object found for node: ${nodeName}" : "No node found with name: ${nodeName}"
}

def reinstall(String nodeName) {
    // Set default ipc_type based on chip_type
    if (env.chip_type in ['quyuan1', 'quyuan2']) {
        env.ipc_type = 'shm'
    }

    echo "Using ipc_type: ${env.ipc_type} for chip_type: ${env.chip_type}"

    oss.install()
    sh """
        cd /data1/qemu/running
        rm -rf ${nodeName}.img
        qemu-img create -F qcow2 -f qcow2 -b win10_test_base.img ${nodeName}.img
        docker start ${nodeName}
    """

    credentials.runWithCredential('SH_MOSS') {
        // Determine config file path based on vps_version
        def versionFloat = env.vps_version as Float
        def configPath = (versionFloat >= 21.0) ?
            "/root/workspace/soc_model/release_mode/amodel/${env.chip_type}/gfx_config.yaml" :
            '/root/workspace/gfx_config.yaml'

        def configCommands = ''
        if (versionFloat >= 21.0) {
            // For version 21.0+, only copy files without config modifications
            configCommands = """
                mc alias set sh-moss https://sh-moss.mthreads.com ${USERNAME} ${PASSWORD}
                mc cp --recursive sh-moss/mt-amodel-release/${env.chip_type}_daily/${vpsTag}/ /root/workspace/soc_model/release_mode/amodel/${env.chip_type}/
                mc cp --recursive sh-moss/mt-amodel-release/${env.chip_type}_daily/${vpsTag}/windows/gfx_config.yaml /root/workspace/soc_model/release_mode/amodel/${env.chip_type}/
                sed -i 's/hazard_check_enable: true/hazard_check_enable: false/' ${configPath}
                sed -i 's/reg_reuse_check_enable: true/reg_reuse_check_enable: false/' ${configPath}
                cat ${configPath}
            """
        } else {
            // For versions < 21.0, use the original logic with config modifications
            configCommands = """
                sed -i 's/irq_enable: true/irq_enable: false/' ${configPath}
                sed -i 's/watch_dog_enable: true/watch_dog_enable: false/' ${configPath}
                sed -i 's/shared_memory_enable: false/shared_memory_enable: true/' ${configPath}
                mc alias set sh-moss https://sh-moss.mthreads.com ${USERNAME} ${PASSWORD}
                mc cp --recursive sh-moss/mt-amodel-release/${env.chip_type}_daily/${vpsTag}/ /root/workspace/soc_model/release_mode/amodel/${env.chip_type}/
                cat ${configPath}
            """
        }

        if (env.model_type == 'cmodel') {
            configCommands = """
                mc alias set sh-moss https://sh-moss.mthreads.com ${USERNAME} ${PASSWORD}
                mc cp --recursive sh-moss/mt-cmodel-release/${env.chip_type}_daily/${vpsTag}/arch_VPS_release_binary.tar.gz . && \\
                    tar xf arch_VPS_release_binary.tar.gz && \\
                    cp -f ci_env/vps_binary/*.so /root/workspace/soc_model/release_mode/cmodel/${env.chip_type}/ && \\
                    cp -f ci_env/vps_binary/param_configuration.conf /root/workspace/
            """
        }

        if (env.chip_type in ['ph1', 'hg', 'hs', 'ls']) {
            sh """
                docker exec -t ${nodeName} /bin/bash -c "${configCommands}"
            """
        }
    }

    def versionFloat = env.vps_version as Float
    def scriptName = (versionFloat >= 20.0) ? 'start_vps_vnc' : 'start_vps'
    sh """
        docker exec -t ${nodeName} /bin/bash -c "nohup ./vps/${scriptName}.sh ${env.vps_version} ./vps/${nodeName}.img ${env.chip_type} ${env.model_type} ${env.ipc_type} > vm.log 2>&1"
        sleep 5
        echo "[VPS] Startup Logs:"
        docker exec -t ${nodeName} /bin/bash -c "cat vm.log"
    """

    def secretNum = getNodeSecret(nodeName)
    def startJenkinsAgent = """echo java -jar agent.jar -url http://swci-jenkins.mthreads.com/ -secret ${secretNum} -name ${nodeName} -webSocket -workDir \\"C:\\\\jenkins\\">C:\\jenkins\\start_jenkins.bat"""

    retry(50) {
        try {
            sshExecute(startJenkinsAgent)
        } catch (e) {
            sleep(2)
            throw e
        }
    }
}

def createContainer(String nodeName) {
    def port = env.nodeName[-3..-1]
    sh """
        echo "=== Previous VM Logs ==="
        docker exec -t ${nodeName} cat vm.log 2>/dev/null || echo "No previous VM logs found"
        echo "=== End VM Logs ==="

        docker rm -f ${nodeName} || true
        docker run -d --privileged -p 10${port}:5905 -p 20${port}:10022 -v /data1/qemu/running:/root/vps/ --shm-size=32gb --name ${nodeName} --restart=always sh-harbor.mthreads.com/build-env/vps-qemu:v${env.vps_version}
    """
}

def deleteVpsDockerImage(String nodeName) {
    sh """
        docker rm -f ${nodeName} || true
        docker rmi -f sh-harbor.mthreads.com/build-env/vps-qemu:v${env.vps_version} || true
    """
}

def handleNodeOperation(String nodeName, String operation) {
    def maxRetries = 3
    def retryInterval = 30

    retry(maxRetries) {
        try {
            switch (operation) {
                case 'restart':
                    restart(nodeName)
                    timeSync()
                    break

                case 'shutdown':
                    shutdown(nodeName)
                    break

                case 'reinstall':
                    createContainer(nodeName)
                    reinstall(nodeName)
                    try {
                        commonLib.waitUntilNodeOnline(nodeName, 360)
                    } catch (e) {
                        echo "Warning: Node connection issue: ${e.message}"
                        restart(nodeName)
                        timeSync()
                        commonLib.waitUntilNodeOnline(nodeName, 240)
                    }
                    timeSync()
                    break

                case 'new_vps':
                    deleteVpsDockerImage(nodeName)
                    createContainer(nodeName)
                    reinstall(nodeName)
                    timeSync()
                    break

                default:
                    error "Unknown operation: ${operation}"
            }
        } catch (e) {
            echo "Operation failed: ${e.message}"
            sh """
                echo "=== Container Status ==="
                docker ps -a | grep ${nodeName} || echo "Container not found"
                echo "=== Container Logs ==="
                docker logs ${nodeName} 2>/dev/null || echo "No logs available"
            """
            sleep(retryInterval)
            throw e
        }
    }
}

runner.start(env.runChoice) {
    currentBuild.description = "To ${env.operation} ${env.nodeName} <br>"
    def workflow = [
        'getHostIP': [closure: { getHostIP(env.nodeName) }],

        'handleNodeOperation': [closure: { node(hostLabel) { handleNodeOperation(env.nodeName, env.operation) } }],
    ]

    runPipeline(workflow, [disablePost: true])
}
