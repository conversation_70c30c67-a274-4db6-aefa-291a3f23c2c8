@Library('swqa-ci')

//author: liya
//email: <EMAIL>

def downloadAndStashDriver(String downloadUrl, String dirName = 'wddm_driver') {
    def pkgName = downloadUrl.split('/')[-1]
    dir(dirName) {
        sh """
            wget -q ${downloadUrl} -O ${pkgName} --no-check-certificate
            tar xvzf ${pkgName}
            rm -f ${pkgName}
        """
    }
}

def generateDdfFile(String driverDirPath, String ddfFileName = 'mtgpu.ddf', String cabName = 'mtgpu.cab', String destDir = 'mtgpu') {
    def driverDir = "${env.WORKSPACE}\\${driverDirPath}"
    def ddfFilePath = "${env.WORKSPACE}\\${ddfFileName}"

    def ddfHeader = """
    ;*** Generated mtgpu.ddf ***
    .OPTION EXPLICIT
    .Set CabinetFileCountThreshold=0
    .Set FolderFileCountThreshold=0
    .Set FolderSizeThreshold=0
    .Set MaxCabinetSize=0
    .Set MaxDiskFileCount=0
    .Set MaxDiskSize=0
    .Set CompressionType=MSZIP
    .Set Cabinet=on
    .Set Compress=on
    .Set CabinetNameTemplate=${cabName}
    .Set DestinationDir=${destDir}
    ;Specify files to be included in cab file
    """.stripIndent()

    writeFile(file: ddfFilePath, text: ddfHeader)

    def batScript = """
        @echo off
        set DRIVER_DIR=${driverDir}
        set DDF_FILE=${ddfFilePath}

        echo DRIVER_DIR=%DRIVER_DIR%
        echo DDF_FILE=%DDF_FILE%

        for /r "%DRIVER_DIR%" %%F in (*) do (
            echo %%~dpF | findstr /i "symbols" >nul
            if errorlevel 1 (
                echo %%~nxF >> "%DDF_FILE%"
            )
        )

        echo DDF file has been updated successfully: %DDF_FILE%
    """

    if (env.driver_url.contains('release')) {
        batScript = """
            @echo off
            set DRIVER_DIR=${driverDir}
            set DDF_FILE=${ddfFilePath}

            echo DRIVER_DIR=%DRIVER_DIR%
            echo DDF_FILE=%DDF_FILE%

            for /r "%DRIVER_DIR%" %%F in (*) do (
                rem Exclude paths containing "symbols" directory
                echo %%~dpF | findstr /i "symbols" >nul
                if errorlevel 1 (
                    rem Check if the file has an excluded extension (.win, .dict, .bin)
                    echo %%~xF | findstr /i ".win" >nul
                    if errorlevel 1 (
                        echo %%~xF | findstr /i ".dict" >nul
                        if errorlevel 1 (
                            echo %%~xF | findstr /i ".bin" >nul
                            if errorlevel 1 (
                                rem Write the file name to the DDF file
                                echo %%~nxF >> "%DDF_FILE%"
                            )
                        )
                    )
                )
            )

            echo DDF file has been updated successfully: %DDF_FILE%
        """
    }

    def result = bat(script: batScript, returnStdout: true)

    println result
    println "DDF file has been generated successfully at: ${ddfFilePath}"
}

def sign() {
    node(env.iTrusSign_Nodes) {
        deleteDir()
        downloadAndStashDriver(env.driver_url)
        generateDdfFile('wddm_driver')
        pkgName = env.driver_url.tokenize('/').last().replace('.tar.gz', '')

        def signtoolSha1 = env.NODE_NAME.contains('114.121') ? constants.signtoolSha1ChengDu : constants.signtoolSha1

        dir('wddm_driver') {
            stage('make cab') {
                def ddfFilename = 'mtgpu.ddf'

                bat"""
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\*.dll
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\*.sys
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\*.cat
                    makecab /f ${env.WORKSPACE}\\${ddfFilename}
                """
            }

            stage('sign cab') {
                bat """
                    signtool sign /fd sha256 /sha1 ${signtoolSha1} /t http://timestamp.globalsign.com/?signature=sha2 ${WORKSPACE}\\wddm_driver\\disk1\\mtgpu.cab
                """
            }

            retry(3) {
                stage('sign driver') {
                    bat """
                        chcp 65001
                        set LANG=en_US.UTF-8
                        REM Clear Chrome cache before signing
                        taskkill /f /im chrome.exe /t 2>nul || echo "Chrome not running"
                        REM Clear Chrome cache
                        rd /s /q "%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default\\Cache" 2>nul || echo "Chrome cache cleared"
                        rd /s /q "%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default\\Code Cache" 2>nul || echo "Chrome code cache cleared"
                        sleep 2
                        python C:\\tools\\wddm_signature\\wddmdriver_signature.py ${pkgName} ${WORKSPACE}\\wddm_driver\\disk1\\mtgpu.cab ${env.signature_waiting_status}
                        sleep 2
                    """
                }

                if (env.signature_waiting_status == 'yes') {
                    stage('upload') {
                        oss.setUp()
                        def sign_driver_map = readJSON file: 'C:\\tools\\wddm_signature\\wddmdriver.json' , returnPojo: true
                        def sign_driver_id = sign_driver_map["${pkgName}"]
                        bat """
                        chcp 65001
                        set LANG=en_US.UTF-8
                        cd /D "%USERPROFILE%\\Downloads
                        move /y Signed_${sign_driver_id}.zip Signed_${pkgName}.zip
                        mc cp Signed_${pkgName}.zip sh-moss/${env.oss_save_path}/
                    """
                    }
                }
            }
        }
    }
}

def pes() {
    def baseUrl = "https://sh-moss.mthreads.com/${env.oss_save_path}"
    def wddm_driver_url = "${baseUrl}/Signed_${pkgName}.zip"
    def wddm_branch = env.driver_url.split('/')[-3]
    def save_commitid = env.driver_url.split('/')[-2]
    def host_pkg_save_path = "sw-build/VDI/${wddm_branch}/${save_commitid}"

    def branchConfig = [
        'release_vgpu_2.7.0': [install_ver: '200.270.004', pes_branch: 'release_vgpu_2.7.0', pes_version: '1.9.3', driver_ver: '10.0', allinone_version: '270-004'],
        'release_vgpu_2.5.6': [install_ver: '200.256.008', pes_branch: 'release_vgpu_2.5.6', pes_version: '1.0.1', driver_ver: '1.0.2.1102', allinone_version: '256-008'],
        'release_vgpu_2.7.5': [install_ver: '200.275.004', pes_branch: 'release_vgpu_2.7.5', pes_version: '1.9.3', driver_ver: '10.0', allinone_version: '275-004']
    ]

    def defaults = [install_ver: new Date().format('yyyyMMdd.HH'), pes_branch: wddm_branch, pes_version: '', driver_ver: '']
    def cfg = branchConfig.get(wddm_branch, defaults)
    install_ver = cfg.install_ver
    pes_branch  = cfg.pes_branch
    pes_version = cfg.pes_version
    driver_ver  = cfg.driver_ver
    allinone_version = cfg.allinone_version

    if (env.buildPes == 'pes_vdi') {
        runPipeline.runJob([
            job: 'pes_build_tool',
            wait:false,
            parameters: [
                wddm_driver_url: wddm_driver_url,
                wddm_branch: wddm_branch,
                pes_branch: pes_branch,
                oss_save_path: oss_save_path,
                pes_version: pes_version,
                install_ver: install_ver,
                allinone_version: allinone_version,
                driver_ver: driver_ver,
                package_platform_type: 'pes-vdi',
                host_pkg_save_path:host_pkg_save_path
            ]
        ])
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'sign': [closure: { sign() }],

        'pes': [closure: { pes() }]
    ]

    runPipeline(workflow, [disablePost:true])
}
