@Library('swqa-ci')

import org.swqa.tools.git

def docker_image = 'sh-harbor.mthreads.com/build-env/gr-umd:v55'
def build_date = new Date().format('yyyyMMdd')
def branch = env.branch
def pack_branch = env.pack_branch ?: 'pack_vdi_driver_windows'
def package_name = env.package_name ?: 'mtvgpu'
def today = new Date().format('yyyy.MM.dd')
def version = env.version ?: today

if (branch == 'release_vgpu_2.5.6') {
    pack_branch = 'pack_vdi_driver_win'
}

echo "vdi driver using branch: ${branch}"
echo "package using branch: ${pack_branch}"

if (!(version ==~ /(\w+\.){2}\w+([-~\+\.]{1}\w+)*/ && !version.contains('_'))) {
    throw new Exception("Version must be in the format A.B.C-XXX and cannot contain '_'. Your input: ${version}.")
}

def getVDIVersion = { branch_name ->
    def matcher = branch_name =~ /vgpu_(\d+\.\d+\.\d+)_release/
    matcher.find() ? matcher.group(1) : '1.0.0'
}

def parseVersion(String version) {
    def splitParts = version.split(/-/, 2)
    def mainPart = splitParts[0]
    def extraPart = splitParts.size() > 1 ? splitParts[1] : null

    def mainParts = mainPart.split(/\./).collect { it as int }

    def extraParts = extraPart ? extraPart.split(/[^0-9]/).collect { it as int } : []

    return [mainParts, extraParts]
}

def compareVersions = { v1, v2 ->
    def (parts1, extra1) = parseVersion(v1)
    def (parts2, extra2) = parseVersion(v2)

    for (int i = 0; i < Math.min(parts1.size(), parts2.size()); i++) {
        if (parts1[i] != parts2[i]) {
            return parts1[i] <=> parts2[i]
        }
    }
    def mainComparison = parts1.size() <=> parts2.size()
    if (mainComparison != 0) { return mainComparison }

    for (int i = 0; i < Math.min(extra1.size(), extra2.size()); i++) {
        if (extra1[i] != extra2[i]) {
            return extra1[i] <=> extra2[i]
        }
    }
    return extra1.size() <=> extra2.size()
}

def vdiVersion = env.version ? version : getVDIVersion(branch)
def kmdDkmsVersion = compareVersions(vdiVersion, '2.5.0') >= 0 ? vdiVersion : '1.0.0'

echo "kmd dkms version: ${kmdDkmsVersion}"
version = branch == 'develop' ? "${version}-dev" : version
echo "now version is ${version}"

def branchToGmiFilter = [
    'release_2.5.0_update'         : '*_mthreads-gmi_1.12.2*.tar.gz',
    'vgpu_2.5.0_release'           : '*_mthreads-gmi_1.12.3*.tar.gz',
    'vgpu_2.6.0_release'           : '*_mthreads-gmi_1.12.2*.tar.gz',
    'vgpu_2.6.5_release'           : '*_mthreads-gmi_1.12.2*.tar.gz',
    'release_vgpu_2.7.0'           : '*_mthreads-gmi_2.0.0*.tar.gz',
    'release_vgpu_2.7.5'           : '*_mthreads-gmi_2.0.3*.tar.gz'
]

node(env.Build_Nodes) {
    cleanWs()
    currentBuild.description = "<br> ${env.build_ID}_${env.branch}"
    currentBuild.description = "Test Node: ${env.NODE_NAME} <br>"

    sh """
        mkdir -p vdi_${build_date}
    """

    def host_pkg_ossurl = "https://oss.mthreads.com/${env.host_pkg_save_path}/"
    def host_commit = env.host_pkg_save_path.split('/')[-1] ?: sh(
        script: "curl --insecure ${host_pkg_ossurl}latest.txt",
        returnStdout: true
    ).trim().split('/').last()

    echo "host commit: ${host_commit}"
    currentBuild.description += "host commit: ${host_commit} <br>"

    def gmi_branch = env.gmi_branch ?: 'release_1.10'
    def gmi_pkg_url = sh(
        script: "curl --insecure https://sh-moss.mthreads.com/sw-build/gmi/${gmi_branch}/latest.txt",
        returnStdout: true
    ).trim()

    if (gmi_branch in ['release_1.12']) {
        docker.image(docker_image).inside('-i -u 0:0') {
            oss.install()
            sh '''
                add-apt-repository "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ focal main restricted universe multiverse"
                apt update
                apt install -y jq
            '''
            def gmi_filter = branchToGmiFilter.get(branch, '*_mthreads-gmi_1.12.4*.tar.gz')
            def gmi_pkg_name = sh(
                script: "mc find sh-moss/sw-build/gmi/${gmi_branch} --name ${gmi_filter} --exec \"mc stat --json {}\" | jq -sr 'map({lastModified, name}) | sort_by(.lastModified) | reverse | .[0].name'",
                returnStdout: true
            ).trim()
            gmi_pkg_url = "https://sh-moss.mthreads.com/sw-build/gmi/${gmi_branch}/${gmi_pkg_name}"
        }
    }

    echo "gmi pkg url: ${gmi_pkg_url}"

    def snapshot_ver = env.snapshot_ver ?: '0.1.1'
    def snapshot_pkg_url = "https://sh-moss.mthreads.com/dependency/mtgpu_snapshot/${snapshot_ver}/mtgpu_snapshot-${snapshot_ver}-1.noarch.rpm"
    echo "snapshot pkg url: ${snapshot_pkg_url}"

    def mtvgm_branch = branch.contains('release') ? branch : 'master'
    def mtvgm_pkg_url = sh(
        script: "curl --insecure https://sh-moss.mthreads.com/sw-build/mtvgm/${mtvgm_branch}/latest.txt",
        returnStdout: true
    ).trim() + '_mtvgm_amd64.rpm'

    def build_types = env.support_types.split('\n').findAll { it.trim() }
    def build_tasks = [:]

    build_types.each { iter ->
        build_tasks[iter] = {
            def host_pkg_url = "https://oss.mthreads.com/${env.host_pkg_save_path}/"
            docker.image(docker_image).inside('-i -u 0:0') {
                dir("${env.WORKSPACE}/vdi_${iter}") {
                    stage('fetch build code') {
                        new git().fetchCode('musa_package', pack_branch, null, [preBuildMerge: false])
                    }
                    stage(iter) {
                        def pkg_type = iter in ['dkms_deb', 'Ubuntu'] ? 'deb' : 'rpm'

                        switch (iter) {
                            case 'dkms_deb':
                                host_pkg_url += "${host_commit}_mtgpu-${kmdDkmsVersion}.amd64.deb"
                                break
                            case 'dkms_rpm':
                                host_pkg_url += "${host_commit}_mtgpu-${kmdDkmsVersion}.amd64.rpm"
                                break
                            default:
                                host_pkg_url += "${host_commit}_kmd_${iter}_release.tar.gz"
                        }

                        if (iter in ['dkms_deb', 'dkms_rpm']) {
                            iter = 'dkms'
                        }

                        def exportVars = [
                            "export host_pkg_url='${host_pkg_url}'",
                            branch in ['vgpu_2.5.0_release'] ? "export pes_pkg_url='${pes_pkg_url}'" : '',
                            env.guestDrvUrl ? "export guest_pkg_url='${env.guestDrvUrl}'" : '',
                            "export gmi_pkg_url='${gmi_pkg_url}'",
                            "export snapshot_pkg_url='${snapshot_pkg_url}'",
                            mtvgm_pkg_url ? "export mtvgm_pkg_url='${mtvgm_pkg_url}'" : ''
                        ].findAll { it }.join('\n')

                        echo "${exportVars}"

                        dir('musa_package') {
                            sh """
                                ${exportVars}
                                ls -al
                                ./build_package.sh -n ${package_name} -v "${version}" -o "${iter}" -p "${pkg_type}" -a amd64
                                for f in ${package_name}*; do
                                    mv \$f ${env.WORKSPACE}/vdi_${build_date}/"${host_commit}_${iter}_\$f";
                                done;
                            """
                        }
                    }
                }
            }
        }
    }

    try {
        parallel build_tasks
    } catch (exc) {
        println("Build failed: ${exc}")
        throw exc
    }

    stage('upload') {
        def commit_info = "${env.WORKSPACE}/vdi_${build_date}/${host_commit}_${package_name}_${version}_info.txt"
        def env_info = "${env.WORKSPACE}/vdi_${build_date}/${host_commit}_${package_name}_${version}_env.txt"

        oss.install()

        sh """
            echo "host commit: ${host_commit}" > ${commit_info};
            echo "gmi url: ${gmi_pkg_url}" >> ${commit_info};
            echo pes url: ${pes_pkg_url} >> ${commit_info};
            echo "snapshot url: ${snapshot_pkg_url}" >> ${commit_info};
            printenv > ${env_info};
            mc cp ${env.WORKSPACE}/vdi_${build_date}/* sh-moss/${env.oss_save_path};
        """
        currentBuild.description += "pkg path: https://oss.mthreads.com/${env.oss_save_path}<br>"
    }
}
