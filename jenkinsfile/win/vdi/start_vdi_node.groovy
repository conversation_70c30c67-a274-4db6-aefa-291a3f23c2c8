@Library('swqa-ci')

import org.swqa.tools.common

common_libs = new common()

def getGpuIdMap() {
    return [
        'CI_GFX_S3000_Ubuntu_Linux_test_24.37': [
            'gpu_id': '3b',
            'test_node_guests': 'win10_CI_vdi_ubuntu_Test_01'
        ],
        'CI_GFX_S3000_Ubuntu_Linux_test_24.35': [
            'gpu_id': '3b',
            'test_node_guests': 'win10_CI_vdi_ubuntu_Test_02'
        ],
        'CI_GFX_S3000_Ubuntu_Linux_test_103.186': [
            'gpu_id': 'ca',
            'test_node_guests': 'win10_CI_vdi_ubuntu_Test_03'
        ]
    ]
}

def getBmcInfo(nodeName) {
    def parts = nodeName.tokenize('_')
    def ip = parts ? parts[-1] : ''
    ip = ip.replaceAll('-', '.')
    def bmcMap = [
        '24.35': [ip: '*************', credId: 'vdi_ci'],
        '24.37': [ip: '*************', credId: 'vdi_ci'],
        '103.186': [ip: '*************', credId: 'vdi_ci']
    ]
    def fullIp = ip
    def last2 = ip.tokenize('.').size() == 4 ? ip.tokenize('.')[-2..-1].join('.') : ip
    def result = bmcMap[fullIp] ?: bmcMap[last2] ?: [ip: '', credId: '']
    println "[getBmcInfo] nodeName=${nodeName}, ip=${ip}, fullIp=${fullIp}, last2=${last2}, result=${result}"
    return result
}

def cleanupPreviousVdi() {
    stage('cleanup previous VDI') {
        try {
            sh '''
                # Destroy VMs and clean up resources
                virsh destroy VDI-CI-1 vdi_01 2>/dev/null || true
                sleep 10
                rm -rf /root/mt_ci/qemu/win10_vdi_test_01.img /var/crash/* 2>/dev/null || true
                sudo pkill -f "/dev/dri/" 2>/dev/null || true
            '''
            echo 'VDI cleanup completed'
        } catch (e) {
            echo "VDI cleanup warning: ${e.message}"
        }
    }
}

def checkMtgpuStatus() {
    stage('mtgpu status check') {
        sh'''
            sudo rmmod mtgpu 2>log.txt || true
            sudo rmmod mtvgpu_basic 2>>log.txt || true
        '''
        if (fileExists('log.txt')) {
            try {
                sh(script: "grep 'Module mtgpu is not currently loaded' log.txt", returnStdout: true).trim()
            }catch (e) {
                reboothost()
            }
        }
    }
}

def installHostDriver() {
    stage('install host driver') {
        def kmd_pkg_name = env.hostDriverPkgUrl.split('/')[-1]

        timeout(time: 2, unit: 'HOURS') {
            while (true) {
                if (sh(script: "curl -s -k --max-time 30 -I ${env.hostDriverPkgUrl}", returnStatus: true) == 0) {
                    if (sh(script: "wget -q --no-check-certificate --timeout=300 ${env.hostDriverPkgUrl} -O ${kmd_pkg_name}", returnStatus: true) == 0) {
                        echo "Successfully downloaded: ${kmd_pkg_name}"
                        break
                    }
                    sh "rm -f ${kmd_pkg_name}"
                }

                echo 'Download failed, retrying in 5 minutes...'
                sleep(300)
            }
        }

        if (kmd_pkg_name.endsWith('.deb')) {
            sh"""
                echo "Installing DEB package: ${kmd_pkg_name}"
                sudo dpkg -i ./${kmd_pkg_name} || sudo apt-get install -f -y

                echo "Loading mtgpu kernel modules..."
                sudo modprobe mtgpu || echo "mtgpu module load failed, may need reboot"
                sudo modprobe mtvgpu_basic || echo "mtvgpu_basic module load failed, may need reboot"

                echo "Checking mdev support registration..."
                sleep 5
                lspci | grep -E "(VGA|3D|Display)" || echo "No display devices found"
                ls /sys/class/mdev_bus/ || echo "No mdev_bus devices found"
            """
        } else {
            error "Unsupported package format: ${kmd_pkg_name}. Only .deb packages are supported."
        }

        return kmd_pkg_name
    }
}

def prepareVmImages(image_path, img_list = ['win10_vdi_test_01']) {
    stage('prepare VM images') {
        dir(image_path) {
            img_list.each { img_name ->
                sh "qemu-img create -F qcow2 -f qcow2 -b ${img_name}_base.img ${img_name}.img"
            }
        }
    }
}

def createVgpuAndSetupVm(gpu_id, vm_number, vgpu_type, image_path) {
    timeout(10) {
        stage("create vGPU ${vgpu_type} and setup VM ${vm_number}") {
            // Check if mdev support is available for the GPU
            def mdev_check = sh(script: "ls /sys/class/mdev_bus/0000:${gpu_id}:00.0/mdev_supported_types/ 2>/dev/null | wc -l", returnStdout: true).trim()
            if (mdev_check == '0') {
                error "GPU 0000:${gpu_id}:00.0 does not have mdev support registered. Driver installation may have failed or system needs reboot."
            }

            echo "GPU 0000:${gpu_id}:00.0 has mdev support registered, proceeding..."

            sh """
                sudo mdevctl start -u c4f702ca-c69d-4d7d-a526-5fdcf78d34${vm_number} -p 0000:${gpu_id}:00.0 -t mtgpu-${vgpu_type}
                sleep 10
                virsh create ${image_path}/vdi_${vm_number}.xml
                sleep 60
            """
        }
    }
}

def setupMultipleVms(image_path = '/root/mt_ci/qemu') {
    stage('setup multiple VMs') {
        if (!env.testEnv) {
            error 'testEnv must be provided via environment variable env.testEnv'
        }

        def testEnv = env.testEnv.trim()
        echo "Using vGPU type: ${testEnv}"

        def gpu_id_map = getGpuIdMap()
        def gpu_id_data = gpu_id_map.get(env.NODE_NAME, [:])
        def gpu_id = gpu_id_data.gpu_id ?: ''
        def test_node_guest = gpu_id_data.test_node_guests ?: ''

        if (!gpu_id) {
            error "No GPU ID found for node: ${env.NODE_NAME}"
        }

        if (!test_node_guest) {
            error "No test node guest configured for node: ${env.NODE_NAME}"
        }

        echo "Setting up VM with vGPU type: ${testEnv}"
        echo "GPU ID: ${gpu_id}"
        echo "Test node guest: ${test_node_guest}"

        prepareVmImages(image_path)

        def vm_number = '01'
        def task_name = "VM${vm_number}_vGPU${testEnv}_${test_node_guest}"

        stage(task_name) {
            createVgpuAndSetupVm(gpu_id, vm_number, testEnv, image_path)

            sh """
                echo "VM ${vm_number} with vGPU ${testEnv} is ready on ${test_node_guest}"
                virsh list | grep vdi_${vm_number} || echo "Warning: VM vdi_${vm_number} not found in running list"
            """
        }

        return [
            testEnv: testEnv,
            gpu_id: gpu_id,
            test_node_guest: test_node_guest
        ]
    }
}

def reboothost() {
    stage('reboot host') {
        try {
            common_libs.reboot(env.NODE_NAME)
        } catch (e) {
            node('Linux_jump') {
                if (bmc?.ip && bmc?.credId) {
                    withCredentials([usernamePassword(credentialsId: bmc.credId, usernameVariable: 'BMC_USER', passwordVariable: 'BMC_PASS')]) {
                        sh """
                            echo "Graceful reboot failed, using BMC reset..."
                            ipmitool -I lanplus -H ${bmc.ip} -U ${BMC_USER} -P ${BMC_PASS} power reset
                        """
                    }
                } else {
                    echo "No BMC info found for node ${env.NODE_NAME}, cannot perform BMC reset."
                }
                print("Reboot triggered, connection might be interrupted: ${e}")
            }
        }
    }
}

def setNodeOffline() {
    stage('set node offline') {
        echo "Setting node ${env.NODE_NAME} offline before VDI setup"
        common_libs.setNodeStatus(env.NODE_NAME, 'offline')
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'cleanupPreviousVdi': [closure: { cleanupPreviousVdi() }],

        'checkMtgpuStatus': [closure: { checkMtgpuStatus() }],

        'installHostDriver': [closure: { installHostDriver() }],

        'setupMultipleVms': [closure: { setupMultipleVms() }],

        'setNodeOffline': [closure: { setNodeOffline() }]
    ]

    runPipeline(workflow, [disablePost:true])
}
