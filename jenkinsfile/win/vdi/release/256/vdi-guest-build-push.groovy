@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

env.buildBranch = env.buildBranch ?: env.ref.split('refs/heads/')[-1]
env.buildCommitId  = env.buildCommitId  ?: env.master_commit_id
tasksToRemove = []
disableSubmoduleUpdate = ['import/mtcc']

def time_tag_name = new Date().format('MM/dd/yyyy')
def version = "${time_tag_name}"
def baseUrl = "https://sh-moss.mthreads.com/sw-build/wddm/${env.buildBranch}/${env.buildCommitId [0..8]}"
def guest_driver_url = "${baseUrl}/${env.buildCommitId [0..8]}_wddm_vdi_release.tar.gz"
def m3d_test_url = "${baseUrl}/${env.buildCommitId [0..8]}_m3dTest.tar.gz"
def directstream_test_url = "${baseUrl}/${env.buildCommitId [0..8]}_directstreamTest.tar.gz"

if (env.buildM3d != 'true') {
    tasksToRemove << 'm3d'
}

if (env.buildDirectstream != 'true') {
    tasksToRemove << 'directstream'
}

if (env.buildDxc != 'true') {
    tasksToRemove << 'dxc'
    disableSubmoduleUpdate << 'dxc'
}

if (env.buildOGL != 'true') {
    tasksToRemove << 'ogl'
    disableSubmoduleUpdate << 'ogl'
}

if (env.buildVulkan != 'true') {
    tasksToRemove << 'Vulkan'
    disableSubmoduleUpdate << 'Vulkan'
}

def fetchCode() {
    def disable_submodule_update = disableSubmoduleUpdate.join(',')

    new git().fetchCode(env.repo, env.buildBranch, env.buildCommitId , [preBuildMerge: false, disableSubmodules: true, updateBuildDescription: true])
    new git().updateSubmodule(env.repo, 1, '', disable_submodule_update)

    def directstreamBranch = env.buildBranch
    if (env.buildBranch == 'release_vgpu_2.5.6_001' || env.buildBranch == 'vgpu_2.5.6_for_h3c') {
        directstreamBranch = 'release_vgpu_2.5.6'
    }

    if (env.buildDirectstream == 'true') {
        new git().fetchCode('DirectStream', directstreamBranch)
    }
    ciConfig = loadCiConfig()
}

def loadCiConfig() {
    def ciConfigContent = libraryResource('conf/vdi/winvdiconfig256000.yaml')
    if (env.buildBranch == 'release_vgpu_2.5.6_001' || env.buildBranch == 'vgpu_2.5.6_for_h3c') {
        ciConfigContent = libraryResource('conf/vdi/winvdiconfig256001.yaml')
    }
    def ciConfig = readYaml(text: ciConfigContent)
    echo 'ciConfig loaded successfully.'

    def vdiWddmConfig = ciConfig.vdi_wddm
    driverPath = vdiWddmConfig.product_paths.x64.common.release
    mtapi_url = ciConfig.mtapi_url

    return vdiWddmConfig
}

def extendTestPkg() {
    //upload m3d test
    if (env.buildM3d == 'true') {
        winBuild.upload_m3d(env.repo, env.buildBranch, env.buildCommitId , 'wddm\\m3d_lib\\m3d\\test')
    }
    //upload directstream test
    if (env.buildDirectstream == 'true') {
        winBuild.upload_directstream(env.repo, env.buildBranch, env.buildCommitId)
    }
}

def executeVdiBuild(String guest_driver_url, String m3d_test_url, String directstream_test_url) {
    runPipeline.runJob([
        job: 'M-vdi-host256',
        wait:false,
        parameters: [
            guest_driver_url: guest_driver_url,
            m3d_test_url: m3d_test_url,
            directstream_test_url: directstream_test_url,
            buildBranch: env.buildBranch
        ]
    ])
}

runner.start(env.runChoice) {
    //init
    new common().initEnv(env.NODE_NAME, "192.168.${env.NODE_NAME.split('_')[-1]}")

    deleteDir()

    def workflow = [
        'checkout': [closure: { fetchCode() }],

        'build': [closure: { winBuild.integratedBuildProcess(ciConfig, tasksToRemove, env.driver_env, env.driver_type, env.chip_type, env.build_type) }],

        'pkgProducts' : [closure: { winBuild.pkgProducts(ciConfig, env.driver_type, env.build_type, tasksToRemove) }],

        'fetchMtapi' : [closure: { winBuild.fetchMtapi(driverPath, mtapi_url) }],

        'signature' : [closure: { winBuild.signature(ciConfig, env.driver_type, version, '', 0, 'wddm\\build\\wddm\\mtgpu\\INFs') }],

        'upload' : [closure: { winBuild.upload(ciConfig, env.repo, env.buildBranch, env.buildCommitId , env.driver_type) }],

        'extendTestPkg' : [closure: { extendTestPkg() }],

        'executeVdiBuild' : [closure: { executeVdiBuild(guest_driver_url, m3d_test_url, directstream_test_url) }]
    ]

    runPipeline(workflow, [disablePost:true])
}
