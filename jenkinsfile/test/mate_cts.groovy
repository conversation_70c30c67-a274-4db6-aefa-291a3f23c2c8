@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repo = 'mate'
env.mateCtsRepo = 'mate_cts'
envExport = env.exports ? commonLib.parseExportVars(env.exports).join(';') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def setEnv() {
    // ddk
    if (env.linuxDdkPackageUrl.contains('deb')) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }

    // musa_toolki || MUSA-Runtime + mtcc
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
    } else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
    }

    //muDNN
    if (env.muDNNPackageUrl) {
        constants.downloadAndUnzipPackage(env.muDNNPackageUrl)
        sh 'cd mudnn; ./install_mudnn.sh -i'
    }

    //mccl
    if (env.mcclPackageUrl) {
        constants.downloadAndUnzipPackage(env.mcclPackageUrl)
        sh 'cd mccl; ./install.sh -i'
    }
}

def installDependency() {
    // muAlg
    if (env.muAlgPackageUrl) {
        musa.installmuAlg(env.muAlgPackageUrl)
    } else {
        gitLib.fetchCode('muAlg', 'develop', null, [disableSubmodules: true, updateBuildDescription: true])
        dir('muAlg') { sh './mt_build.sh -i' }
    }

    // muThrust
    if (env.muThrustPackageUrl) {
        musa.installmuThrust(env.muThrustPackageUrl)
    } else {
        gitLib.fetchCode('muThrust', 'develop', null, [disableSubmodules: true, updateBuildDescription: true])
        dir('muThrust') { sh './mt_build.sh -i' }
    }

    // conda
    constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')

    // torch_musa
    constants.downloadPackage(env.torchPackageUrl)
    constants.downloadPackage(env.torchMusaPackageUrl)
    sh """
        ${constants.genCondaActivate(env.condaEnv)}
        pip install --force-reinstall ./torch*.whl
    """
}

def installMate() {
    if (env.matePackageUrl) {
        constants.downloadAndUnzipPackage(env.matePackageUrl)
        sh """
            ${constants.genCondaActivate(env.condaEnv)}
            ${envExport}
            cd dist
            pip install *.whl
        """
    }
}

def test() {
    dir('mate') {
        sh """
            ${constants.genCondaActivate(env.condaEnv)}
            ${envExport}
            cd tests
            ${env.runCmd}
        """
    }
}

// def checkResult() {
//     dir("${env.WORKSPACE}/${env.repo}") {
//         commonLib.allure('test-report')
//     }
// }

// def uploadTestResult() {
//     oss.install()
//     generateTestUrl.each {
//         oss.cp("${env.WORKSPACE}/${env.repo}/mate_cts_allure_result.tar.gz", it)
//         currentBuild.description += "<br>generate_pkg:${it}/mate_cts_allure_result.tar.gz".replaceAll('//', '/')
//     }
// }

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'env-setup': [closure: { setEnv() }],
        'install-rely': [closure: { installDependency() }],
        'install-mate': [closure: { installMate() }],
        'test': [closure: { test() }]
    ]
    runPipeline(workflow)
}
