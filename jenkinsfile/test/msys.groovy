@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

def installDriver() {
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
}

def installPkg() {
    dir('msys') {
        String urlPrefix = 'https://sh-moss.mthreads.com/sw-build/msight-system'
        String pkgUrlPrefix = sh(script: "curl --insecure ${urlPrefix}/${env.pkgBranch}/latest.txt", returnStdout: true).trim()
        String pkgVersion = sh(script: "curl --insecure ${pkgUrlPrefix}_internal_version.txt", returnStdout: true).trim()
        String name = env.pkgBranch == 'develop' ? '_moore-perf-system_' : '_moore-perf-system_kuae_'
        String pkgUrl = pkgUrlPrefix + name + pkgVersion + '_x86_64_internal.deb'
        constants.downloadPackage(pkgUrl)
        sh 'dpkg -i *.deb'
        sh 'apt install g++-12 -y'
    }
}

def runTest() {
    gitLib.fetchCode('qa_sdk', 'develop', null)
    oss.install()
    dir('qa_sdk') {
        sh """
            cd msight/assets
            mc cp sh-moss/sw-build/computeQA/cuda_compatible/newest/master/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz .
            tar xzf mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz
            ./install.sh
            export PATH=/usr/local/musa/bin:\$PATH
            export LD_LIBRARY_PATH=/usr/local/musa/lib/:\$LD_LIBRARY_PATH
            cd ../
            pytest test_cases/msys/test_profile_musa.py::TestProfile::test_profile_musa_base test_cases/msys/test_profile_musa.py::TestProfile::test_profile_musa_simpleStreams
        """
    }
    def dmesg = sh(script: 'dmesg -T', returnStdout: true).trim()
    if (dmesg =~ 'ERROR: ioctl args checksum mismatch') {
        throw new Exception('Test failed: octl args checksum mismatch')
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'installDriver': [ closure: { installDriver() } ],
        'installPkg': [ closure: { installPkg() } ],
        'runTest': [ closure: { runTest() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    ]
    runPipeline(workflow)
}
