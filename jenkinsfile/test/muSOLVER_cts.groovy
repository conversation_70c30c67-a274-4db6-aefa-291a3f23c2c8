@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * cmd (String) - ./build.sh -d ./install -j 16 -c mp21
 * exports (Multiline String) default 'usual export', split by ';'
 * dependcy (Multiline String) - default 'musolver_data', split by ';
 * musaToolkitsPackageUrl (String) - default 'musatoolkit url'
 * muSOLVERPackageUrl (String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

env.repoDev = 'muSOLVER'
env.repo = 'muSOLVER_cts'
env.branch = env.muSOLVERCtsBranch ? env.muSOLVERCtsBranch : env.branch
env.commitId = env.muSOLVERCtsBranch ? '' : env.commitId
envExport = utils.generateEnvExport(env)
testType = env.testType ?: 'smoke'
coverage_report_name = ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muRANDPackageUrl = env.muRANDPackageUrl?.trim() ? env.muRANDPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musolverPackageUrl = env.musolverPackageUrl?.trim() ? env.musolverPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muBLASPackageUrl = env.muBLASPackageUrl?.trim() ? env.muBLASPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    if (env.gitlabSourceRepoName != env.repo) {
        env.branch = commonLib.findMrDependency(env.repo, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: '')) ?: env.branch
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    dir('dependencyDir') {
        oss.install()
        for (_ in env.dependcy.split(';')) {
            if (_) {
                oss.cp(_.replace('\n', '').trim(), './')
            }
        }
        sh 'tar -xzf musolver_data.tar.gz'
        envExport += "&& export MUSOLVER_DATA_DUMP_DIR=${env.WORKSPACE}/dependencyDir/data"
    }
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
        if (env.musaToolkitsSrcPackageUrl) {
            constants.downloadAndUnzipPackage(env.musaToolkitsSrcPackageUrl, '/go/build/musa_toolkit_master_gcov/')
        }
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
        constants.downloadAndUnzipPackage(env.muSOLVERPackageUrl)
        sh 'cd muSOLVER && chmod +x install.sh . && ./install.sh'
        constants.downloadAndUnzipPackage(env.muBLASPackageUrl)
        sh 'cd muBLAS && chmod +x install.sh . && ./install.sh'
    }

// musa.installMusify(env.musifyPackageUrl)
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        // build
        dir(env.repo) {
            sh """
                ${envExport}
                export TEST_TYPE=${testType}
                git submodule update --init --recursive
                mkdir -p build && cd build
                cmake .. ${env.compileArgs}
                make -j ${env.compileParallel}
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${constants.genCondaActivate('mathx')}
                ${envExport}
                export TEST_TYPE=${testType}
                export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
                sync && echo 3 > /proc/sys/vm/drop_caches ||:
                python run_test.py ${env.testArgs} ||:
            """
        }
        // coverage
        if (env.gCover == 'true') {
            runCoverage()
        }
    }

    if (env.TIMEDEBUG?.trim()) {
        timeout(time: env.TIMEDEBUG.toInteger(), unit: 'HOURS') {
            input message: "CI环境将保留 ${env.TIMEDEBUG} 小时，请选择:\nProceed(继续流水线)\n Abort(终止流水线)"
        }
    }
}

def runCoverage() {
    dir(env.repo) {
        llvm_cov_plan = mathxCoverage.llvm_cov_plan.get(env.repoDev)
        println "llvm_cov_plan: ${llvm_cov_plan}"
        coverage_report_name = mathxCoverage.mathxGenerateCoverage(llvm_cov_plan.'llvm_cov_directory', llvm_cov_plan.'src_remove', llvm_cov_plan.'so_name', env.repoDev)
        println "coverage_report_name: ${coverage_report_name}" // muSOLVER_llvm_coverage_report
        if (coverage_report_name) {
            // 读取代码覆盖率数据，并写入influxdb
            catchError(stageResult: 'FAILURE') {
                commonLib.loadScript('mathx_coverage_writetoInfluxDB.py', 'coverage', false)
                sh """
                    ${constants.genCondaActivate('mathx')}
                    ls -l
                    python mathx_coverage_writetoInfluxDB.py --indexfile ${env.WORKSPACE}/${env.repo}/${coverage_report_name}/index.html --product ${env.repoDev}
                """
            }
            commonLib.publishHTML(coverage_report_name, coverage_report_name)
        }
        else {
            println 'generate coverage fail!'
            currentBuild.description += '<b>generate coverage fail!</b><br>'
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh "tar -czvf ${env.repo}_allure_result.tar.gz test-report"
        artifact.uploadTestReport("${env.repo}_allure_result.tar.gz", env.reportOssPath)

        catchError(stageResult: 'FAILURE') {
            if (env.gCover == 'true') {
                sh"""
                    pwd
                    ls -l
                    tar -czvf ${coverage_report_name}.tar.gz ${coverage_report_name}
                    ls -l
                """
                artifact.uploadTestReport("${coverage_report_name}.tar.gz", env.reportOssPath)
            }
        }
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'muSOLVER test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'upload result': [closure: {
                catchError(stageResult: 'FAILURE') {
                        uploadTestResult()
                }
            }
            ],
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
