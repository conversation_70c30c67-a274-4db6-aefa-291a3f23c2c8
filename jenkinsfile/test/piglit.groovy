@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

// runChoice nodeLabel kmdPackageUrl umdPackageUrl openGL41CaseListUrl openGL42CaseListUrl

def runTest(String caseListUrl) {
    def caseList = caseListUrl.split('/')[-1]
    def logPath = "${env.WORKSPACE}/${caseList.split('_pr')[0]}_logs"
    try {
        commonLib.loadScript('piglit_test.sh')
        commonLib.checkGlxinfo()
        sh """
            wget -q --no-check-certificate ${caseListUrl}
            mkdir -p ${logPath}
            cp ${caseList} /root/
            export DISPLAY=:0.0
            ${env.WORKSPACE}/piglit_test.sh ${logPath} ${caseList}
        """
    } catch (ex) {
        timeout(5) {
            sh """
                cp -rf /usr/local/var/log/Xorg.0.log ${logPath}/
                cat ${logPath}/tmp_cases.txt
            """
        // tmp_cases created by piglit_test.sh
        }
        throw(ex)
    } finally {
        commonLib.killProcess('Xorg')
    }
}

def installDriver() {
    try {
        ddk.install(true)
    } catch (ex) {
        commonLib.publishHTML("${env.WORKSPACE}/piglit_logs/html", '*.html', 'HTML Report')
        sh 'timeout 5 dmesg'
        commonLib.reboot(env.NODE_NAME)
        throw new Exception('Test failed!')
    }
}

def runPiglitTest() {
    try {
        // remove useless caselist
        sh 'cd /root && rm -f piglit_pr_develop.txt lianying_piglit.txt ||:'
        //run openGL4.1 test case
        runTest(env.openGL41CaseListUrl)
        // run openGL4.2 test case
        // musa.ini will be recovered when install umd
        if (env.openGL42CaseListUrl) {
            sh '''
                cd /etc && rm -rf musa.ini && wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-release/cts/musa.ini
                /usr/lib/xorg/Xorg &
                sleep 5
            '''
            runTest(env.openGL42CaseListUrl)
        }
    } catch (ex) {
        throw new Exception('test failed!')
    } finally {
        sh 'cd /etc && rm -rf musa.ini'
    }
    commonLib.publishHTML("${env.WORKSPACE}/piglit_logs/html", '*.html', 'HTML Report')
}

runner.start(env.runChoice) {
    def workflow = [
        'install driver': [closure: { installDriver() }, handler: 'closure'],
        'piglit test': [closure: { runPiglitTest() }, handler: 'closure', setGitlabStatus: true, statusName: env.testLabel, maxWaitTime: [time: 45, unit: 'MINUTES']],
    ]

    runPipeline(workflow, [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
