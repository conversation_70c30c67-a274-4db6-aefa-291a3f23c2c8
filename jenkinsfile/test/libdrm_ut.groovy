@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice nodeLabel ddkBranch linuxDdkPackageUrl caseListUrl

def installDriver() {
    if (env.compatDdkPackageUrl && env.gitlabSourceRepoName == 'libdrm-mt') {
        // libdrm-mt MR, get libdrm_mtgpu.so from MR, get kmd & mtfw-gen*.bin from compatDdkBranch
        // gr-kmd MR, get libdrm_mtgpu.so from compatDdkBranch, get kmd & mtfw-gen*.bin from MR
        def temp = env.compatDdkPackageUrl
        env.compatDdkPackageUrl = env.linuxDdkPackageUrl
        env.linuxDdkPackageUrl = temp
    }
    ddk.installLinuxDdkAndSetup()
    if (env.compatDdkPackageUrl) {
        dir('compatDdk') {
            constants.downloadPackage(env.compatDdkPackageUrl)
            sh '''
                dpkg-deb -R *.deb temp/
                cp -rf -P \$(ls -d temp/usr/lib/*gnu)/libdrm_mtgpu.so* \$(ls -d /usr/lib/*gnu)/
                cp -rf temp/home/<USER>/mtgpu_test /home/<USER>/mtgpu_test
            '''
        }
    }
    // this needs to be done on host, not container
    kmemleak.clearKmemleak()
}

def runLibdrmUtTest() {
    try {
        def exportStr = env.gitlabMergeRequestIid ? "export TEST_TYPE=${env.testType};" : 'export TEST_TYPE=daily;'
        gitLib.fetchCode('linux-ddk', env.ddkBranch, null, [preBuildMerge: true, noTags: true, disableSubmodules: true])
        utPath = fileExists('linux-ddk') ? 'linux-ddk/libdrm-mt/pytest' : 'libdrm-mt/pytest'
        if (utPath.contains('linux-ddk')) {
            credentials.runWithCredential('SSH_GITLAB') {
                dir('linux-ddk') {
                    sh 'git submodule update --init libdrm-mt'
                    if (env.gitlabSourceBranch && env.gitlabSourceRepoName == 'libdrm-mt') {
                        sh "cd libdrm-mt && git checkout ${env.gitlabSourceBranch}"
                    }
                }
            }
        }

        commonLib.runRelyNetwork(3, 10) {
            sh 'pip3 install allure-pytest pandas pytest'
        }
        dir(utPath) {
            // if (env.caseListUrl) {
            //     sh "wget -q --no-check-certificate ${env.caseListUrl} -O libdrm_mr_test_list.txt"
            // }
            exportStr += "export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:\$LD_LIBRARY_PATH"
            def stdout = ''
            timeout(time: env.testTimeout, unit: 'MINUTES') {
                stdout = sh(script: "${exportStr}; ${env.cmd} --alluredir=${env.allureDir}", returnStdout: true).trim()
            }
            print(stdout)
            if (stdout.contains('collected 1 item')) {
                error 'env error!'
            }
            if (stdout.contains('AssertionError: run fail!')) {
                error 'test failed!'
            }
        }
        // this needs to be done on host, not container
        // only kmd pr need this check
        // not every node has the ability to do kmemleak check
        kmemleak.checkMemoryIssues()
    } catch (exc) {
        throw new Exception('test failed!')
    } finally {
        commonLib.allure("${utPath}/${env.allureDir}", 'ddk2.0-allure_report')
    }
}

runner.start(env.runChoice) {
    env.ddkBranch = env.compatDdkBranch ?: (env.gitlabSourceRepoName == 'linux-ddk' ? env.gitlabSourceBranch : (env.ddkBranch ?: 'master'))
    runPipeline([
        'install driver': [closure: { installDriver() }, maxWaitTime: [time: 30, unit: 'MINUTES']],
        'ddk libdrm ut': [closure: { runLibdrmUtTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ], [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
