@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

commonLib = new common()
gitLib = new git()

// runChoice nodeLabel dockerImage linuxDdkPackageUrl chipType vpsVersion model tagDate
logFile = "${env.build_ID}_vps.log"
testResultsUrl = "https://oss.mthreads.com/${env.ossTestResultsSavePath}"
sshLoginCmd = 'sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no'

chips = env.chips ? env.chips.split(',') : ['hg']
models = ['amodel']
// driver_modes = ['metaOnly', 'fec']
// driver_mode_params_map = ['metaOnly': "${env.metaOnlyParams}", 'fec': "${env.fecParams}"]
driver_modes = ['metaOnly']
driver_mode_params_map = ['metaOnly': "${env.metaOnlyParams}"]

def downloadImg() {
    // if restart vps exists in following steps, we need to copy vps img to workspace and delete it afterwards
    // vps storage will lost after rebooting vps, when starting vps with option -snapshot
    def imgPath = '/data/qemu/linux/cts_vps'
    if (!fileExists("${imgPath}/${env.vpsImg}")) {
        sh """
            mkdir -p ${imgPath} && cd ${imgPath}
            sudo wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/computeQA/vps/vps-img/${env.vpsImg}
            sleep 5
        """
    }
}

def runVpsTest() {
    sh """
        cd ${env.WORKSPACE}
        wget -q --no-check-certificate ${env.linuxDdkPackageUrl}
    """
    if (env.kmdPackageUrl) {
        sh """
            cd ${env.WORKSPACE}
            wget -q --no-check-certificate ${env.kmdPackageUrl}
        """
    }
    // Ensure env.amodelPath ends with '/' so later path joins work correctly
    if (env.amodelPath) {
        if (!env.amodelPath.endsWith('/')) {
            env.amodelPath = "${env.amodelPath}/"
        }
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    if (env.gitlabSourceRepoName == 'libdrm-mt') {
        gitLib.fetchCode('libdrm-mt', env.branch, env.commitId, [updateBuildDescription: true])
    } else {
        def libdrmBranch = commonLib.findMrDependency('libdrm-mt', (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
        gitLib.fetchCode('linux-ddk', env.ddkBranch, null, [preBuildMerge: true, noTags: true, disableSubmodules: true])
        credentials.runWithCredential('SSH_GITLAB') {
            dir('linux-ddk') {
                sh 'git submodule update --init libdrm-mt'
                if (libdrmBranch) {
                    sh "cd libdrm-mt && git checkout ${libdrmBranch}"
                }
            }
        }
    }
    utPath = fileExists('linux-ddk') ? 'linux-ddk/libdrm-mt/pytest' : 'libdrm-mt/pytest'

    try {
        chips.each { chip ->
            models.each { model ->
                driver_modes.each { driver_mode ->
                    def kmd_insmod_params = driver_mode_params_map[driver_mode]
                    println "chip: ${chip}, model: ${model}, driver_mode: ${driver_mode}, kmd_insmod_param: ${kmd_insmod_params}"

                    docker.image(env.dockerImage).inside("--privileged -i -u 0:0 --hostname qemu-dev -v ${env.WORKSPACE}:/root/test/pkg -v /data/qemu/linux/cts_vps:/root/images") {
                        // setup vps
                        oss.install('mtoss', 'mtoss123')
                        new common().runRelyNetwork(3, 10) {
                            sh 'apt install -y sshpass'
                        }
                        // TODO: move below before sed -i when dev resolve issues
                        // cp -f /root/workspace/soc_model/release_mode/amodel/${chip}/linux/gfx_config.yaml /root/workspace/
                        sh """
                            mc cp ${env.amodelPath} /root/workspace/soc_model/release_mode/amodel/${chip}/ -r
                            sed -i 's/^\\(\\s*shared_memory_enable:\\s*\\)false/\\1true/' /root/workspace/gfx_config.yaml
                        """
                        sh """
                            /root/run_qemu.pl -r ${env.vpsVersion} -g /root/images/${env.vpsImg} -extra_args "-net user,hostfwd=tcp::10022-:22 -net nic -m 8G -virtfs local,path=/root/test/pkg,mount_tag=host0,security_model=passthrough,id=host0 --enable-kvm -snapshot" -chip_type ${chip} -mode ${model} -display_none > ${env.workspace}/${chip}_${model}_${driver_mode}_${logFile} &
                        """

                        timeout(10) {
                            sh '''
                                while ! timeout 10 sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no; do
                                    sleep 5
                                done
                            '''
                        }

                        // insmod kmd
                        sh """
                            ${sshLoginCmd} 'rm -rf B${env.build_ID}_logs && mkdir -p ddk_vps B${env.build_ID}_logs'
                            ${sshLoginCmd} 'mkdir -p /root/test/pkg && mount -t 9p -o trans=virtio,version=9p2000.L host0 /root/test/pkg || true'
                            ${sshLoginCmd} 'chmod +x /root/test/pkg/*'

                            ${sshLoginCmd} 'dpkg -P musa ||:'
                            ${sshLoginCmd} 'dpkg -i /root/test/pkg/*_vps_ddk2.0.deb ||:'
                        """

                        if (env.kmdPackageUrl) {
                            sh """
                                ${sshLoginCmd} 'dpkg -i --force-all /root/test/pkg/*mtgpu-*******.deb ||:'
                            """
                        }
                        if (env.musaRuntimePackageUrl) {
                            sh "${sshLoginCmd} 'cd /root/test/pkg/MUSA-Runtime && ./install.sh'"
                        }
                        sh """
                            ${sshLoginCmd} 'modprobe -rv mtgpu && modprobe -v mtgpu ${kmd_insmod_params}'
                            ${sshLoginCmd} 'cd /root/test/pkg/${utPath} && export TEST_TYPE=${env.testType} && pytest -v test_libdrm_ut.py::test_libdrm_ut --alluredir=/root/test/pkg/results_${chip}_${model}_${driver_mode}'
                        """
                    }
                }
            }
        }
    } catch (exc) {
        throw new Exception('test failed!')
    } finally {
        // allure report
        chips.each { chip ->
            models.each { model ->
                driver_modes.each { driver_mode ->
                    def resultsDir = "${env.WORKSPACE}/results_${chip}_${model}_${driver_mode}"
                    def reportName = "${chip}_${model}_${driver_mode}_allure_report"
                    if (fileExists(resultsDir)) {
                        commonLib.allure(resultsDir, reportName)
                    } else {
                        echo "Warning: ${resultsDir} not found"
                    }
                }
            }
        }
        // update log
        oss.install('mtoss', 'mtoss123')
        sh """
            ${sshLoginCmd} 'dmesg -T' ||:
            cd ${env.WORKSPACE}
            mc cp *${logFile} sh-moss/${env.ossTestResultsSavePath}/
        """
        currentBuild.description += "Test Results PKG: ${testResultsUrl} <br>"
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'download img': [closure: { downloadImg() }],
        'vps test': [closure: { runVpsTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ])
}
