@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()
// env.pipelineTimeout = 120
// TODO: move test binary to image
/*
 * parameters
 * kmdPackageUrl (String)
 * umdPackageUrl (String)
 * logAddress (String)
 * support multiple test
    testConfig (Multiline String):
    {
        "gl4.1": {
            "workdir": "/root/cts_gl",
            "binary": "glcts",
            "case": "https://sh-moss.mthreads.com/sw-release/cts/gl/gl_cts_pr.txt"
        },
        "gl4.2+": {
            "workdir": "/root/cts_gl",
            "binary": "glcts",
            "case": "https://sh-moss.mthreads.com/sw-release/cts/gl/gl_cs_cts_pr.txt"
        },
        "gles": {
            "workdir": "/root/cts_gles",
            "binary": "glcts",
            "case": "https://sh-moss.mthreads.com/sw-release/cts/gles/gles_cts_pr.txt"
        },
        "gles2": {
            "workdir": "/root/cts_gles",
            "binary": "glcts",
            "case": "https://sh-moss.mthreads.com/sw-release/cts/gles/gles_cts_pr_ddk2.txt"
        },
        "vk": {
            "workdir": "/root/cts_vk",
            "binary": "deqp-vk",
            "case": "https://sh-moss.mthreads.com/sw-release/cts/vk/vk_cts_pr.txt"
        },
        "vk2": {
            "workdir": "/root/cts_vk_vulkan",
            "binary": "deqp-vk",
            "case": "https://sh-moss.mthreads.com/sw-release/cts/vk/vk_cts_vulkan.txt"
        }
    }
//  * workdir (String)
//  * binary (String)
//  * case (String)
*/

def installDriver() {
    if (env.linuxDdkPackageUrl) {
        // ddk2.0
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl, false) {
            if (env.vulkanPackageUrl) { ddk.installLinuxDdk(env.vulkanPackageUrl) }
            if (env.oglPackageUrl) { ddk.installLinuxDdk(env.oglPackageUrl) }
            if (env.mtccPackageUrl) { installDependency(['mtcc': env.mtccPackageUrl]) }
        }
    } else {
        // legacy ddk
        ddk.install()
    }
}

def exportDisplay(int retryCount = 3) {
    retry(retryCount) {
        commonLib.startXorg('/usr/lib/xorg/Xorg')
        timeout(time: 10, unit: 'SECONDS') {
            commonLib.checkGlxinfo()
        }
    }
}

def runCase(String workdir, String testBin, String caseUrl, String logfile, String singleExport = '') {
    try {
        exportDisplay()
        kmemleak.clearKmemleak()
        sh '''
            nproc; uptime; free -h
        '''
        // nproc查看核心数，uptime查看负载，free -h查看内存使用情况
        Number fdCount = sh(script: 'ulimit -n', returnStdout: true).trim().toInteger()
        String fdCmd = fdCount < 4096 ? 'ulimit -n 4096; ulimit -n' : 'ulimit -n'
        if (env.exportEnv) {
            fdCmd = "${fdCmd}; ${env.exportEnv}; ${singleExport}"
        }
        dir(workdir) {
            String caseFilename = caseUrl.split('/')[-1]
            // TODO: handle githab url
            sh "wget -q --no-check-certificate ${caseUrl} -O ${caseFilename}"
            sh """
                # print pid for later debug
                ${fdCmd}
                export DISPLAY=:0.0 vblank_mode=0 && cd ${workdir}/ && ./${testBin} --deqp-caselist-file=${caseFilename} | tee ${logfile}
            """
        // sh """
        //     # print pid for later debug
        //     ${fdCmd}
        //     export DISPLAY=:0.0 && cd ${workdir}/ && ./${testBin} --deqp-caselist-file=${caseFilename} > ${logfile} &
        //     pid=\$!
        //     echo "${testBin} PID: \$pid"
        //     wait \$pid
        // """
        }
    } catch (e) {
        sh 'cat /usr/local/var/log/Xorg.0.log'
        sh 'cat /sys/kernel/debug/musa/gpu00/firmware_trace ||:'
        throw(e)
    } finally {
        if (fileExists(logfile)) { sh "cat ${logfile}" }
        def passRate = sh(script: "awk '/Test run totals:/ {found=1} found' ${logfile}", returnStdout: true).trim()
        def failCases = sh(script: "awk '/Fail \\(/ {print prev; print} {prev=\$0}' ${logfile}", returnStdout: true).trim()
        print(passRate)
        if (failCases) {
            print('FailCases :\n' + failCases)
            sh 'cat /sys/kernel/debug/musa/gpu00/firmware_trace ||:'
        }
        // only kmd pr need this check
        // not every node has the ability to do kmemleak check
        def logAddress = env.logAddress ?: "sh-moss/sw-pr/${env.gitlabSourceRepoName}/${env.gitlabMergeRequestIid}/${env.BUILD_ID}/log"
        kmemleak.checkMemoryIssues(logAddress, 1000)
    }
}

def runGraphicCts() {
    Map testConfig = readJSON text: env.testConfig
    // 检查testConfig是否包含vk，如果包含则先安装vulkan-tools并运行vulkaninfo
    boolean hasVkConfig = testConfig.keySet().any { key -> key.toLowerCase().contains('vk') }
    if (hasVkConfig) {
        sh 'sudo apt update ||:'
        sh 'sudo apt install -y vulkan-tools ||:'
        sh 'vulkaninfo ||:'
    }
    Map defaultCaseList = [
        'gl4.1': 'https://sh-moss.mthreads.com/sw-release/cts/gl/gl_cts_pr.txt',
        'gl4.2+': 'https://sh-moss.mthreads.com/sw-release/cts/gl/gl_cs_cts_pr.txt',
        'gles': 'https://sh-moss.mthreads.com/sw-release/cts/gles/gles_cts_pr.txt',
        'gles2': 'https://sh-moss.mthreads.com/sw-release/cts/gles/gles_cts_pr_ddk2.txt',
        'vk': 'https://sh-moss.mthreads.com/sw-release/cts/vk/vk_cts_pr.txt',
        'vk2': 'https://sh-moss.mthreads.com/sw-release/cts/vk/vk_cts_vulkan.txt',
    ]
    String logPath = "${env.WORKSPACE}/log"
    sh "mkdir -p ${logPath}"
    String combineLog = "${logPath}/TestResults.log"
    sh "echo > ${combineLog}"
    // AssertOnHWRTrigger when debug
    if (env.assertOnHWRTrigger.toBoolean()) {
        sh 'echo Y > /sys/kernel/debug/musa/apphint/0/AssertOnHWRTrigger'
        sh 'echo 1 > /sys/kernel/debug/musa/gpu00/disable_hwr'
    }
    try {
        int testCount = 0
        testConfig.each { name, config ->
            testCount += 1
            String caseUrl = config.case ?: defaultCaseList[name]
            String logfile = "${logPath}/${name}_TestResults.log"
            String singleExport = config.exportEnv ?: ''
            if (name == 'gl4.2+') {
                sh 'cd /etc/ && rm -rf musa.ini && wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-release/cts/musa.ini'
            }
            retry(env.maxRetries.toInteger()) {
                timeout(time: env.ctsTimeout, unit: 'MINUTES') {
                    runCase(config.workdir, config.binary, caseUrl, logfile, singleExport)
                    sh "mv ${config.workdir}/TestResults.qpa ${logPath}/TestResults_${name}.qpa"
                }
            }
            sh "cat ${logfile} >> ${combineLog}"
        }
        dir(logPath) {
            def scriptContent = gitLib.getFileContentByApi('mt-gfx-test', 'master', 'scripts/check_cts_result.sh')
            def isEnableHWRCheck = env.isEnableHWRCheck.toBoolean() ? '1' : '0'
            writeFile(file: 'check_cts_result.sh', text: scriptContent)
            sh "cat ${combineLog}; chmod 755 check_cts_result.sh; ./check_cts_result.sh ${testCount} ${combineLog} ${isEnableHWRCheck}"
        }
        sh """
            for i in \$(seq 1 10); do ! ps -ef |grep Xorg|grep -v grep && break;pkill Xorg;sleep 3;done
        """
    } catch (e) {
        throw(e)
    } finally {
        timeout(time: 15, unit: 'MINUTES') {
            env.logAddress = env.logAddress ?: "sh-moss/sw-pr/${env.gitlabSourceRepoName}/${env.gitlabMergeRequestIid}/${env.BUILD_ID}/log/"
            utils.catchErrorContinue {
                oss.install()
                String logPackage = "${env.build_ID}_graphic_cts_logs.tar.gz"
                sh """
                    rm -rf /lib/firmware/musa*
                    cd /etc/ && rm -rf musa.ini
                    dmesg -T |tee ${logPath}/dmesg.log
                    tar czf ${logPackage} ${logPath}
                    mc cp ${logPackage} ${env.logAddress}
                """
                String logPackageUrl = constants.ossPathToUrl(env.logAddress) + logPackage

                currentBuild.description += "Test Results PKG: ${logPackageUrl} <br>"
            }
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'check dependency': [closure: { commonLib.checkCtsDeps() }],
        'install driver': [closure: { installDriver() }, maxWaitTime: [time: 15, unit: 'MINUTES']],
        'graphic cts test': [closure: { runGraphicCts() }, setGitlabStatus: true, statusName: env.testLabel]
    ]

    runPipeline(workflow, [
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
