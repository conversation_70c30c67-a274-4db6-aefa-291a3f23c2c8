@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.atf

commonLib = new common()

properties([
    parameters([
        string(name: 'linuxDdkPackageUrl', defaultValue: '', description: ''),
        string(name: 'concurrency', defaultValue: '100', description: '测试并发数'),
        string(name: 'timeout', defaultValue: '48', description: 'in HOURS'),
        string(name: 'atfplanId', defaultValue: '', description: ''),
        choice(name: 'ctsApp', choices: ['OpenGL', 'GLES', 'Vulkan'], description: ''),
        string(name: 'cardId', defaultValue: '1', description: ''),
        choice(name: 'runChoice', choices: ['node', 'pod'], description: ''),
        string(name: 'nodeLabel', defaultValue: '', description: ''),
    ])
])
def recoverEnv() {
    commonLib.recoverEnv()
}

def installDriver() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdkAndSetup()
    } else {
        ddk.install()
    }
}

def runAtfJob(String dutIp, String planId, String ctsApp, Integer cardId=1) {
    // binpath
    def appToBinPath = [
        'OpenGL': '/root/cts_gl',
        'GLES': '/root/cts_gles',
        'Vulkan': '/root/cts_vk'
    ]
    def binPath = appToBinPath."${ctsApp}"
    def today = new Date().format('yyyyMMdd')
    env.jobTitle = "${today}-${ctsApp}_${env.JOB_NAME}#${env.BUILD_NUMBER}#${cardId}"
    def executeData = """
        {
            "title": "${env.jobTitle}",
            "executionId": 240001,
            "config" : {
                "params": {
                    "atf_device_id": ${cardId},
                    "atf_pull_size": ${env.concurrency},
                    "dut_ip": "${dutIp}",
                    "bin_path": "${binPath}",
                    "cardslot": "0.${cardId - 1}",
                    "parallel": ${env.concurrency},
                    "timeout": "60"
                },
                "image": "sh-harbor.mthreads.com/service/atf-v4",
                "scheduleStrategy": "hw",
                "retryTimes": "3",
                "retryResults": [
                    "RESULT_UNKNOWN",
                    "RESULT_FAIL",
                    "RESULT_NOT_SUPPORTED",
                    "RESULT_TIMEOUT",
                    "RESULT_CRASH"
                ]
            },
            "planId": ${planId}
        }
    """
    def response = new atf().startExecution(executeData)
    env.jobId = response.jobId
    currentBuild.description += "ATF Job ID: ${env.jobId} <br>"
    currentBuild.description += "Driver: <a href='${env.linuxDdkPackageUrl}' target='_blank'>${env.linuxDdkPackageUrl}</a> <br>"

    def shouldExitLoop = false
    while (!shouldExitLoop) {
        jobResult = new atf().getAtfJobResult(env.jobId)
        if (jobResult.status != 'STATUS_RUNNING' && jobResult.status != 'STATUS_RUNNABLE' && jobResult.status != 'STATUS_NEW') {
            println('Job status is not RUNNING or RUNNABLE. Exiting loop.')
            shouldExitLoop = true
        }
        sleep time: 60, unit: 'SECONDS'
    }
}

def cleanAndInstallDriverOnTestNode() {
    env.nodeIp = commonLib.getNodeIP()
    deleteDir()

    // 获取nodelabel 并保存 dutNodeLabel
    env.originalLabels = commonLib.getNodeLabels(env.NODE_NAME)
    recoverEnv()
    installDriver()
    // 修改nodelabel为atf_only_now
    commonLib.setNodeLabels(env.NODE_NAME, 'atf_only_now')
}

def uninstallDriverOnTestNode() {
    commonLib.recoverEnv()
    // 恢复nodelabel 原始nodelabel
    commonLib.setNodeLabels(env.NODE_NAME, env.originalLabels)
}

def genAtfTestReport() {
    def response = new atf().getTestReport(env.jobId)
    def csvFilePath = "${env.workspace}/"
    def csvFileName = "ATF-report-${env.jobId}-${env.jobTitle}.csv"
    def content = response.split('\n').join('\n')
    // 使用 shell 一次性写入文件
    writeFile(file: "${csvFilePath}/${csvFileName}", text: content)
    log.debug("数据已成功写入 ${csvFilePath}/${csvFileName}")
    artifact.uploadTestReport("${env.WORKSPACE}/${csvFileName}", "sh-moss/sw-build/tmp/${JOB_NAME}/${BUILD_NUMBER}")
    log.debug("成功上传 sh-moss/sw-build/tmp/${JOB_NAME}/${BUILD_NUMBER}/${csvFileName}")
}

runner.start(env.runChoice) {
    runPipeline([
        'setup on testnode': [closure: { cleanAndInstallDriverOnTestNode() }],
        'run atf job': [closure: { runAtfJob(env.nodeIp, env.atfPlanId, env.ctsApp, env.cardId.toInteger()) }, maxWaitTime: [time: env.timeout.toInteger(), unit: 'HOURS']],
        'get log': [closure: { genAtfTestReport() }]
    ], [post: { uninstallDriverOnTestNode() }])
}
