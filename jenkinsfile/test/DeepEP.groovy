@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common
import org.swqa.tools.SshTool

gitLib = new git()
commonLib = new common()

/*
 * testEnvVar (Multiline String) default 'usual export', split by ';'
 * linuxDdkPackageUrl (String) - default ''
 * mtshmemPackageUrl (String) - default ''
 * mtmlPackageUrl (String) - default ''
 * deepEpPackageUrl (String) - default ''
 * relyScriptsUrl (String) - default ''
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - deepeTest
*/

env.repo = 'DeepEP'
containerName = 'deepep_smoke_test'
mtshmemPackageName = env.mtshmemPackageUrl.split('/').last()
mtmlPackageName = env.mtmlPackageUrl.split('/').last()
deepEpPackageName = env.deepEpPackageUrl.split('/').last()
linuxDdkPackageName = env.linuxDdkPackageUrl.split('/').last()
relyScriptsName = env.relyScriptsUrl.split('/').last()
peermemPackageName = env.peermemPackageUrl.split('/').last()
testEnvVar = env.testEnvVar ? commonLib.parseExportVars(env.testEnvVar).join(';') : ''
remoteWorkDir = "${env.remoteWorkDir}/${BUILD_NUMBER}/"
remoteDockerDir = "/home/<USER>/${BUILD_NUMBER}"
ipConfig = readJSON text: env.ipConfig
sshService = new SshTool(this, [user: ipConfig.service.user, ip: ipConfig.service.ip, port: ipConfig.service.port.toInteger(), sshOpts: env.sshOpts])
sshClients = []
ipConfig.client.each { clientInfo ->
    def sshClient = new SshTool(this, [user: clientInfo.user, ip: clientInfo.ip, port: clientInfo.port.toInteger(), sshOpts: env.sshOpts])
    sshClients << sshClient
}

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    sh 'tar -czf DeepEP.tar.gz DeepEP'
}

def setupDockerEnv(boolean initDockerEnv, String dockerImage, SshTool remoteHost) {
    def volumes = [
        '/data/swqa/swqa_shared_dir/DeepEp_test:/home/<USER>',
        '/usr/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/',
        '/usr/bin/:/usr/bin/'
    ]
    def volumeArgs = volumes.collect { "-v ${it}" }.join(' ')
    def containerExists = remoteHost.execWithOutput("docker ps -a --format '{{.Names}}' | grep -w ${containerName} || true")

    if (initDockerEnv) {
        echo "Performing Docker setup operation, image: ${dockerImage}"
        remoteHost.exec("""
            set -e
            docker pull ${dockerImage} || echo "Warning: docker pull failed, possibly using cached image"
            docker rm -f ${containerName} 2>/dev/null || true
            docker run -d --privileged --net host --name ${containerName} ${volumeArgs} ${dockerImage} sleep infinity

            echo "Configuring SSH inside container..."
            docker exec ${containerName} bash -c '
                set -e
                echo -e "ClientAliveInterval 60\\nClientAliveCountMax 5\\nPermitRootLogin yes" >> /etc/ssh/sshd_config
                service ssh restart
            '
        """)
    } else {
        echo 'Checking container status...'
        if (containerExists) {
            echo 'Restarting existing container...'
            remoteHost.exec("docker restart ${containerName}")
        } else {
            echo 'Container does not exist, creating a new one...'
            remoteHost.exec("docker run -d --privileged --net host --name ${containerName} ${volumeArgs} ${dockerImage} sleep infinity")
        }
    }
}

def transPackage() {
    sshService.exec("sudo mkdir -p ${remoteWorkDir}")
    constants.downloadPackage(env.mtmlPackageUrl)
    constants.downloadPackage(env.mtshmemPackageUrl)
    constants.downloadPackage(env.deepEpPackageUrl)
    constants.downloadPackage(env.linuxDdkPackageUrl)
    constants.downloadPackage(env.relyScriptsUrl)
    constants.downloadPackage(env.peermemPackageUrl)
    //scp to remote work dir
    sshService.scpTo(mtmlPackageName, remoteWorkDir)
    sshService.scpTo(mtshmemPackageName, remoteWorkDir)
    sshService.scpTo(deepEpPackageName, remoteWorkDir)
    sshService.scpTo(linuxDdkPackageName, remoteWorkDir)
    sshService.scpTo(relyScriptsName, remoteWorkDir)
    sshService.scpTo('DeepEP.tar.gz', remoteWorkDir)
    sshService.scpTo(peermemPackageName, remoteWorkDir)
}

initCmdOnNode = """
    cd /data/swqa/swqa_shared_dir/; ./uninstall_linux_ddk.sh
    cd ${remoteWorkDir}
    tar xzf ${mtshmemPackageName}
    tar xzf ${deepEpPackageName}
    tar xzf DeepEP.tar.gz
    chmod +x ${relyScriptsName}; ./${relyScriptsName}
    dpkg -i ${linuxDdkPackageName}
    echo "${env.modprobeOptions}" >> /etc/modprobe.d/mtgpu.conf
    modprobe -rv mtgpu
    modprobe -v mtgpu
    dpkg -i ${peermemPackageName}
    modprobe -v mt_peermem
    sleep 180
    cat /sys/module/mtgpu/parameters/tp_pci_vender_id
    export PATH=/usr/local/musa/bin:\$PATH
    muInfo
"""

initCmdInDocker = """
    docker exec ${containerName} bash -c "
        set -e
        rm -rf /usr/local/mtshmem*
        pip uninstall deep_ep || true
        cd ${remoteDockerDir}
        dpkg -i ${mtmlPackageName}
        cd mtshmem; ./install.sh
        cd ${remoteDockerDir}
        pip install deep*.whl
        export PATH=/usr/local/musa/bin:\$PATH
        export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH
        musaInfo
    "
"""

def setUpOnService() {
    sshService.exec(initCmdOnNode)
    setupDockerEnv(env.initDockerEnv?.toBoolean(), env.dockerImage, sshService)
    sshService.exec(initCmdInDocker)
}

def setUpOnClients() {
    sshClients.each { client ->
        client.exec("mount ${ipConfig.service.ip}:/data/swqa/swqa_shared_dir /data/swqa/swqa_shared_dir")
        client.exec(initCmdOnNode)
        setupDockerEnv(env.initDockerEnv?.toBoolean(), env.dockerImage, client)
        client.exec(initCmdInDocker)
    }
}

def deepEpRemoteTest() {
    sshService.exec("""
        docker exec ${containerName} bash -c "
            set -e
            cd ${remoteDockerDir}/DeepEP/pytest
            export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH
            ${testEnvVar}
            python run_test.py ${env.testParam} > testOutput.txt 2>&1
        "
    """)
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'transPackage': [closure: { transPackage() }],
        'setUpOnService': [closure: { setUpOnService() }],
        'setUpOnClients': [closure: { setUpOnClients() }],
        'deepEpRemoteTest': [closure: { deepEpRemoteTest() }]
    ]
    runPipeline(workflow, [
        post: {
            sshService.exec("""
                docker exec ${containerName} bash -c "
                    cd ${remoteDockerDir}/DeepEP/pytest
                    cat testOutput.txt ||:
                "
            """)
        }
    ])
}
