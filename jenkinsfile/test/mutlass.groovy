@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * muAlgPackageUrl (String) - default 'https://sh-moss.mthreads.com/sw-build/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://sh-moss.mthreads.com/sw-build/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'sh-moss/sw-build/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'sh-moss/sw-build/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'mutlass'
env.mudnnCtsRepo = 'muDNN_cts'
env.computeCILibRepo = 'compute_ci_library'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    env.googletesCommitId = gitLib.fetchCode('googletest', 'v1.13.x', env.googletesCommitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
    if (env.perfTest == 'true') {
        env.mudnnCtscommitId = gitLib.fetchCode(env.mudnnCtsRepo, env.mudnnCtsBranch, env.mudnnCtscommitId)
        env.computeCILibCommitId = gitLib.fetchCode('compute_ci_library', 'master', env.computeCILibCommitId)
    }
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
        constants.downloadAndUnzipPackage(env.muRANDPackageUrl)
        sh 'cd muRAND && chmod -R 777 . && ./install.sh ||:'
    }
}

def deployMutlass() {
    dir(env.repo) {
        sh """
            ${envExport}
            ${constants.genCondaActivate('mtdnn_mtgpu')}
            mkdir build && cd build && cmake .. ${env.makeOpt} -DGOOGLETEST_DIR=${env.WORKSPACE}/googletest && make ${env.compileArgs} -j${env.compileParallel}
        """
    }
}

def runNormalTest() {
    timeout(env.TIMEOUT) {
        dir(env.repo) {
            dir('build') {
                new common().loadScript('mutlass_verify.sh', 'linux')
                sh './mutlass_verify.sh'
            }
        }
    }
}

def runPerfTest() {
    timeout(env.TIMEOUT) {
        dir("${env.mudnnCtsRepo}/mutlass_pytest") {
            sh """
                ${envExport}
                ${constants.genCondaActivate('mtdnn_mtgpu')}
                pytest . -v -m "mutlass_benchmark_tests" --alluredir=mutlass_perf_report ||:
                mkdir -p benchmark
                mv ../log/mutlass_benchmark_cases/gemm_benchmark_cases.csv benchmark
                mv ../log/asm_cases/asm_output.txt benchmark
            """
        }
    }
}

def uploadDataBase() {
    dir("${env.computeCILibRepo}/compute_qa_tool_chain/benchmark/") {
        sh """
            ${envExport}
            ${constants.genCondaActivate('mtdnn_mtgpu')}
            pip install pymysql ||:
            python mutlass_benchmark.py -p ${env.WORKSPACE}/${env.mudnnCtsRepo}/mutlass_pytest/benchmark/gemm_benchmark_cases.csv -t ${env.WORKSPACE}/${env.mudnnCtsRepo}/mutlass_pytest/benchmark/asm_output.txt ||:
        """
    }
}

def checkResult() {
    dir("${env.mudnnCtsRepo}/mutlass_pytest") {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('mutlass_perf_report')
    }
}

def uploadTestResult() {
    dir("${env.mudnnCtsRepo}/mutlass_pytest") {
        sh 'tar -czvf mutlass_perf_allure_result.tar.gz mutlass_perf_report'
        sh 'tar -czvf mutlass_benchmark_data.tar.gz benchmark'
        artifact.uploadTestReport('mutlass_perf_allure_result.tar.gz', env.reportOssPath)
        artifact.uploadTestReport('mutlass_benchmark_data.tar.gz', env.reportOssPath)
    }
}

def runFlow = [
    main: {
        def workflow = [
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'deploy mutlass': [closure: { deployMutlass() }],
            'mutlass normal test': [closure: { runNormalTest() }]
        ]
        if (env.perfTest.toBoolean()) {
            workflow['mutlass perf test'] = [closure: { runPerfTest() }]
            workflow['upload data base'] = [closure: { uploadDataBase() }]
        }
        runPipeline(workflow, [disablePre: true])
    }, pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
]
if (env.perfTest.toBoolean()) {
    runFlow.post = {
        runPipeline([
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
            'upload result': [closure: { uploadTestResult() }]
        ], [disablePre: true])
    }
}
runner.start(env.runChoice, runFlow)
