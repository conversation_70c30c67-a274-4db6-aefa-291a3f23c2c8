@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch m3d_cts branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * linuxDdkBranch (String) - default 'mt-ddk-2.0'
 * linuxDdkcommitId (String) - default ''
 * kmdPackageUrl (String) - default ''
 * umdPackageUrl (String) - default ''
 * libdrmPackageUrl (String) - default ''
 * m3dBranch (String) - default ''
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testMark (String) - default ''
 * coverageCollection (Boolean) - default false
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
*/

env.repo = 'm3d_cts'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
linuxDdkBranch = env.linuxDdkBranch ?: 'mt-ddk-2.0'
linuxDdkcommitId = env.linuxDdkcommitId ?: ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.m3dCtsCommit = gitLib.fetchCode(env.repo, env.m3dCtsBranch, env.m3dCtsCommit)
}

def setUpOnNode() {
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }else {
        installDependency([
            'gr-kmd': [env.kmdPackageUrl, 'GeneralSVMHeapPageSize=0x1000'],
            'gr-umd': [env.umdPackageUrl, false]
        ])
        sh 'update-initramfs -u -k \$(uname -r)'
        commonLib.reboot(env.NODE_NAME)
        sh 'modprobe mtgpu'
        sh 'cat /sys/module/mtgpu/parameters/GeneralSVMHeapPageSize'
    }
    // this needs to be done on host, not container
    kmemleak.clearKmemleak()
}

def setUpinDocker() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        if (env.m3dPackageUrl) {
            ddk.installLinuxDdk(env.m3dPackageUrl)
        }
        if (env.kmdPackageUrl) {
            ddk.installLinuxDdk(env.kmdPackageUrl, true)
        }
        linuxDdkCommitId = gitLib.fetchCode('linux-ddk', linuxDdkBranch, linuxDdkcommitId, [disableSubmodules: true])
        gitLib.updateSubmodule('linux-ddk', null, 'libdrm-mt m3d shared_include')
        dir('linux-ddk') {
            def submoduleList = ['m3d', 'libdrm-mt', 'shared_include']
            for (submodule in submoduleList) {
                if (env.gitlabTargetRepoName == submodule) {
                    sh "rm -rf ${env.gitlabTargetRepoName}"
                    gitLib.fetchCode(env.gitlabTargetRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit)
                }
                def newCommit = commonLib.findMrDependency(submodule, (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
                if (newCommit) {
                    sh "rm -rf ${submodule}"
                    gitLib.fetchCode(submodule, newCommit)
                }
            }
            def ddkBuildCmd = env.ddkBuildCmd ?: './ddk_build.sh -a 0 -l 1 -m 1'
            sh "${ddkBuildCmd}"
        }
        // 保证 /m3d /m3d_cts 文件夹同级
        sh "mv ${env.WORKSPACE}/${env.repo} ${env.WORKSPACE}/linux-ddk"
    }else {
        installDependency([
            'gr-umd': [env.umdPackageUrl, false],
            'libdrm-mt': [env.libdrmBranch, env.libdrmCommitId],
            m3d: [env.m3dBranch, env.m3dCommitId, envExport]
        ])
    }
}

def runCtsTest() {
    timeout(env.TIMEOUT) {
        m3dCtsDir = env.linuxDdkPackageUrl ? "${env.WORKSPACE}/linux-ddk/${env.repo}" : "${env.WORKSPACE}/${env.repo}"
        dir(m3dCtsDir) {
            sh """
                ${envExport}
                # mkdir build
                # cmake -DDDK_2_0=ON -DCMAKE_BUILD_TYPE:STRING=Debug -DCMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE -DCMAKE_CXX_FLAGS_DEBUG:STRING="-g --coverage" -S . -B build -G Ninja
                # cmake --build build --config Debug --target all
                python3 run_test.py ${env.testMark} ||:
            """
        }
    }
}

def checkResult() {
    dir(m3dCtsDir) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
    // this needs to be done on host, not container
    // only kmd pr need this check
    // not every node has the ability to do kmemleak check
    kmemleak.checkMemoryIssues()
}

def uploadTestResult() {
    dir(m3dCtsDir) {
        sh 'tar -czvf m3d_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport("${m3dCtsDir}/m3d_cts_allure_result.tar.gz", env.reportOssPath)
        if (sh(script:'find . -name *.csv', returnStdout:true)) {
            sh 'tar -czvf m3d_cts_test_result.tar.gz `find . -name *.csv`'
            artifact.uploadTestReport("${m3dCtsDir}/m3d_cts_test_result.tar.gz", env.reportOssPath)
        }
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpinDocker() }],
        "${env.testLabel}": [closure: { runCtsTest() }],
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() }]
    ], [
        disablePre: true,
        post: {
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}, pre: {
    runPipeline([
        'setup on node': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
