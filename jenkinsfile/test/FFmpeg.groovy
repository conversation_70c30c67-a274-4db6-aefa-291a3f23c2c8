@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

/*
 * parameters
 * vaapiBranch (String) - scj_dev
 * videosPackageUrl (String) - https://sh-moss.mthreads.com/sw-build/media/depend/videos.tar.gz
 * ffmpegPackageUrl (String)
 * linuxDdkPackageUrl (String)
 * testLabel (String)
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - S50_video_test
*/

def cleanFFmpegEnv() {
    sh '''
        # Uninstall ffmpeg and related packages
        apt purge ffmpeg ffmpeg-mt-build libva2-mt-build -y || {
            echo "Warning: Some packages failed to uninstall, continuing..."
        }
        # Clean up unnecessary dependencies
        apt autoremove --purge -y || {
            echo "Warning: Autoremove failed, continuing..."
        }
        echo "Cleanup completed!"
    '''
}

def initVaapiEnv() {
    gitLib.fetchCode('vaapi-fits', env.vaapiBranch, null, [lfs: true, updateBuildDescription: true])
    credentials.runWithCredential('SSH_GITLAB') {
        gitLib.installGitLfs()
        dir('vaapi-fits') {
            sh '''
                git pull && tar -xvf assets.tbz2
                pip3 install -r requirements.txt
            '''
        }
    }
}

def checkVideoDeps() {
    if (!fileExists('/home/<USER>/workspace/videos/') || ((env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))?.contains('update case')) {
        dir('/home/<USER>/workspace/') {
            sh 'rm -rf videos*'
            constants.downloadAndUnzipPackage(env.videosPackageUrl)
        }
    }
}

def installDriver() {
    cleanFFmpegEnv()
    if (env.linuxDdkPackageUrl) {
        // ddk2.0
        ddk.installLinuxDdkAndSetup()
    } else {
        // legacy ddk
        ddk.install()
    }
    ddk.installFFmpeg(env.ffmpegPackageUrl)
}

def runFFmpegTest() {
    timeout(time: 15, unit: 'MINUTES') {
        initVaapiEnv()
    }
    checkVideoDeps()
    sh """
        export LIBVA_DRIVER_NAME=mtgpu
        export LIBVA_DRIVERS_PATH=/usr/lib/x86_64-linux-gnu/dri
        export LD_LIBRARY_PATH=/usr/local/mt_vaapi/lib:/usr/local/mt_vaapi/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH
        vainfo
        export VIDEO_ASSETS_DIR=/home/<USER>/workspace/videos
        export PATH=/usr/local/mt_vaapi/bin/:\$PATH
        cd vaapi-fits && chmod 777 ./vaapi-fits
        ./linux_ffmpeg_ci.sh
    """
}

runner.start(env.runChoice) {
    def workflow = [
        'install driver': [closure: { installDriver() }, maxWaitTime: [time: 15, unit: 'MINUTES']],
        'ffmpeg test': [closure: { runFFmpegTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ]
    runPipeline(workflow, [
        post: {
            cleanFFmpegEnv()
        }
    ])
}
