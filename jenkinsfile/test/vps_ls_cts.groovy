@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

commonLib = new common()
gitLib = new git()

chips = ['ls']
models = ['amodel']
driver_modes = ['metaOnly']
driver_mode_params_map = ['metaOnly': env.metaOnlyParams]

imagePathhost = '/data/qemu/linux/cts_vps'
imagePathVps = '/root/images'
workPathVps = '/root/test/pkg'
testResultsUrl = "https://sh-moss.mthreads.com/${env.ossTestResultsSavePath}"
sshLoginCmd = 'sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no'
env.testDir = env.testDir ?: 'opengl-cts-*******-gbm-intel-ubuntu'
env.binaryUrl = env.binaryUrl ?: 'https://sh-moss.mthreads.com/sw-release/cts/gl/opengl-cts-*******-gbm-intel-ubuntu.tar.gz'
env.caseListUrl = env.caseListUrl ?: 'https://sh-moss.mthreads.com/sw-release/cts/gl/ogl_passlist.txt'
binaryName = env.binaryUrl.split('/')[-1]
caseList = env.caseListUrl.split('/')[-1]

def downloadImg() {
    // if restart vps exists in following steps, we need to copy vps img to workspace and delete it afterwards
    // vps storage will lost after rebooting vps, when starting vps with option -snapshot
    sh """
        echo ' ******** downloadImg ******** ' ||:
        pwd ||:
        ls -l ||:
        ls -l ${env.WORKSPACE} ||:
        ls -l ${workPathVps} ||:
    """
    if (!fileExists("${imagePathhost}/${env.vpsImg}")) {
        sh """
            mkdir -p ${imagePathhost} && cd ${imagePathhost}
            wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/computeQA/vps/vps-img/${env.vpsImg}
            sleep 5
        """
    }
}

def runVpsTest() {
    def packageName = env.linuxDdkPackageUrl.split('/')[-1]
    sh "wget -q --no-check-certificate ${env.linuxDdkPackageUrl} -O ${packageName}"

    try {
        chips.each { chip ->
            models.each { model ->
                driver_modes.each { driver_mode ->
                    def kmd_insmod_params = driver_mode_params_map[driver_mode]
                    println "chip: ${chip}, model: ${model}, driver_mode: ${driver_mode}, kmd_insmod_param: ${kmd_insmod_params}"

                    docker.image(env.testImage).inside("--privileged -i -u 0:0 --hostname vps-test -v ${env.WORKSPACE}:${workPathVps} -v ${imagePathhost}:${imagePathVps}") {
                        // setup vps
                        oss.install()
                        apt.installPackage('sshpass')
                        if (env.AmodlePackageUrl) {
                            sh "mc cp ${env.AmodlePackageUrl} /root/workspace/soc_model/release_mode/amodel/ls/ -r"
                        }
                        sh """
                            sed -i '36i thread_num: 32' /root/workspace/gfx_config.yaml
                            /root/run_qemu.pl -r ${env.vpsVersion} -g ${imagePathVps}/${env.vpsImg} -extra_args "-net user,hostfwd=tcp::10022-:22 -net nic -m 32G -smp 4,cores=2,threads=2,sockets=1 -virtfs local,path=${workPathVps},mount_tag=host0,security_model=passthrough,id=host0 --enable-kvm -snapshot" -display_none -chip_type ${chip} -mode ${model} -model_mode release -ipc_type direct > ${env.workspace}/${BUILD_NUMBER}_${chip}_${model}_${driver_mode}_vps.log &
                        """

                        timeout(10) {
                            sh """
                                while ! timeout 10 ${sshLoginCmd}; do
                                    sleep 5
                                done
                            """
                        }

                        // insmod kmd
                        sh """
                            ${sshLoginCmd} 'mkdir -p ${workPathVps} && mount -t 9p -o trans=virtio,version=9p2000.L host0 ${workPathVps} ||:'
                            ${sshLoginCmd} 'chmod +x ${workPathVps}/*'
                            ${sshLoginCmd} 'apt-get update && apt install --fix-missing && apt install libegl-dev -y'
                            ${sshLoginCmd} 'dpkg -P musa ||:'
                            ${sshLoginCmd} 'dpkg -i ${workPathVps}/${packageName} ||:'
                        """
                        credentials.runWithCredential('SH_MOSS') {
                            def mossUser = USERNAME
                            def mossPwd = PASSWORD
                            sh """
                                ${sshLoginCmd} 'mc alias set sh-moss ${constants.OSS.MOSS_URL_PREFIX} ${mossUser} ${mossPwd}'
                            """
                        }
                        sh """
                            ${sshLoginCmd} 'modprobe -rv mtgpu && modprobe -v mtgpu ${kmd_insmod_params}'
                            ${sshLoginCmd} 'wget -q --no-check-certificate ${env.binaryUrl}'
                            ${sshLoginCmd} 'tar -xzf ${binaryName}'
                            ${sshLoginCmd} 'cd ${env.testDir} && wget -q --no-check-certificate ${env.caseListUrl}'
                            ${sshLoginCmd} 'cd ${env.testDir} && export ENABLE_M3D_GLES=1 && ./glcts --deqp-egl-display-type=gbm --deqp-gl-config-name=rgba8888d24s8 --deqp-surface-type=pbuffer --deqp-surface-width=256 --deqp-surface-height=256 --deqp-caselist-file=${caseList}'
                            ${sshLoginCmd} 'cd ${env.testDir} && mc cp TestResults.qpa sh-moss/${env.ossTestResultsSavePath}/'
                        """
                    }
                }
            }
        }
    } catch (exc) {
        throw new Exception('test failed!')
    } finally {
        oss.install()
        sh """
            echo ' ******** upload log ******** ' ||:
            pwd && ls -l ||:
            ls -l ${env.WORKSPACE} ||:
            ls -l ${workPathVps} ||:
            cd ${env.WORKSPACE}
            mc cp *_vps.log sh-moss/${env.ossTestResultsSavePath}/
        """
        currentBuild.description += "Test Results PKG: ${testResultsUrl} <br>"
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'download img': [closure: { downloadImg() }],
        'vps test': [closure: { runVpsTest() }, setGitlabStatus: true, statusName: testLabel]
    ])
}
