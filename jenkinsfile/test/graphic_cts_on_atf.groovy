@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.atf

commonLib = new common()

/*
 * parameters
 * TestSuite (choice OpenGL/GLES/Vulkan)
 * DutNodeLabel (String)
 * linuxDdkPackageUrl (String)
 * AgentNode (String)
*/

env.dutNodeName = ''
env.dutNodeIp = ''
env.dutNodeOriginalLabels = ''
env.jobId = ''
env.jobTitle = ''

def recoverEnv() {
    commonLib.recoverEnv()
    commonLib.checkCtsDeps()
}

def installDriver() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdkAndSetup()
    } else {
        ddk.install()
    }
}

def exportDisplay(int retryCount = 3) {
    retry(retryCount) {
        commonLib.startXorg('/usr/lib/xorg/Xorg')
        timeout(time: 5, unit: 'SECONDS') {
            sh 'export DISPLAY=:0.0 && glxinfo -B'
        }
    }
}

def runAtfJob(String dutIp, String suiteType) {
    // PlanId: https://tms-prod.shg1.mthreads.com/180001/plan
    def suitetypeToPlanId = [
        'OpenGL': 390007,
        'GLES': 270013,
        'Vulkan': 330009
    ]
    // binpath
    def suitetypeToBinPath = [
        'OpenGL': '/root/cts_gl',
        'GLES': '/root/cts_gles',
        'Vulkan': '/root/cts_vk'
    ]
    def planId = suitetypeToPlanId.containsKey(suiteType) ? suitetypeToPlanId[suiteType] : null
    def binPath = suitetypeToBinPath.containsKey(suiteType) ? suitetypeToBinPath[suiteType] : null
    def timeTagName = new Date().format('yyyyMMddHHmmss')
    env.jobTitle = "${timeTagName}_${suiteType}"
    def executeData = """
        {
            "title": "${env.jobTitle}",
            "executionId": 180001,
            "config" : {
                "params": {
                    "bin_path": "${binPath}",
                    "cardslot": "0.0",
                    "dut_ip": "${dutIp}",
                    "timeout": "30"
                },
                "image": "sh-harbor.mthreads.com/service/atf-v4",
                "scheduleStrategy": "hw",
                "retryResults": [
                    "RESULT_UNKNOWN",
                    "RESULT_FAIL",
                    "RESULT_NOT_SUPPORTED",
                    "RESULT_TIMEOUT",
                    "RESULT_CRASH"
                ]
            },
            "planId": ${planId}
        }
    """
    def response = new atf().startExecution(executeData)
    env.jobId = response.jobId
    currentBuild.description += "Test Suite: ${suiteType} <br>"
    currentBuild.description += "Job ID: ${env.jobId} <br>"
    currentBuild.description += "Dut Node Name: ${env.dutNodeName} <br>"
    currentBuild.description += "Driver: <a href='${env.linuxDdkPackageUrl}' target='_blank'>${env.linuxDdkPackageUrl}</a> <br>"

    def shouldExitLoop = false
    while (!shouldExitLoop) {
        retry(5) {
            sleep time: 10, unit: 'SECONDS'
            jobResult = new atf().getAtfJobResult(env.jobId)
            if (jobResult.status != 'STATUS_RUNNING' && jobResult.status != 'STATUS_RUNNABLE' && jobResult.status != 'STATUS_NEW') {
                println('Job status is not RUNNING or RUNNABLE. Exiting loop.')
                shouldExitLoop = true
            }
        }
        sleep time: 30, unit: 'SECONDS'
    }
}

def cleanAndInstallDriverOnTestNode() {
    node(env.DutNodeLabel) {
        env.dutNodeName = env.NODE_NAME
        env.dutNodeIp = commonLib.getNodeIP()
        deleteDir()

        // 获取nodelabel 并保存 dutNodeLabel
        env.dutNodeOriginalLabels = commonLib.getNodeLabels(env.dutNodeName)
        recoverEnv()
        installDriver()
        // exportDisplay()
        // 修改nodelabel为atf_only_now
        commonLib.setNodeLabels(env.dutNodeName, 'atf_only_now')
    }
}

def uninstallDriverOnTestNode() {
    node(env.dutNodeName) {
        commonLib.recoverEnv()
        // 恢复nodelabel 原始nodelabel
        commonLib.setNodeLabels(env.dutNodeName, env.dutNodeOriginalLabels)
    }
}

def runAtfJobOnAgent() {
    runAtfJob(env.dutNodeIp, env.TestSuite)
}

def genAtfTestReport() {
    node(env.dutNodeName) {
        def response = new atf().getTestReport(env.jobId)
        def csvFilePath = "${env.workspace}/"
        def csvFileName = "ATF-report-${env.jobId}-${env.jobTitle}.csv"
        def content = response.split('\n').join('\n')
        // 使用 shell 一次性写入文件
        writeFile(file: "${csvFilePath}/${csvFileName}", text: content)
        println("数据已成功写入 ${csvFilePath}/${csvFileName}")
        artifact.uploadTestReport("${env.WORKSPACE}/${csvFileName}", "sh-moss/sw-build/computeQA/tmp/${JOB_NAME}/${BUILD_NUMBER}")
        println("成功上传 sh-moss/sw-build/computeQA/tmp/${JOB_NAME}/${BUILD_NUMBER} ${csvFileName}")
    }
}

runner.start('node') {
    runPipeline([
        'setup on testnode': [closure: { cleanAndInstallDriverOnTestNode() }],
        'run atf job': [closure: { runAtfJobOnAgent() }, maxWaitTime: [time: 168, unit: 'HOURS']],
        'get log': [closure: { genAtfTestReport() }]
    ], [post: { uninstallDriverOnTestNode() }])
}
