@Library('swqa-ci')

import org.swqa.tools.git

env.repo = 'gr-kmd'
// runchoice nodeLabel branch commitId containerImage
def fetchCode() {
    env.commitId = new git().fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
// check if this commit is the latest, and set updateLatest to true/false
}

def build() {
    def BVNC = '*******'
    dir(env.repo) {
        sh """
            ./kmd_build.sh -r ${BVNC} -p mtgpu_linux -w xorg -o hw -b release -g kylin
            ./kmd_build.sh -r ${BVNC} -p mtgpu_linux -w xorg -o hw -b release -g kylin install
        """
    }
}

def runDkmsIntreeTest() {
    dir(env.repo) {
        sh """
            targetPath=\$(find \$(pwd) -type d -name 'target*')
            wget -q --no-check-certificate https://sh-moss.mthreads.com/dependency/ci/kernel-sourcecode/linux-source-5.4.0.tar.bz2.tar.bz2.tar.bz2
            tar -xf linux-source-5.4.0.tar.bz2.tar.bz2.tar.bz2
            cp -ar \${targetPath}/mtgpu/ linux-source-5.4.0/drivers/gpu/drm/
            echo "obj-\\\$(CONFIG_GPU_MOORE_THREADS) += mtgpu/"  >> linux-source-5.4.0/drivers/gpu/drm/Makefile
            wget -q --no-check-certificate https://sh-moss.mthreads.com/dependency/ci/kernel-sourcecode/.config -O linux-source-5.4.0/.config
            echo "source \\"drivers/gpu/drm/mtgpu/Kconfig\\"" >> linux-source-5.4.0/drivers/gpu/drm/Kconfig
            cd linux-source-5.4.0
            make modules -j8
            cd drivers/gpu/drm/mtgpu
            ls -l
            if [ -e mtgpu.ko ]; then
                echo "dkms intree test passed"
            else
                echo "dkms intree test failed"
                exit 1
            fi
        """
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetch code': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'dkms intree test': [closure: { runDkmsIntreeTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]

    runPipeline(workflow)
}
