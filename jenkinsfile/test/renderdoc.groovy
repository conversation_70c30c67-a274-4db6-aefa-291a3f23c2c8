@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

// runChoice nodeLabel kmdPackageUrl umdPackageUrl timeout

def installDriver() {
    if (env.linuxDdkPackageUrl) {
        // ddk2.0
        ddk.installLinuxDdkAndSetup()
    } else {
        // legacy ddk
        ddk.install()
    }
}

def runRenderdocTest() {
    // setup
    commonLib.downloadTar('https://oss.mthreads.com/backup/vulkan/tools/MTRenderDoc_linux.tgz')
    oss.install('mtoss', 'mtoss123')
    def preinstallCmd = sh(script: 'cat build/preinstall', returnStdout: true).split(':')[-1].trim() + ' -y'
    apt.installPackage('python3-pip')
    pip.installPackage('pillow', 'pip3', 'http://mirrors.aliyun.com/pypi/simple')
    retry(3) {
        sh "${preinstallCmd}"
    }
    sh '''
        mkdir -p result
        mc cp -r oss/backup/vulkan/trace .
        mc cp -r oss/backup/vulkan/golden .
        mc cp oss/backup/vulkan/tools/replay_rdc.py .
    '''
    // run test
    timeout(env.timeout.toInteger()) {
        def result = sh(script: 'python3 replay_rdc.py', returnStdout: true).trim()
        print(result)
        if (result.contains('diff with golden')) {
            throw new Exception('test failed!')
        }
    }
}

def runGfxrTest() {
    oss.install('mtoss', 'mtoss123')
    sh '''
        rm -rf result ||:
        mkdir -p result
        mc cp -r oss/backup/vulkan/ddk2_0_ci_gfxr .
        chmod +x ddk2_0_ci_gfxr/gfxrecon-info
        chmod +x ddk2_0_ci_gfxr/gfxrecon-replay
    '''
    // run test
    timeout(env.timeout.toInteger()) {
        def result = sh(script: 'cd ddk2_0_ci_gfxr; python3 replay_gfxr.py', returnStdout: true).trim()
        print(result)
        if (result.contains('diff with golden')) {
            throw new Exception('test failed!')
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'install driver': [closure: { installDriver() }],
        'renderdoc test': [closure: { runRenderdocTest() }, setGitlabStatus: true, statusName: env.testLabel],
        'gfxr test': [closure: { runGfxrTest() }, setGitlabStatus: true, statusName: 'jenkins/gfxr_test']
    ]
    runPipeline(workflow, [
        post: {
            utils.catchErrorContinue {
                sh 'dmesg -T | tee dmesg.log'
                artifact.uploadLog('dmesg.log')
            }
            commonLib.reboot(env.NODE_NAME)
            artifact.uploadLog('/var/log/kern.log')
        }
    ])
}
