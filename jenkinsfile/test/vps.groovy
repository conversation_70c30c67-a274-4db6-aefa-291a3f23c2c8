@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

// runChoice nodeLabel dockerImage kmdPackageUrl umdPackageUrl chipType vpsVersion model mergeRequestId targetBranch
logFile = "${env.build_ID}_vps.log"
testResultsUrl = "https://oss.mthreads.com/${env.ossTestResultsSavePath}"
sshLoginCmd = 'sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no'

def downloadImg() {
    // if restart vps exists in following steps, we need to copy vps img to workspace and delete it afterwards
    // vps storage will lost after rebooting vps, when starting vps with option -snapshot
    def imgPath = '/data/qemu/linux/cts_vps'
    if (!fileExists("${imgPath}/vps-xorg-test-ci.img")) {
        sh """
          mkdir -p ${imgPath}  && cd ${imgPath}
          sudo wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/vps/ci_test/vps-xorg-test-ci.img
          sleep 5
        """
    }
}

def runVpsTest() {
    docker.image(env.dockerImage).inside('--privileged -i -u 0:0 --hostname qemu-dev -v /data/qemu/linux/cts_vps:/root/images') {
        try {
            // setup vps
            def libchiplib = "echo ${env.chipType}"
            if (env.chipType == 'ph1') {
                def yestoday = new Date((new Date().getTime() - 1 * 24 * 60 * 60 * 1000)).format('yyyyMMdd')
                oss.install('mtoss', 'mtoss123')
                libchiplib = """
                  echo ${env.chipType}
                  mc cp oss/mt-amodel-release/ph1_daily/${yestoday}/libchiplib.so /root/workspace/soc_model/release_mode/amodel/ph1/
                  mc cp oss/mt-amodel-release/ph1_daily/${yestoday}/libsystemc.so /root/workspace/soc_model/release_mode/amodel/ph1/
                  mc cp sh-moss/sw-release/vps/misc/ogl_ci/gfx_config.yaml /root/workspace/ -r
                """
            }
            sh """
              cd /root
              ls -al
              apt install -y sshpass || exit 1
              ${libchiplib}
              cd /root/workspace && rm -rf param_configuration.yaml && wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/vps/vps_cts/param_configuration.yaml
              /root/run_qemu.pl -r ${env.vpsVersion} -g /root/images/vps-xorg-test-ci.img -extra_args "-net user,hostfwd=tcp::10022-:22 -net nic -snapshot" -mode ${env.model} -chip_type ${env.chipType} > ${env.workspace}/${logFile} &
            """
            // wait ssh start
            timeout(10) {
                sh '''
                  while ! timeout 10 sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no; do
                    sleep 5
                  done
                '''
            }

            // download pkg
            def kmdBranch = env.gitlabSourceRepoName == 'gr-umd' ? env.gitlabTargetBranch : null
            def umdBranch = env.gitlabSourceRepoName == 'gr-kmd' ? env.gitlabTargetBranch : null
            def kmdPackageUrl = env.kmdPackageUrl ?: constants.genLatestPackageUrl('gr-kmd', kmdBranch, 'x86_64-mtgpu_linux-xorg-release-vps.tar.gz')
            def umdPackageUrl = env.umdPackageUrl ?: constants.genLatestPackageUrl('gr-umd', umdBranch, env.umdPackageName ?: 'x86_64-mtgpu_linux-xorg-release-vps.tar.gz')
            sh """
                ${sshLoginCmd} 'ps -ef | grep Xorg'
                ${sshLoginCmd} 'rm -rf ddk_kmd ddk_umd B${env.build_ID}_logs && mkdir ddk_kmd ddk_umd B${env.build_ID}_logs'
                ${sshLoginCmd} 'cd /root/ddk_kmd && wget -q --no-check-certificate ${kmdPackageUrl} && tar xzf *.tar.gz'
                ${sshLoginCmd} 'cd /root/ddk_umd && wget -q --no-check-certificate ${umdPackageUrl} && tar xzf *.tar.gz'
                ${sshLoginCmd} 'systemctl stop lightdm && systemctl disable lightdm' || true
            """
            // install umd
            sh """
                ${sshLoginCmd} 'cd /root/ddk_umd/x86_64-mtgpu_linux-xorg-release/ && ./install.sh -u .' || true
                ${sshLoginCmd} 'cd /root/ddk_umd/x86_64-mtgpu_linux-xorg-release/ && ./install.sh -s .'
                ${sshLoginCmd} 'ldconfig'
            """
            // insmod kmd
            sh """
                ${sshLoginCmd} 'cd /lib/modules/`uname -r`/kernel/sound/ && sudo insmod soundcore.ko' || true
                ${sshLoginCmd} 'cd /lib/modules/`uname -r`/kernel/sound/core/ && sudo insmod snd.ko && sudo insmod snd-timer.ko &&sudo insmod snd-pcm.ko &&sudo insmod snd-pcm-dmaengine.ko' || true
                ${sshLoginCmd} 'modprobe vfio'
                ${sshLoginCmd} 'modprobe vfio_mdev'
                ${sshLoginCmd} 'modprobe drm_kms_helper'
                ${sshLoginCmd} 'modprobe gpu-sched || (apt update && apt install linux-modules-extra-\$(uname -r) -y && modprobe gpu-sched)'
                ${sshLoginCmd} 'insmod /root/ddk_kmd/*mtgpu_linux-xorg-release/lib/modules/*/extra/mtgpu.ko display=none disable_vpu=1 disable_audio=1 mtgpu_vpu_mode=2 disable_jpu=1'
                ${sshLoginCmd} 'dmesg -T'
            """
            // run test
            sh "${sshLoginCmd} 'musadebug -dds'"
            if (env.gitlabTargetBranch == 'develop') {
                sh """
                    ${sshLoginCmd} '/usr/local/bin/Xorg -verbose'&
                    ${sshLoginCmd} 'sleep 10'
                    ${sshLoginCmd} 'export DISPLAY=:0.0 && cd /root/cts/gles/*******/xorg && ./glcts -n dEQP-GLES2.info.version'
                """
            }
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            // update log
            oss.install('mtoss', 'mtoss123')
            sh """
                ${sshLoginCmd} 'dmesg -T'
                cd ${env.WORKSPACE}
                mc cp ${logFile} sh-moss/${env.ossTestResultsSavePath}/
            """
            currentBuild.description += "Test Results PKG: ${testResultsUrl}/${logFile} <br>"
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
      'download img': [closure: { downloadImg() }],
      'vps test': [closure: { runVpsTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ])
}
