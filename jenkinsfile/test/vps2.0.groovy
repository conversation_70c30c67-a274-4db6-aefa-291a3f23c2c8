@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()

// runChoice nodeLabel dockerImage linuxDdkPackageUrl chipType vpsVersion model tagDate
logFile = "${env.build_ID}_vps.log"
testResultsUrl = "https://oss.mthreads.com/${env.ossTestResultsSavePath}"
sshLoginCmd = 'sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no'

def downloadImg() {
    // if restart vps exists in following steps, we need to copy vps img to workspace and delete it afterwards
    // vps storage will lost after rebooting vps, when starting vps with option -snapshot
    def imgPath = '/data/qemu/linux/cts_vps'
    if (!fileExists("${imgPath}/test_compute_v3_no_cuda.img")) {
        sh """
            mkdir -p ${imgPath} && cd ${imgPath}
            sudo wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/computeQA/vps/vps-img/test_compute_v3_no_cuda.img
            sleep 5
        """
    }
    if (!fileExists("${imgPath}/libcunit1_2.1-3-dfsg-2build1_amd64.deb")) {
        sh """
            mkdir -p ${imgPath} && cd ${imgPath}
            sudo wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/computeQA/vps/ph1_m3d/libcunit1_2.1-3-dfsg-2build1_amd64.deb
        """
    }
}

def runLibdrmUt() {
    def exportStr = [
        'export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/:\${LD_LIBRARY_PATH}',
        'export LD_LIBRARY_PATH=/usr/local/musa/lib/:\${LD_LIBRARY_PATH}',
        'export PATH=/usr/local/musa/bin/:\${PATH}'
    ]
    sh """
        ${sshLoginCmd} 'cd /home/<USER>';')} && ./mtgpu_test -s 7 -t 1'
    """
}

def runVpsTest() {
    docker.image(env.dockerImage).inside("--privileged -i -u 0:0 --hostname qemu-dev -v ${env.WORKSPACE}:/root/test/pkg -v /data/qemu/linux/cts_vps:/root/images") {
        try {
            // setup vps
            oss.install('mtoss', 'mtoss123')
            new common().runRelyNetwork(3, 10) {
                sh 'apt install -y sshpass'
            }
            def tagDate = env.tagDate
            def libchiplib = """
                echo ${env.chipType}
                cd /root/workspace/
                rm gfx_config.yaml param_configuration.yaml ||:
                mc cp sh-moss/sw-build/computeQA/vps/ph1_m3d/ph1_ddk2_yaml.tar .
                tar xf ph1_ddk2_yaml.tar
            """
            // temp solution, move this to qemu image in the future
            sh 'cp /root/images/libcunit1_2.1-3-dfsg-2build1_amd64.deb /root/test/pkg/'
            sh """
                ${libchiplib}
                cd /root && ls -al
                echo -e "\\033[32mCmodel release date: ${tagDate}\\033[0m"
                mc cp -r sh-moss/sw-build/computeQA/vps/backup/ph1_cmodel/${tagDate} .

                cd ${tagDate}
                tar -zxf arch_DOG_release_binary.tar.gz >/dev/null
                cp -rf ci_env/transif_binary/libs*.so /root/workspace/soc_model/release_mode/cmodel/ph1/
                tar -xf chiplib_for_vps_release.tar
                cp -rf libchiplib.so /root/workspace/soc_model/release_mode/cmodel/ph1/

                /root/run_qemu.pl -r ${env.vpsVersion} -g /root/images/test_compute_v3_no_cuda.img -extra_args "-net user,hostfwd=tcp::10022-:22 -net nic -snapshot -m 32G -smp 4 -virtfs local,path=/root/test/pkg,mount_tag=host0,security_model=passthrough,id=host0 -display none " -chip_type ${env.chipType} -mode ${env.model} -ipc_type shm > ${env.workspace}/${logFile} &
            """
            // wait ssh start
            timeout(10) {
                sh '''
                    while ! timeout 10 sshpass -p 123456 ssh 127.0.0.1 -p 10022 -o StrictHostKeyChecking=no; do
                        sleep 5
                    done
                '''
            }

            // insmod kmd
            def branch = env.gitlabTargetBranch ?: 'mt-ddk-2.0'
            def linuxDdkPackageUrl = env.linuxDdkPackageUrl ?: constants.genLatestPackageUrl('linux-ddk', branch, 'ddkVps2.0.deb')
            def packageName = linuxDdkPackageUrl.split('/')[-1]
            sh """
                ${sshLoginCmd} 'ps -ef | grep Xorg'
                ${sshLoginCmd} 'rm -rf B${env.build_ID}_logs && mkdir ddk_vps B${env.build_ID}_logs'
                # ${sshLoginCmd} 'apt install libcunit1 -y'
                ${sshLoginCmd} 'mkdir -p /root/test/pkg && mount -t 9p -o trans=virtio,version=9p2000.L host0 /root/test/pkg'
                ${sshLoginCmd} 'cd /root/test/pkg && dpkg -i libcunit1*.deb'
                ${sshLoginCmd} 'rm -rf /lib/firmware/mthreads/*' ||:
                ${sshLoginCmd} 'cd /root/ddk_vps && wget -q --no-check-certificate ${linuxDdkPackageUrl} && dpkg -i ${packageName}'
                ${sshLoginCmd} 'dmesg -T'
            """
            // run test
            runLibdrmUt()
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            // update log
            oss.install('mtoss', 'mtoss123')
            sh """
                ${sshLoginCmd} 'dmesg -T'
                cd ${env.WORKSPACE}
                mc cp ${logFile} sh-moss/${env.ossTestResultsSavePath}/
            """
            currentBuild.description += "Test Results PKG: ${testResultsUrl}/${logFile} <br>"
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'download img': [closure: { downloadImg() }],
        'vps test': [closure: { runVpsTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ])
}
