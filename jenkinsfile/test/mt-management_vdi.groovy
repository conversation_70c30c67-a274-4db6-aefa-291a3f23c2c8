@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

// runChoice nodeLabel branch commitId dockerImage vdiBranch runCov coverageOssPath testLabel
def perpareEnv() {
    commonLib.reboot(env.NODE_NAME)
    commonLib.recoverEnv()
    sh'rmmod mtgpu 2>log.txt || true'
    if (fileExists('log.txt')) {
        try {
            sh(script: "grep 'Module mtgpu is not currently loaded' log.txt", returnStdout: true).trim()
        } catch (exc) {
            commonLib.reboot(5)
        }
    }
}

def installDriver() {
    def latestVdiPkg = utils.runCommandWithStdout("curl --insecure https://sh-moss.mthreads.com/sw-build/VDI/${env.vdiBranch}/latest.txt")
    def pkgParts = latestVdiPkg.split('/')[-1]
    def kmdPkgName = "${pkgParts}_kmd_Ubuntu_release.tar.gz"
    def kmdVdiUrl = "${latestVdiPkg}/${kmdPkgName}"
    sh """
        wget -q --no-check-certificate ${kmdVdiUrl}
        tar xvzf ${kmdPkgName}
        modprobe drm_kms_helper
        modprobe vfio
        modprobe vfio_mdev
        modprobe gpu-sched
        rm -rf /usr/lib/firmware/mthreads/
        mkdir -p /usr/lib/firmware/mthreads/
        cp -rf ./firmware/* /usr/lib/firmware/mthreads/
        insmod mtgpu.ko mtgpu_driver_mode=0 display=dummy mtgpu_load_windows_firmware=1
        dmesg | grep 'MooreThreads GPU drm driver loaded successfully' || exit 1
    """
}

def googleTest() {
    gitLib.fetchCode('mt-management', env.branch, env.commitId)
    dir('mt-management') {
        docker.image(env.dockerImage).inside('-i -u 0:0 --privileged=true -v /dev:/dev -v /sys:/sys') {
            def version = utils.runCommandWithStdout("egrep -A1 'mtml_version' config.ini | grep 'version = ' | awk -F '=' '{print \$2}'")
            sh """
                sed -i 's/^\\#[ ]*aux/aux/' ./test/CMakeLists.txt
                ./build_ci.sh DEBUG x86_64 ${version} YES
            """
        }
        sh '''
            apt-get install mdevctl -y
            set PATH=/usr/sbin:\$PATH
            ./build/test/sdk_googletest -p=1
        '''
    }
}

def runCoverage() {
    def cvgReport = 'vdi_report.info'
    docker.image(env.dockerImage).inside('-i -u 0:0 --privileged=true -v /dev:/dev -v /sys:/sys') {
        dir('mt-management/build') {
            commonLib.runCoverage(cvgReport)
            artifact.upload(cvgReport, env.coverageOssPath)
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'prepare env': [closure: { perpareEnv() }],
        'install driver': [closure: { installDriver() }],
        'test mtml': [closure: { googleTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ]
    if (env.runCov == 'true') {
        workflow['run coverage'] = [closure: { runCoverage() }]
    }
    runPipeline(workflow)
}
