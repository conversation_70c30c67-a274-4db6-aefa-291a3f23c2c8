@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

// nodeLabel runChoice branch commitId umdPackageUrl triggerInfo compileArgs compileParallel pkgName

gitLib = new git()
commonLib = new common()

env.repo = 'musa_asm_test'
pkgName = env.pkgName
env.handwritingRepo = 'musaasm_handwriting_test'
env.musaAsmRepo = 'musa_asm'

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muRANDPackageUrl = env.muRANDPackageUrl?.trim() ? env.muRANDPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.musaAsmCommitId = env.gitlabTargetRepoName == 'musa_asm' ? env.gitlabMergeRequestLastCommit : ''
    env.musaAsmBranch = env.gitlabTargetRepoName == 'musa_asm' ? env.gitlabTargetBranch : env.musaAsmBranch
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    env.hardWrittingCommitId = gitLib.fetchCode(env.handwritingRepo, env.hardWrittingBranch, env.hardWrittingCommitId)
    env.musaAsmCommitId = gitLib.fetchCode(env.musaAsmRepo, env.musaAsmBranch, env.musaAsmCommitId)
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    sh '''
        apt update ||:
        apt install libcunit1 -y ||:
    '''
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }else {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    if (env.anacondaPackageUrl) {
        constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    }
    constants.downloadAndUnzipPackage(env.musaAsmPackageUrl)
    sh 'cd musa_asm && ./install.sh'
    dir(musaAsmRepo) {
        constants.downloadAndUnzipPackage(env.musaAsmLitPackageUrl)
    }
}

def runCtsTest() {
    timeout(env.TIMEOUT) {
        dir(env.repo) {
            sh """
                ${constants.genCondaActivate('mtdnn_mtgpu')}
                export TEMP_LOG=${env.WORKSPACE}/${env.musaAsmRepo}/musa_asm_test.log
                export PATH=/usr/local/musa/bin:${env.WORKSPACE}/${env.repo}/utils/lit/:\$PATH
                lit.py testsuite -o \$TEMP_LOG ||:
                cd pytest
                export MUSA_ASM_PATH=${env.WORKSPACE}/${env.musaAsmRepo}
                pytest test_musa_asm_llt.py --alluredir=${env.WORKSPACE}/allure_result
                pytest musa_asm_test.py --alluredir=${env.WORKSPACE}/allure_result
            """
        }
        // test
        dir(env.handwritingRepo) {
            sh """
                ${constants.genCondaActivate('mtdnn_mtgpu')}
                cd ${env.WORKSPACE}/${env.handwritingRepo}/
                export TEMP_LOG=${env.WORKSPACE}/${env.handwritingRepo}/musaasm_handwriting_test.log
                export PATH=/usr/local/musa/bin:${env.WORKSPACE}/${env.handwritingRepo}/utils/lit/:\$PATH
                lit.py mp31_kernels -o \$TEMP_LOG ||:
                cd ${env.WORKSPACE}/${env.handwritingRepo}/pytest
                pytest musaasm_handwriting_test.py --alluredir=${env.WORKSPACE}/allure_result
            """
        }
    }
}

def checkResult() {
    //python run_test.py generate test-report dir include allure report
    commonLib.allure('allure_result')
}

def uploadTestResult() {
    sh 'tar -czvf musa_asm.tar.gz allure_result'
    artifact.uploadTestReport('musa_asm.tar.gz', env.reportOssPath)
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpinDocker() }],
        'musa asm test': [closure: { runCtsTest() }],
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() }]
    ], [disablePre: true])
}, pre: {
    runPipeline([
        'setup pre': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
