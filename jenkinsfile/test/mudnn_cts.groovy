@Library('swqa-ci')

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * cmd (String) - ./build.sh -d ./install -j 16 -c mp21
 * exports (Multiline String) default 'usual export', split by '\n'
 * dependcy (Multiline String) - default 'musolver_data', split by ';
 * musaToolkitsPackageUrl (String) - default 'musatoolkit url'
 * muDNNPackageUrl (String) - ''
 * condaPackageUrl (String) - 'https://sh-moss.mthreads.com/sw-build/computeQA/ai-rely-pkg/miniforge/miniforge_mtdnn.tar.gz'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()
env.repo = 'muDNN_cts'
env.muDNN_repo = 'muDNN'
// env.ref, env.head_commit are from webhook, triggered by code merge
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
generateTestUrl = env.generateTestUrl ? env.generateTestUrl.split(';') : []

build_path = '/home/<USER>/agent/workspace/build.muDNN'
test_path = ''
deviceArch = ''
coverage_report_name = ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muDNNPackageUrl = env.muDNNPackageUrl?.trim() ? env.muDNNPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }

    env.mudnn_ctsCommitId = gitLib.fetchCode(env.repo, env.mudnn_ctsBranch, env.mudnn_ctsCommitId)
    if (env.gitlabTargetRepoName == 'muDNN') {
        env.mudnnBranch = env.gitlabSourceBranch
        env.mudnnCommitId = env.gitlabMergeRequestLastCommit
    }
    gitLib.fetchCode(env.muDNN_repo, env.muDNNBranch, env.mudnnCommitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])

    if (env.gCover == 'true') {
        dir(build_path) {
            gitLib.fetchCode(env.muDNN_repo, env.muDNNBranch, env.mudnnCommitId)
        }
    }
}

def envSet() {
    sh 'apt install ninja-build ||:'
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    if (env.runChoice == 'node' && env.containerImage == '') {
        try {
            commonLib.restartLinuxNode()
        } finally {
            sleep time: 3, unit: 'MINUTES'
        }
    }

    if (env.musaSdkPackageUrl) {
        sh """
            dpkg -P musa-sdk ||:
            wget -q --no-check-certificate ${env.musaSdkPackageUrl}
            dpkg -i ${env.musaSdkPackageUrl.split('/')[-1]}
        """
    }
    else {
        if (env.musaToolkitsPackageUrl) {
            musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
        }
        else {
            if (env.musaRuntimePackageUrl) {
                musa.installMusaRuntime(env.musaRuntimePackageUrl)
            }
            def dependencies = ['mtcc': env.mtccPackageUrl]
            installDependency(dependencies)
            dir('musa_toolkit') {
                sh 'cp -r cmake /usr/local/musa/'
            }
        }
    }
    constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')
    dir(env.muDNN_repo) {
        sh """
            ${constants.genCondaActivate(env.condaEnv)}

            # 配置远程认证
            dvc remote modify --local bmm_golden access_key_id "sw-guest-mt-sw" 2>/dev/null || true
            dvc remote modify --local bmm_golden secret_access_key "sw-guest123" 2>/dev/null || true
            dvc remote modify --local mudnn_golden access_key_id "mtoss" 2>/dev/null || true
            dvc remote modify --local mudnn_golden secret_access_key "mtoss123" 2>/dev/null || true

            # 修改拉取顺序交替拉取
            echo "尝试从主远程 bmm_golden 拉取..."
            if dvc pull -r bmm_golden; then
                echo "✓ 主远程拉取成功"
            else
                echo "主远程失败，尝试备用远程 mudnn_golden..."
                if dvc pull -r mudnn_golden; then
                    echo "✓ 备用远程拉取成功"
                else
                    echo "备用远程失败，重试主远程 bmm_golden..."
                    if dvc pull -r bmm_golden; then
                        echo "✓ 主远程重试成功"
                    else
                        echo "主远程重试失败，重试备用远程 mudnn_golden..."
                        if dvc pull -r mudnn_golden; then
                            echo "✓ 备用远程重试成功"
                        else
                            echo "❌ 所有远程拉取均失败"
                            exit 1
                        fi
                    fi
                fi
            fi
        """
    }
}

def installMudnn() {
    if (!env.musaSdkPackageUrl) {
        if (env.muDNNPackageUrl) {
            env.muDNNPackageUrl = constants.ossPathToUrl(env.muDNNPackageUrl)
            if (env.muDNNPackageUrl.contains('QY1')) {
                deviceArch = 'mp_21'
            } else if (env.muDNNPackageUrl.contains('QY2')) {
                deviceArch = 'mp_22'
            } else if (env.muDNNPackageUrl.contains('PH1')) {
                deviceArch = 'mp_31'
            }
        }
        else if (env.musaToolkitsPackageUrl) {
            def baseDir = "${env.musaToolkitsPackageUrl}".substring(0, "${env.musaToolkitsPackageUrl}".lastIndexOf('/'))
            baseDir = constants.urlToOSSPath(baseDir)
            def lspciOutput = sh(returnStdout: true, script: 'lspci -n | grep -oP "1ed5:\\K\\d{2}" | head -n 1 || true').trim()
            def deviceType = ''
            switch (lspciOutput) {
                case '02':
                    deviceType = 'QY1'
                    deviceArch = 'mp_21'
                    break
                case '03':
                    deviceType = 'QY2'
                    deviceArch = 'mp_22'
                    break
                case '04':
                    deviceType = 'PH1'
                    deviceArch = 'mp_31'
                    break
                default:
                    error "Unsupported device type: lspci output=${lspciOutput}"
            }
            def matchStr = "mc ls ${baseDir} | grep -i mudnn | grep -E '[._]${deviceType}\\.'"
            def mudnn_pkg = sh(returnStdout: true, script: "${matchStr} | awk -F ' ' '{print \$NF}'").trim()
            env.muDNNPackageUrl = constants.ossPathToUrl("${baseDir}/${mudnn_pkg}")
        }
        test_path = "${build_path}/muDNN/build/${deviceArch}/mudnn/lib"

        if (env.gCover == 'true') {
            dir("${build_path}/${env.muDNN_repo}") {
                sh """
                    wget -q --no-check-certificate ${env.muDNNPackageUrl}
                    tar -xf ${env.muDNNPackageUrl.split('/')[-1]}
                    cd build/${deviceArch}/mudnn
                    ./install_mudnn.sh -i
                """
                envExport += '&& export MUDNN_LIB=/usr/local/musa/lib/ && export MUDNN_HEADERS=/usr/local/musa/include/ && export GCOV_TEST=ON'
            }
        }
        else {
            sh """
                wget -q --no-check-certificate ${env.muDNNPackageUrl}
                tar -xf ${env.muDNNPackageUrl.split('/')[-1]}
                cd mudnn
                ./install_mudnn.sh -i
            """
            envExport += '&& export MUDNN_LIB=/usr/local/musa/lib/ && export MUDNN_HEADERS=/usr/local/musa/include/'
        }
    }
}

def runTest() {
    dir(env.muDNN_repo) {
        sh"""
            ${constants.genCondaActivate(env.condaEnv)}
            cd tests
            ./build_all.sh -o
        """
        envExport += "&& export PYTHONPATH=${env.WORKSPACE}/muDNN/tests/python:${env.WORKSPACE}/muDNN/tests/build"
        print(envExport)
    }
    dir(env.repo) {
        env.testParam = env.test_type == 'daily' ? '' : '-x'
        if (env.isDump == 'true') {
            envExport += '&& export TEST_MODE=DUMP_VERIFY'
            constants.downloadAndUnzipPackage(env.dumpPackageUrl, '.')
        }
        try {
            sh """
                ${envExport}
                export TEST_TYPE=${env.test_type}
                export TIMEOUT_FACTOR=${env.timeoutFactor}
                ${constants.genCondaActivate(env.condaEnv)}
                python run_test.py -m ${env.test_mark} ${env.testParam}
                tar -czf mudnn_cts_allure_result.tar.gz test-report/ ||:
            """
            if (env.gCover == 'true') {
                genCovReport()
            }
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            sh '''
                dmesg -T || :
            '''
        }
    }
}

def genCovReport() {
    llvm_cov_item = mathxCoverage.llvm_cov_plan.get(env.muDNN_repo)
    llvm_cov_item.llvm_cov_directory = test_path
    coverage_report_name = mathxCoverage.mudnnGenerateCoverage(env.muDNN_repo, llvm_cov_item)

    if (coverage_report_name) {
        catchError(stageResult: 'FAILURE') {
            commonLib.loadScript('mathx_coverage_writetoInfluxDB.py', 'coverage', false)
            sh """
                ${constants.genCondaActivate(env.condaEnv)}
                ls -l
                python mathx_coverage_writetoInfluxDB.py --indexfile ${env.WORKSPACE}/${env.repo}/${coverage_report_name}/index.html --product ${env.muDNN_repo} --measurement muDNN || :
            """
        }
        commonLib.publishHTML(coverage_report_name, coverage_report_name)
    }
    else {
        println 'generate coverage fail!'
        currentBuild.description += '<b>generate coverage fail!</b><br>'
    }
}

def checkResult() {
    dir("${env.WORKSPACE}/${env.repo}") {
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    oss.install()
    generateTestUrl.each {
        oss.cp("${env.WORKSPACE}/${env.repo}/mudnn_cts_allure_result.tar.gz", it)
        currentBuild.description += "<br>generate_pkg:${it}/mudnn_cts_allure_result.tar.gz".replaceAll('//', '/')
    }
}

def uploadCoverage() {
    sh"""
        pwd
        ls -l
        tar -czvf ${coverage_report_name}.tar.gz ${coverage_report_name}
        ls -l
    """
    artifact.uploadTestReport("${coverage_report_name}.tar.gz", env.reportOssPath)
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'env-setup': [closure: { envSet() }],
        'install-mudnn': [closure: { installMudnn() }],
        'runTest': [closure: { runTest() }],
    ], [disablePost: true])
}, post: {
    def postPipelineSteps = [
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() }]
    ]
    // if (env.gCover == 'true') {
    //     postPipelineSteps['upload coverage'] = [closure: { uploadCoverage() }]
    // }

    runPipeline(postPipelineSteps, [disablePre: true])
}, pre: {
    if (env.runChoice == 'node' && env.installDDKonHost == 'true') {
        stage('install ddk') {
            // restart machine
            commonLib.reboot(env.NODE_NAME)
            // env recovery
            commonLib.recoverEnv()
            ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
        }
    }
}])
