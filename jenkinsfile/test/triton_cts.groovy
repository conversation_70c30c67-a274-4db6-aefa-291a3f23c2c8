@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch triton_cts branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * musaToolkitsPackageUrl (String) - default ''
 * mcclPackageUrl (String) - default ''
 * muDNNPackageUrl (String) - default ''
 * torchPackageUrl (String) - default ''
 * torchMusaPackageUrl (String) - default ''
 * tritonMusaPackageUrl (String) - default ''
 * muThrustPackageUrl (String) - default ''
 * muAlgPackageUrl (String) - default ''
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * reportOssPath (String) - default 'sh-moss/sw-build/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - ''
*/

env.repo = 'triton_cts'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://***********:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muRANDPackageUrl = env.muRANDPackageUrl?.trim() ? env.muRANDPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    if (env.flaggemsBranch) {
        dir(env.repo) {
            sh 'rm flaggems -rf ||:'
            env.flaggemsCommitId = gitLib.fetchCode('flaggems', env.flaggemsBranch, env.flaggemsCommitId, ['gitlabGroup':'sw-compiler'])
        }
    }
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    if (env.musaSdkPackageUrl) {
        sh """
            dpkg -P musa-sdk ||:
            wget -q --no-check-certificate ${env.musaSdkPackageUrl}
            dpkg -i ${env.musaSdkPackageUrl.split('/')[-1]}
        """
    } else {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
        constants.downloadAndUnzipPackage(env.muDNNPackageUrl)
        sh 'cd mudnn && ./install_mudnn.sh -i'
    }

    constants.downloadAndUnzipPackage(env.mcclPackageUrl)
    sh 'cd mccl && ./install.sh'

    if (env.muAlgPackageUrl) {
        musa.installmuAlg(env.muAlgPackageUrl)
    }
    else {
        gitLib.fetchCode('muAlg', 'develop')
        dir('muAlg') {
            sh './mt_build.sh -i'
        }
    }
    if (env.muThrustPackageUrl) {
        musa.installmuThrust(env.muThrustPackageUrl)
    }
    else {
        gitLib.fetchCode('muThrust', 'develop')
        dir('muThrust') {
            sh './mt_build.sh -i'
        }
    }

    constants.downloadPackage(env.torchPackageUrl)
    constants.downloadPackage(env.torchMusaPackageUrl)

    constants.downloadAndUnzipPackage(env.tritonMusaPackageUrl)

    if (env.runChoice == 'node' && env.containerImage == '') { //running on M1000
        sh 'pip install ./torch*.whl'
        sh 'cd bdist_wheel && pip install ./triton*.whl'
    }
    else {
        sh '''
            . /root/miniforge3/etc/profile.d/conda.sh
            conda activate py310
            pip install ./torch*.whl
        '''
        sh '''
            . /root/miniforge3/etc/profile.d/conda.sh
            conda activate py310
            cd bdist_wheel && pip install ./triton*.whl
        '''
        if (env.flaggemsBranch) {
            dir("${env.repo}/flaggems") {
                sh '''
                    . /root/miniforge3/etc/profile.d/conda.sh
                    conda activate py310
                    pip install -e . --no-deps
                '''
            }
        }
        constants.downloadAndUnzipPackage('http://***********:56548/sw-build/computeQA/ai-rely-pkg/triton_musa/triton_rely_so.tar.gz')
        sh 'cp -a triton_rely/* /usr/lib/x86_64-linux-gnu/'
    }
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        // test
        dir(env.repo) {
            if (env.runChoice == 'node' && env.containerImage == '') { //running on M1000
                sh """
                    ${envExport}
                    python run_test.py ||:
                """
            }
            else {
                sh """
                    ${envExport}
                    . /root/miniforge3/etc/profile.d/conda.sh
                    conda activate py310
                    pip install -r requirements.txt
                    python run_test.py ||:
                """
            }
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh 'tar -czvf triton_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('triton_cts_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpinDocker() }],
        'triton test': [closure: { runCtsTest() }],
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() }]
    ], [disablePre: true])
}, pre: {
    runPipeline([
        'setup pre': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
