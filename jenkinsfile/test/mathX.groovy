@Library('swqa-ci')

import org.swqa.tools.git

gMublasCondaUrl = 'https://sh-moss.mthreads.com/sw-build/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
gMuppCondaUrl = 'https://sh-moss.mthreads.com/sw-build/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
gMtccCondaUrl = 'https://sh-moss.mthreads.com/sw-build/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'

gitLib = new git()

def getConda(String condaFilePath, String condaUrl) {
    def filePath = '/home/<USER>/miniforge/envs/' + condaFilePath
    if (!fileExists(filePath)) {
        def condaTarName = condaUrl.split('/')[-1]
        sh """
            mkdir -P /home/<USER>
            wget --no-check-certificate condaUrl
            tar -xf ${condaTarName} -C /home/<USER>/
        """
    }
}

def runMublasCts() {
    stage('mublas_cts') {
        getConda('mathx', gMublasCondaUrl)
        gitLib.fetchCode('muBLAS_cts', 'master')
        sh """
            cd ${WORKSPACE}/muBLAS_cts
            . /opt/intel/oneapi/setvars.sh intel64
            export PATH=/usr/local/musa/bin:\${PATH}
            export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:\$LD_LIBRARY_PATH
            rm build -rf && mkdir build && cd build && cmake .. && make -j64  && cd ..
            ${constants.genCondaActivate('mathx')}
            export TEST_TYPE=daily
            python run_test.py -k create
            rm -rf ${env.WORKSPACE}/allure_mublas_cts
            mv test-report ${env.WORKSPACE}/allure_mublas_cts
        """
    }
}

def runMufftCts() {
    stage('mufft_cts') {
        getConda('mathx', gMublasCondaUrl)
        sh """
            cd ${WORKSPACE}/mufft_cts
            git clean -dfx
            export PATH=/usr/local/musa/bin:\${PATH}
            export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:\$LD_LIBRARY_PATH
            mkdir build && cd build && cmake .. && make -j64  && cd ..
            ${constants.genCondaActivate('mathx')}
            export TEST_TYPE=daily
            python run_test.py -k muFFT_create_test
            rm -rf ${env.WORKSPACE}/allure_mufft_cts
            mv test-report ${env.WORKSPACE}/allure_mufft_cts
        """
    }
}

def runMuRandCts() {
    stage('murand_cts') {
        getConda('mathx', gMublasCondaUrl)
        sh """
            cd ${WORKSPACE}/muRAND_cts
            git clean -dfx
            export PATH=/usr/local/musa/bin:\${PATH}
            export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:\$LD_LIBRARY_PATH
            rm build -rf && mkdir build && cd build && cmake .. -DMUSA_ARCHS=21,22,31 && make -j64  && cd ..
            ${constants.genCondaActivate('mathx')}
            export TEST_TYPE=daily
            python run_test.py -k murandGetVersion_test
            rm -rf ${env.WORKSPACE}/allure_murand_cts
            mv test-report ${env.WORKSPACE}/allure_murand_cts
        """
    }
}

def runMtccTest() {
    stage('mtcc_test') {
        println 'before get conda'
        getConda('mathx', gMtccCondaUrl)
        println 'after get conda'
        sh """
            export MUSA_VISIBLE_DEVICES=1
            cd ${WORKSPACE}/mtcc_test/
            git clean -dfx
            cd ${WORKSPACE}/mtcc_test/pytest
            export PATH=/usr/local/musa/bin:\$PATH
            export LD_LIBRARY_PATH=/usr/local/musa/lib/:\$LD_LIBRARY_PATH
            python3 run_lit.py --device=ph1 --workers=1
            rm -rf ${env.WORKSPACE}/allure_mtcc_test
            pytest mtcc_lit_report.py --alluredir=${env.WORKSPACE}/allure_mtcc_test --clean-alluredir
        """
    }
}

def runMusaCtsMtcc() {
    stage('musa_cts_ptsz') {
        getConda('mathx', gMublasCondaUrl)
        sh """
            export PATH=/usr/local/musa/bin:\$PATH
            export MUSA_EXECUTION_TIMEOUT=7200000
            export TIMEOUT_FACTOR=12
            export MUSA_FENCE_WAIT_TIMEOUT=10000000
            export LD_LIBRARY_PATH=/usr/local/musa/lib/:/usr/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH
            export MTGPU_ARCH=mp_31
            export TEST_TYPE=dailyM3d
            cd /root/jenkins/musa_cts_light_test/musa_cts
            cd ./pytest_ptsz
            pytest -vs -m "not musa_xorg" -k "not skip" --alluredir=${env.WORKSPACE}/allure_musa_cts_ptsz
        """
    }
}

def runTest(String testType) {
    println testType
    switch (testType) {
        case 'mublas_cts':
            runMublasCts()
            break
        case 'murand_cts':
            runMuRandCts()
            break
        case 'mufft_cts':
            runMufttCts()
            break
        case 'mtcc_test':
            runMtccTest()
            break
        case 'musa_cts_ptsz':
            runMusaCtsPtsz()
            break
    }
    // TODO: run allure command
    // allure command goes here
    // TODO: make sure all test generate allure result to ${env.WORKSPACE}/allure_${test}
    stage('allure report') {
        def now = new Date().format('yyyy-MM-dd-HH-mm')
        println now

        sh '''
            wget -q https://sh-moss.mthreads.com/sw-build/computeQA/tools/allure-2.19.0.tar --no-check-certificate
            tar -xf allure-2.19.0.tar
            ./allure-2.19.0/bin/allure generate -c allure_${testType} -o allure-report_${testType}_${now}
            rm -rf allure_${testType}
        '''
        publishHTML([
            allowMissing: false,
            alwaysLinkToLastBuild: true,
            keepAll: true,
            reportDir: "allure-report_${testType}_${now}",
            reportFiles: 'index.html',
            reportName: "allure-report_${testType}_${now}"
        ])
    }
}

def installDDK() {
    // stage('download package') {
    //     deleteDir()
    //     sh """
    //         mc cp ${env.linuxDDK} .
    //         mc cp ${env.musaToolkit} .
    //         ls *tar.gz | xargs -i tar -zxf {}
    //     """
    // }
    // stage('uninstall old ddk') {
    //     sh '''
    //         sudo modrobe -rv mtgpu ||:
    //         sudo rmmod mtgpu ||:
    //         sudo dpkg -P musa ||:
    //         sudo dpkg -P mtgpu ||:
    //         sudo dkms remove mtgpu -v 1.0.0 --all ||:
    //         sudo rm -rf /usr/src/mtgpu-1.0.0/ ||:
    //         sudo rm -rf /usr/local/musa* ||:
    //         sudo update-initramfs -u -k all ||:
    //     '''
    // }
    // stage('install ddk') {
    //     sh """
    //         #cd /root/123/
    //         #./mtc_tool_v4.0.5_linux_amd64 --mc --set 8
    //         #rmmod mtgpu_tiny
    //         #/root/123/mttool gpu dump
    //         #setpci -s 01:00.0 04.w=0x0407
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 0 > /dev/null
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 1 > /dev/null
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 2 > /dev/null
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 3 > /dev/null
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 4 > /dev/null
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 5 > /dev/null
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 6 > /dev/null
    //         #/root/123/mttool gedit CR_L2_AXI_TIMEOUT_CTRL -v 0x2ffffffff --core 7 > /dev/null
    //         cd ${env.WORKSPACE}
    //         #sudo modprobe -a drm_kms_helper drm snd  snd-pcm gpu-sched
    //         sudo dpkg -i *deb
    //         sudo modprobe -rv mtgpu
    //         sudo modprobe -v mtgpu

    //     """
    //     // sudo echo "options mtgpu display=none disable_vpu=1 disable_audio=1 mtgpu_drm_major=2 mtgpu_pstate_mode=0 rb_switch_enable=0 ignore_mpc_config=1 " | sudo tee /etc/modprobe.d/mtgpu.conf
    // }
    // stage('install musatoolkit') {
    //     sh '''
    //         cd ${env.musaToolkit}
    //         tar -xf *musa_toolkits*.tar.gz
    //         cd ./musa_toolkits_install
    //         sudo ./install.sh
    //     '''
    // }
    // 安装alg, thrust
    // stage('install package') {
    //     sh '''
    //         cd /root/jenkins/musa_cts_light_test/package
    //         dpkg -i *.deb
    //     '''
    // }
    // stage('libdrm test') {
    //     // sh '''
    //     //     cd /root/123/build
    //     //     export LD_LIBRARY_PATH=/root/123/build:$LD_LIBRARY_PATH
    //     //     export LD_LIBRARY_PATH=/usr/local/musa/lib/:$LD_LIBRARY_PATH
    //     //     ./mtgpu_test -s 1 -t 1 | grep passed || return 1
    //     // '''
    //     sh '''
    //         echo gpu,work,subm > /sys/kernel/debug/musa/apphint/0/EnableMetaLogGroup
    //     '''
    // }
    stage('evn check') {
    // sh '''
    //     export LD_LIBRARY_PATH=/usr/local/musa/lib/:$LD_LIBRARY_PATH
    //     /usr/local/musa/bin/musaInfo
    //     /usr/local/musa/bin/muMemcpyTest
    //     #cd /root/jenkins/musa_cts_light_test/musa_cts/scripts/demo/
    //     #/usr/local/musa/bin/mcc axpy.cu -o axpy -mtgpu -lmusart --cuda-gpu-arch=mp_31 -I .
    //     #/root/jenkins/musa_cts_light_test/musa_cts/scripts/demo/axpy
    // '''
    }
}

def setup() {
    stage('git set') {
    // sh '''
    //     export LD_LIBRARY_PATH=/usr/local/musa/lib/:$LD_LIBRARY_PATH
    //     /usr/local/musa/bin/musaInfo
    //     /usr/local/musa/bin/muMemcpyTest
    //     #cd /root/jenkins/musa_cts_light_test/musa_cts/scripts/demo/
    //     #/usr/local/musa/bin/mcc axpy.cu -o axpy -mtgpu -lmusart --cuda-gpu-arch=mp_31 -I .
    //     #/root/jenkins/musa_cts_light_test/musa_cts/scripts/demo/axpy
    // '''
    }
}

def main() {
    currentBuild.description = ''
    env.hosts = env.host_name
    //env.tests = 'mufft_cts,mublas_cts, mtcc_test, musa_cts_ptsz'
    env.tests = 'mufft_cts'
    // env.tests = 'murand_cts'
    def hosts = env.hosts.split(',')
    def tasks = [:]
    def setupTasks = [:]
    hosts.each { host ->
        setupTasks[host] = {
            node(host) {
                setup()
            }
        }
    }
    parallel setupTasks

    env.tests.split(',').eachWithIndex { test, index ->
        def host = hosts[index % hosts.size()]
        test = test.trim()
        host = host.trim()
        tasks[test] = {
            node(host) {
                println 'before setup'
                currentBuild.description += "${env.NODE_NAME}: ${test}<br>"
                println currentBuild.description
                println 'after setup'
                println test
                runTest(test)
                println 'after test'
            }
        }
    }
    parallel tasks
}

main()
