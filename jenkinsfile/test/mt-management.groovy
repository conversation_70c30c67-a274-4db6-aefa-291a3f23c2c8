@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

// nodeLabel runChoice linuxDdkBranch packageName linuxDdkPackageUrl runCov runMpc dockerImage type coverageOssPath testLabel

def installDriver() {
    ddk.installLinuxDdkAndSetup()
}

def runTestInDocker(mtmlPackageName, cmd, isMpcTest = false) {
    docker.image(env.dockerImage).inside('-i -u 0:0 --privileged=true -v /dev:/dev -v /sys/:/sys') {
        dir("${mtmlPackageName}/mt-management") {
            def managementPackageUrl = constants.genPackageUrl(env.repo, env.branch, env.commitId, "${mtmlPackageName}.tar.gz")
            constants.downloadAndUnzipPackage(managementPackageUrl)
            sh """
                export GCOV_PREFIX="${env.WORKSPACE}"
                export GCOV_PREFIX_STRIP=5
                if [ "${isMpcTest}" = "true" ]; then
                    ${cmd} -p=3 --gtest_filter=-DeviceGetPcieSlotInfo.mtmlDeviceGetPcieSlotInfo_normalSuccess
                    ${cmd} -p=4 --gtest_filter=-DeviceGetPcieSlotInfo.mtmlDeviceGetPcieSlotInfo_normalSuccess
                else
                    ${cmd} -p=0
                fi
            """
            if (env.runCov == 'true') {
                def cvgReport = isMpcTest ? 'mpc_report.info' : "${env.type}_report.info"
                dir('build') {
                    commonLib.runCoverage(cvgReport)
                    if (mtmlPackageName.contains('Debug')) {
                        artifact.upload(cvgReport, env.coverageOssPath)
                    }
                }
            }
        }
    }
}

def setupMpc() {
    def pkgUrl = utils.runCommandWithStdout('curl --insecure https://sh-moss.mthreads.com/sw-build/mthreads-gmi/develop/latestUrl.txt')
    sh """
        rm -rf gmi ||: && mkdir gmi && cd gmi && wget -q --no-check-certificate ${pkgUrl} && ls *.tar.gz | xargs -n1 tar xzvf
        service lightdm stop ||:
        rmmod mtgpu ||:
        insmod /usr/lib/modules/`uname -r`/updates/dkms/mtgpu.ko display=dummy
        ./bin/LINUX/x86_64/RELEASE/mthreads-gmi mpc -enable -i 0
    """
}

def disableMpc() {
    sh './gmi/bin/LINUX/x86_64/RELEASE/mthreads-gmi mpc -disable -i 0'
}

def runMtmlTest() {
    def testTask = [:]
    testLists.each { mtmlPackageName, cmd ->
        testTask[mtmlPackageName] = {
            if (env.runMpc == 'false') {
                runTestInDocker(mtmlPackageName, cmd)
            } else {
                //mpc test
                setupMpc()
                runTestInDocker(mtmlPackageName, cmd, true)
                disableMpc()
            }
        }
    }
    parallel testTask
}

runner.start(env.runChoice) {
    env.repo = env.gitlabSourceRepoName ?: env.repo
    env.branch = env.gitlabSourceBranch ?: env.branch
    env.commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    testLists = (env.repo == 'gr-kmd' || env.repo == 'mt-rm' || env.runMpc == 'true') ? [
        'mtml_Debug_x86_64':'./build/test/sdk_googletest'
    ] : [
        'mtml_Debug_x86_64':'./build/test/sdk_googletest',
        'mtml_Release_x86_64':'./build/test/sdk_googletest'
    ]
    def workflow = [
        'install driver': [closure: { installDriver() }],
        'run test': [closure: { runMtmlTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ]

    runPipeline(workflow)
}
