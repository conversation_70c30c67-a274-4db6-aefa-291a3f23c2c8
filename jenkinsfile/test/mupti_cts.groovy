@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * ctsBranch (String) - origin/release_KUAE_2.0_for_PH1_M3D
 * ctsCommitId (String) - ''
 * linuxDdkPackageUrl (String) - daily ddk pkg
 * runtimePkg (String) - newest
 * muptiPkg (String) - daily:"sh-moss/sw-build/computeQA/cuda_compatible/release_KUAE_2.0_for_PH1_M3D/${date}/" or smoke
 * relyPkg (Multiline String) - sh-moss/sw-build/computeQA/musa/newest/mtcc-nightly-x86_64-linux-gnu-ubuntu-20.04.tar.gz;
                                sh-moss/sw-build/computeQA/musa/musa_cmake.tar.gz;
 * mtgpuArch (String) - 'mp_22'
 * testType (String) - 'daily' or 'smoke'
 * timeoutFactor (String) - '1'
 * testEnv (Multiline String) - """
                      export MUSA_INSTALL_PATH=/usr/local/musa;
                      export PATH=/usr/local/musa/bin:\$PATH;
                      export LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/x86_64-linux-gnu/musa/:/usr/lib/x86_64-linux-gnu/:\${LD_LIBRARY_PATH};
                      export MTGPU_ARCH=${mtgpuArch};
                      export TEST_TYPE=${testType};
                      export TIMEOUT_FACTOR=${timeoutFactor}
                      """
 * runChoice (Choice) - node [node | pod]
 * cluster (String) - 'releaseFarm'
 * containerImage (String) - 'sh-harbor.mthreads.com/qa/ubuntu-22-04-mudnn-test-image:v1'
 * podNodeSelector (String) - 'In=mt=buildserver'
 * podResources (String) - 'requests=cpu=8;requests=memory=20Gi;limits=cpu=12;limits=memory=32Gi;limits=mthreads.com/gpu=1;'
 * nodeLabel (String) - Linux_build
*/

gitLib = new git()
commonLib = new common()

env.repo = 'mupti_cts'

List envs = env.testEnv ? env.testEnv.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.ctsCommitId = gitLib.fetchCode(env.repo, env.ctsBranch, env.ctsCommitId)
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        sh '''
            apt update ||:
            apt install dkms -y ||:
        '''
        if (env.linuxDdkPackageUrl.startsWith('oss')) {
            linuxDdkPackageUrl = constants.ossPathToUrl(env.linuxDdkPackageUrl)
        }
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpEnv() {
    if (env.runChoice == 'node') {
        sh '''
            apt update ||:
            apt install dkms -y ||:
        '''
        if (env.linuxDdkPackageUrl.startsWith('oss')) {
            linuxDdkPackageUrl = constants.ossPathToUrl(env.linuxDdkPackageUrl)
        }
        ddk.installLinuxDdk(linuxDdkPackageUrl)
    }

    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        else {
            if (!env.linuxDdkPackageUrl.contains('release_KUAE_2.0_for_PH1_M3D')) {
                error('no musa-runtime pkg!')
            }
        }
        if (env.muptiPkg) {
            constants.downloadPackage(env.muptiPkg)
            sh 'dpkg -i *mupti*.deb'
        }
        else { error('no mupti pkg!') }
        if (!env.mtccPackageUrl || !env.musaCmakePackageUrl) { error('mtccPackageUrl & musaCmakePackageUrl are required') }
        installDependency([
            'mtcc': env.mtccPackageUrl,
            'musa_cmake': env.musaCmakePackageUrl
        ])
    }
}

def buildTest() {
    dir(env.repo) {
        sh """
            ${envExport}
            mkdir build
            cd build
            cmake .. && make -j12

            cd ../elf
            ./compile_elf.sh
        """
    }
}

def runTest() {
    dir(env.repo) {
        try {
            sh """
                ${envExport}
                cd pytest
                pytest -v --alluredir=mupti_allure_result || :
            """
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            sh '''
                dmesg -T || :
            '''
        }
    }
}

def checkResult() {
    dir("${env.WORKSPACE}/${env.repo}/pytest") {
        commonLib.allure('mupti_allure_result')
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'setup in docker': [closure: { setUpEnv() }],
        'build test': [closure: { buildTest() }],
        'run test': [closure: { runTest() }]
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
    ], [disablePre: true])
}, pre: {
    runPipeline([
        'setup pre': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}])
