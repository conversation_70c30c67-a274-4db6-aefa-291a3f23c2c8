@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common
import org.swqa.tools.SshTool

gitLib = new git()
commonLib = new common()

/*
 * testEnvVar (Multiline String) default 'usual export', split by ';'
 * linuxDdkPackageUrl (String) - default ''
 * musaToolkitsPackageUrl(String) - ''
 * mtshmemPackageUrl (String) - default ''
 * mtmlPackageUrl (String) - default ''
 * relyScriptsUrl (String) - default ''
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - deepeTest
*/

env.mtshmemCtsRepo = 'mtshmem_cts'
containerName = 'mtshmem_smoke_test'
mtshmemPackageName = env.mtshmemPackageUrl.split('/').last()
mtmlPackageName = env.mtmlPackageUrl.split('/').last()
musaToolkitsPackageName = env.musaToolkitsPackageUrl.split('/').last()
linuxDdkPackageName = env.linuxDdkPackageUrl.split('/').last()
peermemPackageName = env.peermemPackageUrl.split('/').last()
relyScriptsName = env.relyScriptsUrl.split('/').last()
testEnvVar = env.testEnvVar ? commonLib.parseExportVars(env.testEnvVar).join(';') : ''
remoteWorkDir = "${env.remoteWorkDir}/${BUILD_NUMBER}/"
remoteDockerDir = "/home/<USER>/${BUILD_NUMBER}"
ipConfig = readJSON text: env.ipConfig
sshService = new SshTool(this, [user: ipConfig.service.user, ip: ipConfig.service.ip, port: ipConfig.service.port.toInteger(), sshOpts: env.sshOpts])
sshClients = []
ipConfig.client.each { clientInfo ->
    def sshClient = new SshTool(this, [user: clientInfo.user, ip: clientInfo.ip, port: clientInfo.port.toInteger(), sshOpts: env.sshOpts])
    sshClients << sshClient
}

def fetchCode() {
    env.mtshmemCtsCommit = gitLib.fetchCode(env.mtshmemCtsRepo, env.mtshmemCtsBranch, env.mtshmemCtsCommit ?: '')
    sh 'tar -czf mtshmem_cts.tar.gz mtshmem_cts'
}

def setupDockerEnv(boolean initDockerEnv, String dockerImage, SshTool remoteHost) {
    def volumes = [
        '/data/swqa/swqa_shared_dir/mtshmem_test:/home/<USER>',
        '/usr/lib/x86_64-linux-gnu/:/usr/lib/x86_64-linux-gnu/',
        '/usr/bin/:/usr/bin/'
    ]
    def volumeArgs = volumes.collect { "-v ${it}" }.join(' ')
    def containerExists = remoteHost.execWithOutput("docker ps -a --format '{{.Names}}' | grep -w ${containerName} || true")

    if (initDockerEnv) {
        echo "Performing Docker setup operation, image: ${dockerImage}"
        remoteHost.exec("""
            set -e
            docker pull ${dockerImage} || echo "Warning: docker pull failed, possibly using cached image"
            docker rm -f ${containerName} 2>/dev/null || true
            docker run -d --privileged --net host --name ${containerName} ${volumeArgs} ${dockerImage} sleep infinity

            echo "Configuring SSH inside container..."
            docker exec ${containerName} bash -c '
                set -e
                echo -e "ClientAliveInterval 60\\nClientAliveCountMax 5\\nPermitRootLogin yes" >> /etc/ssh/sshd_config
                service ssh restart
            '
        """)
    } else {
        echo 'Checking container status...'
        if (containerExists) {
            echo 'Restarting existing container...'
            remoteHost.exec("docker restart ${containerName}")
        } else {
            echo 'Container does not exist, creating a new one...'
            remoteHost.exec("docker run -d --privileged --net host --name ${containerName} ${volumeArgs} ${dockerImage} sleep infinity")
        }
    }
}

def transPackage() {
    sshService.exec("sudo mkdir -p ${remoteWorkDir}")
    constants.downloadPackage(env.mtmlPackageUrl)
    constants.downloadPackage(env.mtshmemPackageUrl)
    constants.downloadPackage(env.musaToolkitsPackageUrl)
    constants.downloadPackage(env.linuxDdkPackageUrl)
    constants.downloadPackage(env.relyScriptsUrl)
    constants.downloadPackage(env.peermemPackageUrl)
    //scp to remote work dir
    sshService.scpTo(mtmlPackageName, remoteWorkDir)
    sshService.scpTo(mtshmemPackageName, remoteWorkDir)
    sshService.scpTo(musaToolkitsPackageName, remoteWorkDir)
    sshService.scpTo(linuxDdkPackageName, remoteWorkDir)
    sshService.scpTo(relyScriptsName, remoteWorkDir)
    sshService.scpTo('mtshmem_cts.tar.gz', remoteWorkDir)
    sshService.scpTo(peermemPackageName, remoteWorkDir)
}

initCmdOnNode = """
    cd /data/swqa/swqa_shared_dir/; ./uninstall_linux_ddk.sh
    cd ${remoteWorkDir}
    mkdir -p musa_install; tar xzf ${musaToolkitsPackageName} -C musa_install
    tar xzf ${mtshmemPackageName}
    tar xzf mtshmem_cts.tar.gz
    chmod +x ${relyScriptsName}; ./${relyScriptsName}
    dpkg -P mtml; dpkg -i ${linuxDdkPackageName}
    dpkg -i ${mtmlPackageName}
    echo "${env.modprobeOptions}" >> /etc/modprobe.d/mtgpu.conf
    modprobe -rv mtgpu
    modprobe -v mtgpu
    dpkg -i ${peermemPackageName}
    modprobe -v mt_peermem
    sleep 180
    cat /sys/module/mtgpu/parameters/tp_pci_vender_id
    export PATH=/usr/local/musa/bin:\$PATH
    muInfo
"""

initCmdInDocker = """
    docker exec ${containerName} bash -c "
        set -e
        echo 'Uninstall existing musa...'
        rm -rf /usr/local/musa*
        rm -rf /usr/local/mtshmem*
        cd ${remoteDockerDir}
        dpkg -i ${mtmlPackageName}
        cd mtshmem; ./install.sh
        cd ${remoteDockerDir}
        cd musa_install/musa_toolkits_install; rm -rf /usr/local/musa*; ./install.sh
        cd ${remoteDockerDir}
        export PATH=/usr/local/musa/bin:\$PATH
        export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH
        musaInfo
    "
"""

def setUpOnService() {
    sshService.exec(initCmdOnNode)
    setupDockerEnv(env.initDockerEnv?.toBoolean(), env.dockerImage, sshService)
    sshService.exec(initCmdInDocker)
}

// def setUpOnClients() {
//     sshClients.each { client ->
//         client.exec("mount ${ipConfig.service.ip}:/data/swqa/swqa_shared_dir /data/swqa/swqa_shared_dir")
//         client.exec(initCmdOnNode)
//         setupDockerEnv(env.initDockerEnv?.toBoolean(), env.dockerImage, client)
//         client.exec(initCmdInDocker)
//     }
// }

def mtshmemRemoteTest() {
    sshService.exec("""
        docker exec ${containerName} bash -c "
            set -e
            cd ${remoteDockerDir}/mtshmem_cts/pytest
            export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH
            ${testEnvVar}
            pytest -v --alluredir=./allure_result_mtshmem_cts > testOutput.txt 2>&1
        "
    """)
}

def uploadTestResult() {
    sshService.exec("cd ${remoteWorkDir}/mtshmem_cts/pytest; tar -czvf allure_result_mtshmem_cts_${BUILD_NUMBER}.tar.gz allure_result_mtshmem_cts; sudo chmod 777 -R *")
    sshService.scpFrom("${remoteWorkDir}/mtshmem_cts/pytest/allure_result_mtshmem_cts_${BUILD_NUMBER}.tar.gz", './')
    artifact.uploadTestReport("allure_result_mtshmem_cts_${BUILD_NUMBER}.tar.gz", env.reportOssPath)
    sh "tar -xzvf allure_result_mtshmem_cts_${BUILD_NUMBER}.tar.gz"
    commonLib.allure('allure_result_mtshmem_cts')
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'transPackage': [closure: { transPackage() }],
        'setUpOnService': [closure: { setUpOnService() }],
        // 'setUpOnClients': [closure: { setUpOnClients() }],
        'mtshmemRemoteTest': [closure: { mtshmemRemoteTest() }]
    ]
    runPipeline(workflow, [
        post: {
            uploadTestResult()
            sshService.exec("""
                docker exec ${containerName} bash -c "
                    cd ${remoteDockerDir}/mtshmem_cts/pytest
                    cat testOutput.txt ||:
                "
            """)
        }
    ])
}
