@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://sh-moss.mthreads.com/sw-build/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://sh-moss.mthreads.com/sw-build/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;sh-moss/sw-build/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'sh-moss/sw-build/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'perf_cts'
ddkBranch = env.ddkBranch ?: 'master'
ddk_version = env.ddk_version ?: 'none'
envExport = utils.generateEnvExport(env)
testType = env.testType ?: 'benchmark'

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaAsmPackageUrl = env.musaAsmPackageUrl?.trim() ? env.musaAsmPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }

    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')

    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
    }

    if (env.muDNNPackageUrl) {
        constants.downloadAndUnzipPackage(env.muDNNPackageUrl)
        sh 'cd mudnn && ./install_mudnn.sh -i'
    }

    if (env.mcclPackageUrl) {
        constants.downloadAndUnzipPackage(env.mcclPackageUrl)
        sh 'cd mccl && ./install.sh'
    }

    if (env.muAlgPackageUrl) {
        musa.installmuAlg(env.muAlgPackageUrl)
    }
    else {
        gitLib.fetchCode('muAlg', 'develop')
        dir('muAlg') {
            sh './mt_build.sh -i'
        }
    }
    if (env.muThrustPackageUrl) {
        musa.installmuThrust(env.muThrustPackageUrl)
    }
    else {
        gitLib.fetchCode('muThrust', 'develop')
        dir('muThrust') {
            sh './mt_build.sh -i'
        }
    }

    if (env.torchPackageUrl) {
        constants.downloadPackage(env.torchPackageUrl)
    }

    if (env.torchMusaPackageUrl) {
        constants.downloadPackage(env.torchMusaPackageUrl)
    }

    if (env.tritonMusaPackageUrl) {
        constants.downloadAndUnzipPackage(env.tritonMusaPackageUrl)
    }

    // 安装 torch，torch_musa，triton_py310 包
    sh """
        ${envExport}
        . \"/root/miniforge3/etc/profile.d/conda.sh\" > /dev/null && conda activate py310
        conda install -c conda-forge cmake -y
        pip install ./torch*.whl
        cd ${env.WORKSPACE}/bdist_wheel && pip install ./triton*.whl

        cd ${env.WORKSPACE}/${env.repo}
        git submodule update --init --recursive
        # sleep 36000000000
        cd ${env.WORKSPACE}/${env.repo}/triton/liger-kernel
        pip install -e .
    """

    musa.installMusify(env.musifyPackageUrl)
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        dir(env.repo) {
            sh """
                . \"/root/miniforge3/etc/profile.d/conda.sh\" > /dev/null && conda activate py310
                ${envExport}
                export TEST_TYPE=${testType}
                git submodule update --init --recursive
                mkdir -p build && cd build
                cmake --version ||:
                cmake .. ${env.compileArgs}
                make -j ${env.compileParallel}
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${envExport}
                export TEST_TYPE=${testType}
                export DDK_BRANCH=${ddkBranch}
                export DDK_VERSION=${ddk_version}
                sync && echo 3 > /proc/sys/vm/drop_caches ||:
                mthreads-gmi && mthreads-gmi -q

                # ${constants.genCondaActivate('mathx')}
                # python run_test.py ${env.testArgs} -m 'not triton' ||:

                . \"/root/miniforge3/etc/profile.d/conda.sh\" > /dev/null && conda activate py310
                python run_test.py ${env.testArgs} ||:
            """
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh 'tar -czvf perf_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('perf_cts_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'mathLibs perf test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'upload result': [closure: {
                catchError(stageResult: 'FAILURE') {
                        uploadTestResult()
                }
            }
            ],
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
