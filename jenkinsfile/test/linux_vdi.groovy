@Library('swqa-ci')

// import org.swqa.tools.common
// import org.swqa.tools.git

// commonLib = new common()
// gitLib = new git()

// runChoice nodeLabel testLabel setEnvTimeout testTimeout insmodOptions vdiHostPackageName vdiHostPackageUrl vdiGuestPackageName vdiGuestPackageUrl

def setVdiEnv() {
    ddk.installVdiAndSetup()
}

def runVdiGlmark2() {
    node("${env.NODE_NAME}_VDI") {
        retry(3) {
            sh '''
                systemctl stop lightdm ||:
                killall Xorg ||:
                nohup /usr/bin/Xorg -maxclients 2048 &
                sleep 5
                dmesg -T | tee /root/dmesgOutBeforeTest.log
                dmesg -C
                export DISPLAY=:0.0 && glmark2 | tee /root/glmark2.log
                dmesg -T | tee /root/dmesgOutAfterTest.log
            '''
        }
        def stdout = utils.runCommandWithStdout('grep \"glmark2 Score\" /root/glmark2.log')
        currentBuild.description += "${stdout}<br>"

        def errDmesg = utils.runCommandWithStdout('cat /root/dmesgOutAfterTest.log | grep -i gpu | grep -iE "failed|error" ||:')
        if (errDmesg) {
            println "Error Message: ${errDmesg}"
            error 'Error found in dmesg. Please check.'
        }

        def testLog = utils.runCommandWithStdout('cat /root/glmark2.log')
        def matcher = (testLog =~ /GL_VENDOR:\s+([\w\s,\.]+)GL_RENDERER:/)
        if (matcher.find()) {
            def vendor = matcher[0][1].trim()
            println "Detected Vendor: ${vendor}"
            if (vendor != 'MOORE THREADS' && vendor != 'MTT') {
                error("VGPU status abnormal: Expected Vendor 'MOORE THREADS' or 'MTT', but found '${vendor}'. Please check.")
            }
        } else {
            error 'Vendor information not found in test output. Please verify the command output.'
        }
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'setVdiEnv': [closure: { setVdiEnv() }, maxWaitTime: [time: env.setEnvTimeout, unit: 'MINUTES']],
        'vdi test': [closure: { runVdiGlmark2() }, maxWaitTime: [time: env.testTimeout, unit: 'MINUTES'], setGitlabStatus: true, statusName: env.testLabel]
    ])
}
