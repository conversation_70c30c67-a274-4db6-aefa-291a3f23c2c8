@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()
env.mutlass_repo = 'mutlass'
env.muDNN_repo = 'muDNN'
env.muDNN_cts_repo = 'muDNN_cts'
env.compute_ci_library_repo = 'compute_ci_library'

List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    gitLib.fetchCode(env.mutlass_repo, env.mutlassBranch)
    gitLib.fetchCode(env.muDNN_cts_repo, env.mudnn_ctsBranch)
    gitLib.fetchCode(env.compute_ci_library_repo, 'master')
    gitLib.fetchCode(env.muDNN_repo, 'develop')
}

def envSet() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }

    constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')
    for (singleUrl in env.relyPkgUrl.split(';')) {
        constants.downloadAndUnzipPackage(singleUrl, '/opt/')
    }
}

def deployMutlass() {
    dir(env.mutlass_repo) {
        if (env.testQy1 == 'true') {
            makeOpt = (makeOpt ? makeOpt + ' ' : '') + '-DCOMPILE_ARCH_OPT=mp_21'
        }else {
            makeOpt = env.makeOpt
        }

        credentials.runWithCredential('SSH_GITLAB') {
            sh """
                ${constants.genCondaActivate('mtdnn_mtgpu')}
                ${envExport}
                sed -i 's/MainloopMp22TwoStage/MainloopMp22TwoStageUnpredicated/g' include/mutlass/gemm/collective/builders/mp22_gemm_builder.inl
                mkdir build && cd build && cmake .. ${makeOpt} && make test_profiler -j32
            """
        }
    }
}

def runTest() {
    dir(env.muDNN_cts_repo) {
        try {
            sh """
                ${constants.genCondaActivate('mtdnn_mtgpu')}
                ${envExport}
                python run_test.py -m mutlass_benchmark_tests ||:

                mkdir -p benchmark
                mv log/mutlass_benchmark_cases/gemm_benchmark_cases.csv ./benchmark
                mv log/asm_cases/asm_output.txt ./benchmark
            """
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            sh '''
                dmesg -T || :
            '''
        }
    }
}

def checkResult() {
    dir("${env.WORKSPACE}/${env.muDNN_cts_repo}") {
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.muDNN_cts_repo) {
        sh """
            tar -czvf ${env.reportName} test-report
            tar -czvf ${env.perfName} benchmark
        """
        artifact.uploadTestReport(env.reportName, env.reportOssPath)
        artifact.uploadTestReport(env.perfName, env.reportOssPath)
    }
}

def syncDatabase() {
    dir(env.compute_ci_library_repo) {
        sh """
            ${constants.genCondaActivate('mtdnn_mtgpu')}
            pip install pymysql ||:
            cd compute_qa_tool_chain/benchmark/
            python mutlass_benchmark.py -p ${env.WORKSPACE}/${env.muDNN_cts_repo}/benchmark/gemm_benchmark_cases.csv -t ${env.WORKSPACE}/${env.muDNN_cts_repo}/benchmark/asm_output.txt ||:
        """
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'env-setup': [closure: { envSet() }],
        'deploy-mutlass': [closure: { deployMutlass() }],
        'runTest': [closure: { runTest() }],
        'upload database': [closure: { syncDatabase() } ],
    ], [disablePost: true])
}, post: {
    runPipeline([
        'upload result': [closure: { uploadTestResult() } ],
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
    ], [disablePre: true])
}, pre: {
    if (env.runChoice == 'node' && env.installDDKonHost == 'true') {
        stage('install ddk') {
            // restart machine
            commonLib.reboot(env.NODE_NAME)
            // env recovery
            commonLib.recoverEnv()
            ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
        }
    }
}])
