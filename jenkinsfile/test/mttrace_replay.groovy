@Library('swqa-ci')
/*
 * parameters
 * traceDataUrl (String) - default ''
 * replayBashUrl (String) - default 'https://sh-moss.mthreads.com/dependency/mt_trace/run_mt_traces.sh'
 * traceOutputOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - cdFarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v15
 * podNodeSelector (String) - HOST_TYPE=In=server
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

def replay() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://***********:56548'
        env.replayBashUrl = env.replayBashUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.traceDataUrl = env.traceDataUrl?.replace('https://sh-moss.mthreads.com', endpoint)
    }
    constants.downloadPackage(env.replayBashUrl)
    constants.downloadPackage(env.traceDataUrl)
    sh """
        chmod 755 run_mt_traces.sh
        ./run_mt_traces.sh -m ${env.gpuType} -u 0 -l ${env.WORKSPACE}/${env.traceDataUrl.split('/')[-1]}
    """
}

def upload() {
    sh '''
        tar -czvf ci_dir.tgz ci_dir*/outputs
    '''
    artifact.uploadTestReport("${env.WORKSPACE}/ci_dir.tgz", env.traceOutputOssPath)
}

runner.start(env.runChoice, {
    def workflow = [:]
    workflow['musa_mttrace_replay'] = [ closure: { replay() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    workflow['replay output upload'] = [ closure: { upload() }]
    runPipeline(workflow)
})
