@Library('swqa-ci')

import groovy.transform.Field
import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

@Field List collectedResults = []
@Field def mrStatuses = []

// Get GitLab statuses once at start
def getMRStatuses() {
    try {
        retry(3) {
            sleep time: 10, unit: 'SECONDS'
            println('Waiting for 10 seconds ...')
            mrStatuses = new git().getGitlabCommitAllStatuses(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, env.gitlabSourceBranch)
            println '[getMRStatuses] Retrieved statuses:'
            mrStatuses.each { status ->
                println "  name: ${status.name}, status: ${status.status}"
            }
        }
    } catch (e) {
        echo "Error retrieving GitLab commit statuses: ${e.message}"
    }
}

def isVpsNode(nodeName) {
    return ['ls', 'hs', 'hg', 'ph1', 'sudi', 'quyuan'].any { nodeName.contains(it) }
}

def isVdiNode(nodeName) {
    return ['1101', '1108'].any { nodeName.contains(it) }
}

def initVps(Map config, String nodeType, String nodeName) {
    def vpsinfo = config.vpsinfo
    if (vpsinfo instanceof String) {
        vpsinfo = readJSON text: vpsinfo
    }
    if (!(vpsinfo instanceof Map) || !vpsinfo.containsKey(nodeType)) {
        error "vpsinfo missing key: ${nodeType}, current keys: ${vpsinfo instanceof Map ? vpsinfo.keySet() : vpsinfo}"
    }
    def vpsinfoParts = vpsinfo[nodeType]?.split(',') ?: []
    if (vpsinfoParts.size() < 3) {
        error "Invalid vpsinfo format: ${vpsinfoParts}"
    }

    def vpsmodel = vpsinfoParts[0]
    def vpsversion = vpsinfoParts[1]
    def vpstag = vpsinfoParts[2]

    if (nodeType.contains('hg')) {
        env.hgvpstag = vpstag
    } else if (nodeType.contains('ls')) {
        env.lsvpstag = vpstag
    } else if (nodeType.contains('ph1')) {
        env.ph1vpstag = vpstag
    }

    startVpsNodes(nodeName, vpsversion, nodeType.split('_')[-2], vpsmodel, vpstag)
}

def startVpsNodes(String nodeName, String vps_version, String chip_type, String model_type, String tag) {
    def parameters = [
        nodeName: nodeName,
        vps_version: vps_version,
        chip_type: chip_type,
        model_type: model_type,
        tag: tag
    ]

    runPipeline.runJob([
        job: 'win_start_vps_nodes',
        parameters: parameters
    ])
}

def startVdiNodes(String nodeLabel, String testEnv, String hostDriverPkgUrl) {
    def parameters = [
        nodeLabel: nodeLabel,
        testEnv: testEnv,
        hostDriverPkgUrl: hostDriverPkgUrl
    ]

    runPipeline.runJob([
        job: 'win_start_vdi_nodes',
        parameters: parameters
    ])
}

def shouldRunTest(mrStatuses, testName, statusName = null) {
    if (mrStatuses.isEmpty()) {
        echo 'No statuses found or unable to retrieve statuses from GitLab.'
        return true
    }

    def successfulStatuses = mrStatuses.findAll { it.status == 'success' && it.name.contains(testName) }

    if (successfulStatuses.isEmpty()) {
        echo "No successful statuses found containing ${testName}. Running test."
        return true
    }

    if (statusName && successfulStatuses.any { it.name.contains("${statusName}") }) {
        echo "Match found for ${statusName}. Skipping test."
        return false
}

    return true
}

def executePreTestCmds() {
    if (!env.preTestCmds || env.preTestCmds.trim().isEmpty()) {
        echo 'No preTestCmds parameter provided, skipping pre-test commands'
        return
    }

    def preTestCmds = []
    try {
        preTestCmds = readJSON text: env.preTestCmds
    } catch (e) {
        echo "Error parsing preTestCmds parameter: ${e.message}"
        preTestCmds = []
    }

    if (!preTestCmds || preTestCmds.isEmpty()) {
        echo 'No valid preTestCmds found'
        return
    }

    echo 'Executing pre-test commands...'
    preTestCmds.each { cmdInfo ->
        try {
            if (cmdInfo.buildPath) {
                dir(cmdInfo.buildPath) {
                    echo "Executing: ${cmdInfo.cmd} in ${cmdInfo.buildPath}"
                    bat "${cmdInfo.cmd}"
                }
            } else {
                echo "Executing: ${cmdInfo.cmd}"
                bat "${cmdInfo.cmd}"
            }
        } catch (e) {
            echo "Warning: Pre-test command failed: ${cmdInfo.cmd} - ${e.message}"
        }
    }
}

def executePostTestCmds() {
    if (!env.postTestCmds || env.postTestCmds.trim().isEmpty()) {
        echo 'No postTestCmds parameter provided, skipping post-test commands'
        return
    }

    def postTestCmds = []
    try {
        postTestCmds = readJSON text: env.postTestCmds
    } catch (e) {
        echo "Error parsing postTestCmds parameter: ${e.message}"
        postTestCmds = []
    }

    if (!postTestCmds || postTestCmds.isEmpty()) {
        echo 'No valid postTestCmds found'
        return
    }

    echo 'Executing post-test commands...'
    postTestCmds.each { cmdInfo ->
        try {
            if (cmdInfo.buildPath) {
                dir(cmdInfo.buildPath) {
                    echo "Executing: ${cmdInfo.cmd} in ${cmdInfo.buildPath}"
                    bat "${cmdInfo.cmd}"
                }
            } else {
                echo "Executing: ${cmdInfo.cmd}"
                bat "${cmdInfo.cmd}"
            }
        } catch (e) {
            echo "Warning: Post-test command failed: ${cmdInfo.cmd} - ${e.message}"
        }
    }
}

def getGpuIdMap() {
    return [
        'CI_GFX_S3000_Ubuntu_Linux_test_24.37': [
            'gpu_id': '3b',
            'test_node_guests': 'win10_CI_vdi_ubuntu_Test_01'
        ],
        'CI_GFX_S3000_Ubuntu_Linux_test_24.35': [
            'gpu_id': '3b',
            'test_node_guests': 'win10_CI_vdi_ubuntu_Test_02'
        ],
        'CI_GFX_S3000_Ubuntu_Linux_test_103.186': [
            'gpu_id': 'ca',
            'test_node_guests': 'win10_CI_vdi_ubuntu_Test_03'
        ]
    ]
}

def checkNodeAvailability(String nodeLabel) {
    lock('checkNodeAvailability') {
        echo "Checking availability for node label: ${nodeLabel}"

        def actualNodeName = null
        node(nodeLabel) {
            actualNodeName = env.NODE_NAME
            echo "Successfully connected to node: ${actualNodeName} with label: ${nodeLabel}"
        }
        return actualNodeName
    }
}

def getAvailableVdiNode(String nodeType) {
    def gpuIdMap = getGpuIdMap()
    def availableNodes = []

    echo "Getting VDI nodes for nodeType: ${nodeType}"

    if (env.vdiHostNode && env.vdiGuestNode) {
        echo "Using specified VDI nodes from environment: host=${env.vdiHostNode}, guest=${env.vdiGuestNode}"
        return [
            hostNode: env.vdiHostNode,
            guestNode: env.vdiGuestNode,
            gpuId: env.vdiGpuId ?: 'default'
        ]
    }

    gpuIdMap.each { expectedHostNode, nodeInfo ->
        echo "Checking for expected host node: ${expectedHostNode}"
        def actualHostNode = checkNodeAvailability('S3000_Ubuntu_VDI_Test1')
        if (actualHostNode && actualHostNode == expectedHostNode) {
            echo "Host node matches expected: ${expectedHostNode}, actual node: ${actualHostNode}"
            echo 'Using configured guest node...'
            def guestNode = nodeInfo.test_node_guests
            echo "Using guest node: ${guestNode}"
            availableNodes.add([
                hostNode: actualHostNode,
                guestNode: guestNode,
                gpuId: nodeInfo.gpu_id
            ])
            echo "Added VDI pair: host=${actualHostNode}, guest=${guestNode}, gpu=${nodeInfo.gpu_id}"
        } else {
            echo "Host node does not match expected ${expectedHostNode}, actual: ${actualHostNode}, skipping..."
        }
    }

    if (availableNodes.isEmpty()) {
        error "No available VDI nodes found for nodeType: ${nodeType}"
    }

    def selectedNode = availableNodes[0]
    echo "Selected VDI node pair: host=${selectedNode.hostNode}, guest=${selectedNode.guestNode}, gpu=${selectedNode.gpuId}"
    return selectedNode
}

def executeTestsInBatches(Map config, int n) {
    // Copy driver files using xcopy (simple and reliable)
    def copyDriverFiles = {
        bat 'xcopy "*.dll" "C:\\Test\\" /Y /Q 2>nul || echo "No dll files found"'
        bat 'xcopy "*.json" "C:\\Test\\" /Y /Q 2>nul || echo "No json files found"'
        echo 'Driver files copy commands executed'
    }

    def allTests = config.testinfo.collectMany { node, tests ->
        tests.collect { test -> [node: node, test: test] }
    }

    def testBatches = allTests.groupBy { it.node }.collectEntries { nodeType, nodeTests ->
        [(nodeType): nodeTests.collate(n)]
}

    def parallelStages = [:]

    testBatches.each { nodeType, batches ->
        def isVpsNodeType = isVpsNode(nodeType)
        def isVdiNodeType = isVdiNode(nodeType)

        def vdiNodeInfo = null
        if (isVdiNodeType) {
            vdiNodeInfo = getAvailableVdiNode(nodeType)
            echo "Using VDI node pair for ${nodeType}: host=${vdiNodeInfo.hostNode}, guest=${vdiNodeInfo.guestNode}"
        }

        batches.eachWithIndex { batch, index ->
            def stageName = "${nodeType} Batch-${index + 1}"
            if (!shouldRunTest(mrStatuses, env.testName)) {
                echo "Skipping tests for ${env.testName} as tests already ran successfully."
                return
            }

            parallelStages[stageName] = {
                def nodeName = null
                def nodeSelector = null

                if (isVdiNodeType && vdiNodeInfo) {
                    echo "Starting VDI workflow for ${nodeType}"

                    echo "Starting VDI environment on host: ${vdiNodeInfo.hostNode}"
                    startVdiNodes(vdiNodeInfo.hostNode, nodeType, env.hostDriverPkgUrl ?: '')

                    nodeSelector = vdiNodeInfo.guestNode
                } else {
                    nodeSelector = env.nodeSelector ? env.nodeSelector : "CI_GFX_Win10_${nodeType}"
                }

                node(nodeSelector) {
                    try {
                        nodeName = env.NODE_NAME
                        if (isVpsNodeType) {
                            node('Status_jump') {
                                initVps(config, nodeType, nodeName)
                            }
                        } else {
                            new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
                        }
                        sleep(time: 10, unit: 'SECONDS')
                        deleteDir()
                        if (env.driverUrl) {
                            winTest.update_driver(env.driverUrl, 'MT-GEN1-ENCODE', 'PCI\\VEN_1ED5', true, nodeType, config.vpsinfo)
                        }else {
                            winTest.update_driver_latest(env.driverType)
                        }
                        if (isVpsNodeType) {
                            dir('driver') {
                                copyDriverFiles()
                            }
                        }

                        batch.each { testInfo ->
                            def testName = testInfo.test.name
                            def statusName = testInfo.test.statusName
                            def caselist = testInfo.test.caselist
                            def timelimit = testInfo.test.timelimit
                            def driverType = testInfo.test.driverType
                            echo "testName=${testName}, statusName=${statusName}, caselist=${caselist}, timelimit=${timelimit}, driverType=${driverType}"

                            echo "Running ${statusName} on ${nodeType}"

                            if (shouldRunTest(mrStatuses, testName, statusName)) {
                                if (!testName.contains('mttrace')) {
                                    env.mailReceiver = ''
                                }
                                stage(statusName) {
                                    try {
                                        node('Status_jump') {
                                            new git().setGitlabStatus("${statusName}", 'canceled')
                                            new git().setGitlabStatus("${statusName}", 'running')
                                        }
                                        timeout(timelimit) {
                                            // Execute pre-test commands
                                            executePreTestCmds()

                                            try {
                                                // Execute the actual test
                                                if (caselist) {
                                                    winTest."${testName}"(caselist)
                                                } else {
                                                    winTest."${testName}"()
                                                }
                                            } finally {
                                                // Execute post-test commands
                                                executePostTestCmds()
                                            }
                                        }
                                        node('Status_jump') {
                                            new git().setGitlabStatus("${statusName}", 'success')
                                        }
                                    } catch (e) {
                                        currentBuild.result = 'FAIL'
                                        print(e)
                                        node('Status_jump') {
                                            new git().setGitlabStatus("${statusName}", 'failed')
                                        }
                                        def devOwner = env.devOwner ?: ''
                                        def testOwner = env.testOwner ?: ''
                                        currentBuild.description += 'Test case execution failed. Please check or contact SWQA'
                                        currentBuild.description += "<BR>For CI pipeline/environment issues, contact CI: ${testOwner}"
                                        currentBuild.description += "<BR>For business-related issues, contact Developer: ${devOwner}"
                                        error "${statusName} failed"
                                    }
                                }
                            } else {
                                echo "Skipping ${statusName} on ${nodeType}, already successful."
                            }
                        }
                    } catch (e) {
                        currentBuild.result = 'FAIL'
                        print(e)
                        error 'Test failed'
                    } finally {
                        try {
                            if (isVpsNodeType) {
                                node('Status_jump') {
                                    initVps(config, nodeType, nodeName)
                                }
                            } else if (isVdiNodeType && vdiNodeInfo) {
                                node('Status_jump') {
                                    echo "Bringing host node online: ${vdiNodeInfo.hostNode}"
                                    new common().setNodeStatus(vdiNodeInfo.hostNode, 'online')
                                }
                            } else {
                                new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
                            }
                        } catch (e) {
                            echo "finalize failed: ${e.message}"
                        }
                    }
                }
            }
        }
    }

    parallel parallelStages
}

def initGitlabStatus(String statusName) {
    node('Status_jump') {
        new git().setGitlabStatus(statusName, 'canceled')
        new git().setGitlabStatus(statusName, 'running')
    }
}

runner.start(env.runChoice) {
    int numTestBatches = (env.numTestBatches ?: '5').toInteger()

    def testConfig = [:]

    def vpsinfo = env.vps ?: [:]
    testConfig.vpsinfo = vpsinfo

    def testinfo = [:]
    if (env.testEnv && env.timelimit) {
        def envs = env.testEnv.tokenize(',')
        def caselists = env.caselist ? env.caselist.tokenize(',') : []
        def partSuffix = (0 ..< 10).collect { 'part' + ((char)('A'.charAt(0) + it)) }

        envs.each { envName ->
            if (caselists && caselists.size() == 1) {
                testinfo[envName] = [[
                    name: env.testName,
                    statusName: env.statusName,
                    driverType: env.driverType,
                    caselist: caselists[0],
                    timelimit: env.timelimit as Integer
                ]]
            } else if (caselists && caselists.size() > 1) {
                testinfo[envName] = (0..<caselists.size()).collect { idx ->
                    def cl = caselists[idx]
                    def partName = idx < partSuffix.size() ? "${env.statusName}_${partSuffix[idx]}" : "${env.statusName}_part${idx + 1}"
                    [
                        name: env.testName,
                        statusName: partName,
                        caselist: cl,
                        driverType: env.driverType,
                        timelimit: env.timelimit as Integer
                    ]
                }
            } else {
                testinfo[envName] = [[
                    name: env.testName,
                    statusName: env.statusName,
                    driverType: env.driverType,
                    timelimit: env.timelimit as Integer
                ]]
            }
        }
    }
    testConfig.testinfo = testinfo

    testConfig.testinfo.each { nodeType, tests ->
        if (env.gitlabActionType == 'NOTE' && env.gitlabTriggerPhrase =~ /(?i)cirenew/) {
            tests.each { test ->
                initGitlabStatus("${test.statusName}")
            }
        }
    }
    // Get statuses at start
    getMRStatuses()

    testConfig.testinfo.each { nodeType, tests ->
        testConfig.testinfo[nodeType] = tests.findAll { test ->
            !mrStatuses.any { it.status == 'success' && it.name == test.statusName }
        }
    }

    if (!testConfig || testConfig.isEmpty()) {
        echo '[win-test] testConfig is empty, skip tests.'
        return
    }

    println '[win-test] testConfig map:'
    testConfig.each { k, v ->
        println "  ${k}: ${v}"
    }

    def workflow = [
        'executeTestsInBatches': [closure: { executeTestsInBatches(testConfig, numTestBatches) }, setGitlabStatus: true, statusName: "${env.statusName}"]
    ]

    runPipeline(workflow, [disablePost:true])
}
