@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * musaCtsBranch - default 'm3d_master'
 * musaCtsCommitId - default 'm3d_master'
 * mtccBranch - default 'm3d_master'
 * mtccCommitId - default 'm3d_master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default ''
 * musaAsmReleasePackageUrl (String) -default ''
 * condaPackageUrl (String) - default ''
 * AmodlePackageUrl (String) - default ''
 * CmodlePackageUrl (String) - default ''
 * muArgPackageUrl (String) - default ''
 * muthrustPackageUrl (String) - default ''
 * gpuArch (String) - default ''
 * qemuVersion (String) - default ''
 * qemuModel (String) - default ''
 * exports (Multiline String) default 'usual export', split by ';'
 * testType (String) - default 'smoke'
 * testMark (String) - default ''
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
*/

List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

imagePathhost = '/data/qemu/linux/cts_vps'

def fetchCode() {
    env.musaCtsCommitId = gitLib.fetchCode('musa_cts', env.musaCtsBranch, env.musaCtsCommitId)
    // env.mtccTestCommitId = gitLib.fetchCode('mtcc_test', env.mtccTestBranch, env.mtccTestCommitId)
    if (!fileExists("${imagePathhost}/${env.vpsImg}")) {
        sh """
            mkdir -p ${imagePathhost}; cd ${imagePathhost}
            sudo wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/computeQA/vps/vps-img/${env.vpsImg}
            sleep 5
        """
    }
    sh """
        cp ${imagePathhost}/${env.vpsImg} ${env.WORKSPACE}
    """
}

def buildDiagsys() {
    submoduleMaps = constants.getLinuxDdkSubmoduleCommitInfo(env.gitlabSourceBranch, gitlabMergeRequestLastCommit, false)
    runnerHttp.runJob('diagsys_build', ['Shared_Include_Commit_Id':submoduleMaps['shared_include']])
}

def compileMusaAndCases() {
    def maxRetries = 3
    def attempt = 0
    def success = false

    while (attempt < maxRetries && !success) {
        attempt++
        try {
            echo "第 ${attempt} 次尝试拉起 Docker 容器构建环境..."
            docker.image(env.dockerImage).inside('-i -u 0:0 -v /home/<USER>/home/<USER>') {
                // apt.updateAptSourcesList()
                // sh '''
                //     pip install ahocorapy
                //     apt-get install -y pciutils
                // '''

                ddk.installLinuxDdk(env.linuxDdkPackageUrl)

                // 安装MUSA-Runtime
                if (env.musaRuntimePackageUrl) {
                    musa.installMusaRuntime(env.musaRuntimePackageUrl)
                }

                installDependency(['mtcc': env.mtccPackageUrl])
                def dependencies = ['mtcc': env.mtccPackageUrl]
                installDependency(dependencies)

                constants.downloadAndUnzipPackage(env.musaAsmReleasePackageUrl)
                dir('musa_asm/build/bin') {
                    sh 'cp musaasm /usr/local/musa/bin/'
                }
                dir('muAlg') {
                    constants.downloadAndUnzipPackage(env.muAlgPackageUrl)
                    sh '''
                        cd package
                        ls *.deb | xargs -n1 dpkg -i
                    '''
                }
                dir('muthrust') {
                    constants.downloadAndUnzipPackage(env.muthrustPackageUrl)
                    sh '''
                        cd package
                        ls *.deb | xargs -n1 dpkg -i
                    '''
                }

                dir('pkg') {
                    constants.downloadAndUnzipPackage(env.linuxDdkPackageUrl)
                    constants.downloadAndUnzipPackage(env.mtccPackageUrl)
                    if (env.musaRuntimePackageUrl) {
                        constants.downloadAndUnzipPackage(env.musaRuntimePackageUrl)
                    }
                }

                musa.installMusify(env.musifyPackageUrl)
                dir('musa_cts') {
                    sh '''
                        rm -rf .git* ||:
                        rm -rf mtcc/mtcc_test/ ||:
                    '''
                }

                // 安装miniforge
                if (!fileExists('/home/<USER>/miniforge')) {
                    constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')
                }
                oss.install()
                if (env.AmodlePackageUrl) {
                    dir("${env.WORKSPACE}") {
                        sh """
                            mkdir -p ${env.WORKSPACE}/soc_model/release_mode/amodel/ph1s
                            mc cp -r ${env.AmodlePackageUrl} ${env.WORKSPACE}/soc_model/release_mode/amodel/ph1s
                        """
                    }
                }
                if (env.CmodlePackageUrl) {
                    dir("${env.WORKSPACE}") {
                        sh """
                            mc cp -r ${env.CmodlePackageUrl}arch_DOG_release_binary.tar.gz ./
                            tar -xzvf arch_DOG_release_binary.tar.gz >/dev/null
                            mkdir -p ${env.WORKSPACE}/soc_model/release_mode/cmodel/ph1s/
                            cd ci_env/transif_binary/
                            cp param_configuration.conf  ${env.WORKSPACE}
                            cp *.so* qemu-system-riscv64 ${env.WORKSPACE}/soc_model/release_mode/cmodel/ph1s/ -rf
                        """
                    }
                }

                dir('musa_cts') {
                    // 编译musa_cts api cases
                    sh """
                        export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
                        export PATH=/usr/local/musa/bin:\$PATH
                        export LD_LIBRARY_PATH=/usr/local/musa/lib/:\${LD_LIBRARY_PATH}
                        export MTGPU_ARCH=${env.gpuArch}
                        cmake -B build .
                        cmake --build build -j48
                    """

                    // 编译musa_cts mtcc cases
                    sh """
                        export TEST_TYPE=${env.testType}
                        export RUN_TYPE=compile
                        export MTGPU_ARCH=${env.gpuArch}
                        export COMPILE_EXTRA_ARGS=\"-mllvm -mtgpu-force-wave32=true\"
                        export LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/local/lib/musa/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH
                        export PATH=/usr/local/musa/bin:\$PATH
                        cd pytest/test_mtcc
                        mkdir -p ${env.WORKSPACE}/musa_cts/log
                        . /home/<USER>/miniforge/etc/profile.d/conda.sh && conda activate mathx
                        pytest -v -n 24 test_musa_mtcc.py test_musa_mtcc_fuzz.py ||:
                    """
                }
            }
            success = true // 如果没有异常，说明成功了
        } catch (err) {
            if (err.message?.contains('Timeout after 180 seconds') && attempt < maxRetries) {
                echo "第 ${attempt} 次 Docker 容器启动失败：${err.message}"
                echo '将进行下一次重试...'
                sleep 120
            } else if (err.message?.contains('Failed to kill container')) { // 容器退出后自动清理失败（Jenkins 报 kill container failed）时跳过
                echo " 容器清理失败，跳过：${err.message}"
            } else {
                if (attempt >= maxRetries) {
                    echo "第 ${attempt} 次 Docker 容器启动失败：${err.message}"
                    echo "Docker 容器启动失败：${err.message}, 不再重试，请检查server状态！！！！！！！！！！！！！！！！"
                }
                // 其它报错继续抛出，不隐藏
                throw err
            }
        }
    }

    sh """
        docker rm -f `docker ps -a | awk '/Exited|Create|Removal/ {print \$NF}'`  2>/dev/null ||:
        docker rmi `docker images | grep none | awk '{print \$3}'` ||:
    """
// oss.install()
// oss.cp('pkg/*.tar.gz', "sh-moss/sw-build/computeQA/tmp/test.vps_ph1s/${env.BUILD_ID}/")
}

def runCtsTest() {
    def qemuModel = env.AmodlePackageUrl ? 'amodel' : 'cmodel'
    def sshPort = "1${('111' + env.BUILD_ID)[-4..-1]}"
    def mttraceTestExport = env.mttraceTestExport == 'true' ? 'export MTTRACE_ENABLE=1' : 'export MTTRACE_ENABLE=0'
    def modelPath = env.AmodlePackageUrl ? "-aml ${env.AmodlePackageUrl}" : "-cml ${env.CmodlePackageUrl}"
    sh """
        ${mttraceTestExport}
        export TEST_TYPE=${env.testType}
        export MAX_TIME_LIMIT=`echo "1800" | bc -q`
        . /home/<USER>/miniforge/etc/profile.d/conda.sh && conda activate mathx
        cd ${env.WORKSPACE}/musa_cts/vps_test
        python -u gen_file_for_ATF_ph1s.py -qm ${qemuModel} -wd ${env.WORKSPACE} -crd allure_result_vps_musa -qid ${env.WORKSPACE} -qi test_compute_v3_no_cuda.img -dia \"${env.driverInstallArgs}\" -md ${env.WORKSPACE}/soc_model -mlcf ${env.WORKSPACE}/mtcc_test/vps_test/test_cfg_for_ATF_mtcc.csv -tga ${env.gpuArch} -mlf 0  -pif 0 -optf 0 -tcf test_cfg_for_ATF_ph1s.csv -vn vps_test_musa_ph1s_img_${env.BUILD_ID} -vp ${sshPort} ${env.runnerPythonCmd} ${modelPath} ||:
    """

    def resultsDirMusaCts = "${env.WORKSPACE}/allure_result_vps_musa"
    def reportNameMusaCts = "ph1s_${qemuModel}_vps_allure_report"
    if (fileExists(resultsDirMusaCts)) {
        // 检查allure结果目录中是否包含axpy测试用例的日志
        def axpyExecuted = sh(
            script: "find ${resultsDirMusaCts} -name '*.json' -exec grep -l 'axpy' {} \\;",
            returnStatus: true
        ) == 0

        if (axpyExecuted) {
            commonLib.allure(resultsDirMusaCts, reportNameMusaCts)
        } else {
            error 'Test failed: axpy test case not found in test results. No valid test execution.'
        }
    } else {
        echo "Warning: ${resultsDirMusaCts} not found"
    }
}

runner.start(env.runChoice, {
    def workflow = ['checkout': [closure: { fetchCode() }]]
    if (env.gitlabTargetRepoName == 'linux-ddk' && env.testLabel =~ 'mttrace') {
        workflow['build diagsys'] = [closure: { buildDiagsys() }]
    }
    // workflow['setup on node'] = [closure: { setUpOnNode() }]
    workflow['setup in docker'] = [closure: { compileMusaAndCases() }]
    workflow['run cts test'] = [closure: { runCtsTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    runPipeline(workflow)
})
