@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

repo = 'mt-pfm-controller'
env.packageName = ''
env.mountParms = env.mountParms ?: '-v /usr/local/musa:/usr/local/musa -v /usr/lib/x86_64-linux-gnu/libdrm_mtgpu.so:/usr/lib/x86_64-linux-gnu/libdrm_mtgpu.so'

def fetchCode() {
    env.branch = env.gitlabSourceRepoName == repo ? env.branch : (env.pfmBranch ?: 'develop')
    env.commitId = new git().fetchCode(repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def setUpDepends() {
    sh '''
        apt update ||:
        apt install cmake ||:
        apt install g++-12 -y ||:
    '''
}

def test() {
    oss.install()
    dir(repo) {
        credentials.runWithCredential('SSH_GITLAB') {
            timeout(time: env.timeout ?: 15, unit: 'MINUTES') {
                sh """
                    cd test/pfm_dump
                    mc cp sh-moss/sw-build/computeQA/cuda_compatible/newest/master/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz .
                    tar xzf mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz
                    ./install.sh
                    export PATH=/usr/local/musa/bin:\$PATH
                    export LD_LIBRARY_PATH=/usr/local/musa/lib/:\$LD_LIBRARY_PATH
                    mc cp sh-moss/sw-build/computeQA/vps/demo/test_axpy/axpy.cu .
                    mcc axpy.cu -o axpy -lmusart -lmusa -mtgpu --cuda-gpu-arch=mp_21 --cuda-gpu-arch=mp_22 --cuda-gpu-arch=mp_31
                    cd ../..
                    ${env.cmd}
                """
            }
        }
    }
}

def setUpOnNode() {
    ddk.installLinuxDdkAndSetup()
    if (utils.isEmpty(env.musaRuntimePackageUrl)) {
        env.musaRuntimePackageUrl = env.musaRuntimeBranch ? constants.genLatestPackageUrl('MUSA-Runtime', env.musaRuntimeBranch, 'musaRuntime.tar.gz') : ''
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
}

runner.start(env.runChoice, [pre: {
    runPipeline([
        'setup on node': [closure: { setUpOnNode() }],
    ], [disablePost: true])
}, main: {
    def workflow = [
        'checkout': [ closure: { fetchCode() } ],
        'installDepends': [ closure: { setUpDepends() } ],
        'test': [ closure: { test() }, setGitlabStatus: true, statusName: "${env.testLabel}" ]
    ]

    runPipeline(workflow, [disablePre: true])
}])
