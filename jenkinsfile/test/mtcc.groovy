@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// nodeLabel runChoice containerImage podNodeSelector podResources cluster branch commitId testType testArgs enableAssemble enableMtcclit umdPackageUrl mtccPackageUrl mtccSrcUrl musaRuntimePackageUrl musaToolkitsPackageUrl musifyPackageUrl allurePackageUrl generateTestUrl

env.repo = 'mtcc_test'
generateTestUrl = env.generateTestUrl ? env.generateTestUrl.split(';') : []
MUSA_TOOLKIT_REPO = 'musa_toolkit'
MTCC_LIT_DIR = 'mtcc_lit_test'
MU_ALG_DIR = 'mu_alg'
MU_THRUST_DIR = 'mu_thrust'

def fetchCode() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://10.18.33.18:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.trim() ? env.linuxDdkPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.trim() ? env.musaToolkitsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.trim() ? env.musaToolkitsSrcPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.mtccPackageUrl = env.mtccPackageUrl?.trim() ? env.mtccPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.anacondaPackageUrl = env.anacondaPackageUrl?.trim() ? env.anacondaPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musifyPackageUrl = env.musifyPackageUrl?.trim() ? env.musifyPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.trim() ? env.musaRuntimePackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgPackageUrl = env.muAlgPackageUrl?.trim() ? env.muAlgPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muThrustPackageUrl = env.muThrustPackageUrl?.trim() ? env.muThrustPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
        env.muAlgCtsPackageUrl = env.muAlgCtsPackageUrl?.trim() ? env.muAlgCtsPackageUrl.replace('https://sh-moss.mthreads.com', endpoint) : ''
    }
    gitLib.fetchCode(MUSA_TOOLKIT_REPO, 'master', null, [disableSubmodules: true])
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
}

def testSetup() {
    if (env.umdPackageUrl) {
        ddk.installUmd()
    }
    if (env.allurePackageUrl) {
        constants.downloadAndUnzipPackage(env.allurePackageUrl)
    }
    sh 'rm -fr /usr/local/musa*'
    if (env.musaToolkitsPackageUrl) {
        constants.downloadAndUnzipPackage(env.musaToolkitsPackageUrl)
        sh 'cd ./musa_toolkits_install && ./install.sh'
    } else {
        constants.downloadAndUnzipPackage(env.mtccPackageUrl)
        constants.downloadAndUnzipPackage(env.musaRuntimePackageUrl)
        sh '''
            cd ./MUSA-Runtime && ./install.sh
            cd .. && ./install.sh
        '''
        dir(MUSA_TOOLKIT_REPO) {
            sh 'cp -r cmake /usr/local/musa/'
        }
    }
    if (env.musifyPackageUrl) {
        constants.downloadAndUnzipPackage(env.musifyPackageUrl)
    }
    if (env.mtccSrcUrl) {
        constants.downloadAndUnzipPackage(env.mtccSrcUrl)
        sh """
            mkdir -p ${MTCC_LIT_DIR}
            tar -xzf mtcc_src.tar.gz -C ${MTCC_LIT_DIR}
            cp -r /usr/local/musa/* ${MTCC_LIT_DIR}/mtcc/build
        """
    }
    sh """
        mkdir -p ${MU_ALG_DIR}
        wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/computeQA/bottom_ci_rely_pkg/mualg.tar -O mualg.tar
        tar -xf mualg.tar -C ${MU_ALG_DIR}
        cd ${MU_ALG_DIR}/package
        ls *.deb | xargs -n1 dpkg -i
    """
    sh """
        mkdir -p ${MU_THRUST_DIR}
        wget -q --no-check-certificate https://sh-moss.mthreads.com/sw-build/computeQA/bottom_ci_rely_pkg/muthrust.tar -O muthrust.tar
        tar -xf muthrust.tar -C ${MU_THRUST_DIR}
        cd ${MU_THRUST_DIR}/package
        ls *.deb | xargs -n1 dpkg -i
    """
    sh """
        pip3 install pyahocorasick==1.4.4 -i https://pypi.tuna.tsinghua.edu.cn/simple
        pip3 install allure-pytest -i https://mirrors.aliyun.com/pypi/simple/
        pip3 install ahocorapy -i https://mirrors.aliyun.com/pypi/simple/
        tar -xf musify.tar ||:
        cd ${env.repo}/tools
        export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
        [ -e /usr/local/musa/tools/musify* ] && unset MUSA_PORTING_PATH
        ./porting_for_ci.sh ${MTCC_LIT_DIR}/mtcc
    """
}

def runTest() {
    timeout(180) {
        dir(env.repo) {
            sh """
                cd pytest

                python3 run_lit.py ${env.testArgs}
            """
            if (env.enableMtcclit == 'true') {
                sh """
                    cd pytest
                    python3 run_lit.py --mtcc_dir_path ${MTCC_LIT_DIR}/mtcc
                """
            }
            if (env.enableAssemble == 'true') {
                sh """
                    cd pytest
                    python3 run_lit.py ${env.testArgs} --type=assemble
                """
            }
            sh """
                cd pytest
                pytest mtcc_lit_report.py --alluredir=${env.WORKSPACE}/${env.repo}/pytest/mtcc_test_allure_result ||:
            """
        }
    }
    dir(env.repo) {
        sh '''
            cd pytest
            tar -czvf mtcc_test_allure_result.tar.gz mtcc_test_allure_result
        '''
    }
    gcov = false
    if (sh(script: 'find . -name \'*.gcda\'', returnStdout: true)) {
        gcov = true
        sh '''
            tar -czvf mtcc_test_gcov_gcda.tar.gz `find . -name *.gcda`
            find . -name *.gcda | wc -l
        '''
    }
    if (env.debugTime) {
        sleep env.debugTime
    }
}

def checkResult() {
    dir(env.repo) {
        commonLib.allure('pytest/mtcc_test_allure_result')
    }
}

def uploadTestResult() {
    oss.install()
    generateTestUrl.each {
        oss.cp("./${env.repo}/pytest/mtcc_test_allure_result.tar.gz", it)
        currentBuild.description += "<br>generate_pkg:${it}/mtcc_test_allure_result.tar.gz".replaceAll('//', '/')
        if (gcov) {
            oss.cp('./mtcc_test_gcov_gcda.tar.gz', it)
            currentBuild.description += "<br>generate_pkg:${it}/mtcc_test_gcov_gcda.tar.gz".replaceAll('//', '/')
        }
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'test setup': [closure: { testSetup() }],
        'mtcc test': [closure: { runTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ], [disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() } ]
    ], [disablePre: true])
}])
