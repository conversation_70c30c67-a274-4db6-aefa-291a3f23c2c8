@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

//para: gpuType, dockerImage，runChoice，linuxDdkBranch，linuxDdkPackageUrl，mtmlBranch，commit，exclude_cases，mailReceiver(run自带发送邮件功能),gmi_case_file,testMtml,testGmi

//install ddk2.0
def installDriver() {
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
}

//mtml test
def mtmlTest() {
    oss.install()
    def latestManagement = sh(script: "curl --insecure https://sh-moss.mthreads.com/sw-build/management/${env.mtmlBranch}/latest.txt", returnStdout: true).trim()
    currentBuild.description += "mtml: ${latestManagement} <br>"
    constants.downloadAndUnzipPackage(latestManagement)

    env.gfxCommitId = gitLib.fetchCode('gfxswtest', 'sdk_test', env.gfxCommitId)
    dir('gfxswtest/modules/sdk/mtml') {
        sh """
            export LD_LIBRARY_PATH=${env.WORKSPACE}/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/lib/:\$LD_LIBRARY_PATH
            export CPLUS_INCLUDE_PATH=${env.WORKSPACE}/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/include/:\$CPLUS_INCLUDE_PATH
            cmake -B build . -DBUILD_MTML=OFF -DDYNAMIC=ON -DBUILD_VERSION=0.1.0
            cmake --build build
        """
        filter_info = ''
        card = env.gpuType
        if (env.exclude_cases) {
            print(env.exclude_cases)
            filter_info = '--gtest_filter=-'
            for (line in env.exclude_cases.split(',')) {
                line = line.trim()
                filter_info += ":${line}"
            }
        }
        sh """
            export LD_LIBRARY_PATH=${env.WORKSPACE}/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/lib/:\$LD_LIBRARY_PATH
            export CPLUS_INCLUDE_PATH=${env.WORKSPACE}/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/include/:\$CPLUS_INCLUDE_PATH
            ./build/sdk_gtest ${card} ${filter_info}
        """
    }
}

//gmi test
def gmiTest() {
    def hostIp = commonLib.getNodeIP()
    env.commitId = gitLib.fetchCode('qa_sdk', 'develop', env.commitId)
    dir('qa_sdk/gmi') {
        sh """
            pip3 install -r requirements.txt
            cd test_cases
            pytest ${env.gmi_case_file} --host=${hostIp} --username=root --password=Passw0rd!
        """
    }
}

runner.start(env.runChoice) {
    def workflow = [:]
    if (env.gpuType == 'S80') {
        workflow['install driver'] = [closure: { installDriver() }]
    }
    if (env.testMtml == 'true') {
        workflow['test mtml'] = [closure: { mtmlTest() }]
    }
    if (env.testGmi == 'true') {
        workflow['test gmi'] = [closure: { gmiTest() }]
    }

    runPipeline(workflow)
}
