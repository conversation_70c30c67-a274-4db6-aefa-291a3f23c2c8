build_1st:
# 140 cpu
  - job: "build.all_in_one"
    name: "musa_asm"
    parameters:
      targetRepo: "musa_asm"
      variant: "master"
      packageName: "musa_asm.tar.gz"
      podResources: "requests=cpu=20;requests=memory=50Gi;limits=cpu=20;limits=memory=50Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "linux-ddk_deb"
    parameters:
      targetRepo: "musa_package"
      variant: "master_deb"
      packageName: ""  # todo
      podResources: "requests=cpu=50;requests=memory=256Gi;limits=cpu=50;limits=memory=256Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "linux-ddk_rpm"
    parameters:
      targetRepo: "musa_package"
      variant: "master_rpm"
      packageName: ""  # todo
      podResources: "requests=cpu=50;requests=memory=256Gi;limits=cpu=50;limits=memory=256Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mtml"
    parameters:
      targetRepo: "mtml"
      variant: "master"
      packageName: "mtml_2.2.0-linux-R_aarch64"  # todo 不要带后缀，会上传 packageName.deb 和 packageName.rpm
      podResources: "requests=cpu=20;requests=memory=50Gi;limits=cpu=20;limits=memory=50Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"

build_2nd:
# 350 cpu
  - job: "build.all_in_one"
    name: "musa_toolkit"
    parameters:
      targetRepo: "musa_toolkit"
      variant: "master"
      packageName: "musa_toolkits_install_full.tar.gz"
      podResources: "requests=cpu=50;requests=memory=256Gi;limits=cpu=50;limits=memory=256Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "musa_toolkit_gcov"
    parameters:
      targetRepo: "musa_toolkit"
      variant: "master_gcov"
      packageName: "musa_toolkits_install_full_gcov.tar.gz"
      podResources: "requests=cpu=50;requests=memory=256Gi;limits=cpu=50;limits=memory=256Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "musa_toolkit_asan"
    parameters:
      targetRepo: "musa_toolkit"
      variant: "master_asan"
      packageName: "musa_toolkits_install_full_asan.tar.gz"
      podResources: "requests=cpu=50;requests=memory=256Gi;limits=cpu=50;limits=memory=256Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mtcc"
    parameters:
      targetRepo: "mtcc"
      variant: "master"
      packageName: "mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
      podResources: "requests=cpu=50;requests=memory=256Gi;limits=cpu=50;limits=memory=256Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mtcc_gcov"
    parameters:
      targetRepo: "mtcc"
      variant: "master_gcov"
      packageName: "mtcc-x86_64-linux-gnu-ubuntu_gcov.tar.gz"
      podResources: "requests=cpu=50;requests=memory=256Gi;limits=cpu=50;limits=memory=256Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: " MUSA-Runtime"
    parameters:
      targetRepo: "MUSA-Runtime"
      variant: "master"
      packageName: "musaRuntime.tar.gz"
      podResources: "requests=cpu=20;requests=memory=50Gi;limits=cpu=20;limits=memory=50Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "triton_musa_py310"
    parameters:
      targetRepo: "triton_musa"
      variant: "main_310"
      packageName: "triton_py310.tar.gz"
      podResources: "requests=cpu=40;requests=memory=200Gi;limits=cpu=40;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "triton_musa_py311"
    parameters:
      targetRepo: "triton_musa"
      variant: "main_311"
      packageName: "triton_py311.tar.gz"
      podResources: "requests=cpu=40;requests=memory=200Gi;limits=cpu=40;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"

build_3rd:
# 140 cpu
  - job: "build.all_in_one"
    name: "mudnn_ph1"
    parameters:
      targetRepo: "mudnn"
      variant: "develop_ph1"
      packageName: "mudnn.PH1.tar.gz"
      podResources: "requests=cpu=60;requests=memory=200Gi;limits=cpu=60;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mudnn_qy2"
    parameters:
      targetRepo: "mudnn"
      variant: "develop_qy2"
      packageName: "mudnn.QY2.tar.gz"
      podResources: "requests=cpu=40;requests=memory=100Gi;limits=cpu=40;limits=memory=100Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mccl_ph1"
    parameters:
      targetRepo: "mccl"
      variant: "master_ph1"
      packageName: "mccl.PH1.tar.gz"
      podResources: "requests=cpu=20;requests=memory=100Gi;limits=cpu=20;limits=memory=100Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mccl_qy2"
    parameters:
      targetRepo: "mccl"
      variant: "develop_qy2"
      packageName: "mccl.QY2.tar.gz"
      podResources: "requests=cpu=20;requests=memory=100Gi;limits=cpu=20;limits=memory=100Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"

build_others:
# 240 cpu
  - job: "build.all_in_one"
    name: "mudnn_ph1_perf"
    parameters:
      targetRepo: "mudnn"
      variant: "develop_ph1_perf"
      packageName: "mudnn.PH1.profiling.tar.gz"
      podResources: "requests=cpu=60;requests=memory=200Gi;limits=cpu=60;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mudnn_qy2_perf"
    parameters:
      targetRepo: "mudnn"
      variant: "develop_qy2_perf"
      packageName: "mudnn.QY2.profiling.tar.gz"
      podResources: "requests=cpu=40;requests=memory=100Gi;limits=cpu=40;limits=memory=100Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mudnn_ph1_gcov"
    parameters:
      targetRepo: "mudnn"
      variant: "develop_ph1_gcov"
      packageName: "mudnn_gcov.PH1.tar.gz"
      podResources: "requests=cpu=60;requests=memory=200Gi;limits=cpu=60;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mudnn_qy2_gcov"
    parameters:
      targetRepo: "mudnn"
      variant: "develop_qy2_gcov"
      packageName: "mudnn_gcov.QY2.tar.gz"
      podResources: "requests=cpu=40;requests=memory=100Gi;limits=cpu=40;limits=memory=100Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mtshmem_ph1"
    parameters:
      targetRepo: "mtshmem"
      variant: "develop"
      packageName: "mtshmem.PH1.tar.gz"
      podResources: "requests=cpu=10;requests=memory=50Gi;limits=cpu=10;limits=memory=50Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "msight-compute-internal"
    parameters:
      targetRepo: "msight-compute"
      variant: "develop_internal"
      podResources: "requests=cpu=10;requests=memory=50Gi;limits=cpu=10;limits=memory=50Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "msight-compute-external"
    parameters:
      targetRepo: "msight-compute"
      variant: "develop_external"
      podResources: "requests=cpu=10;requests=memory=50Gi;limits=cpu=10;limits=memory=50Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "mt-dcgm"
    parameters:
      targetRepo: "mt-dcgm"
      variant: "develop"
      podResources: "requests=cpu=10;requests=memory=50Gi;limits=cpu=10;limits=memory=50Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "MUPTI"
    parameters:
      targetRepo: "MUPTI"
      variant: "master"
      podResources: "requests=cpu=20;requests=memory=100Gi;limits=cpu=20;limits=memory=100Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"

build_4th:
# 120 cpu
  - job: "build.all_in_one"
    name: "torch_musa_ph1"
    parameters:
      targetRepo: "torch_musa"
      variant: "release_musa_4.3.0_ph1"
      packageName: ""
      podResources: "requests=cpu=60;requests=memory=200Gi;limits=cpu=60;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"
  - job: "build.all_in_one"
    name: "torch_musa_qy2"
    parameters:
      targetRepo: "torch_musa"
      variant: "release_musa_4.3.0_qy2"
      packageName: ""
      podResources: "requests=cpu=60;requests=memory=200Gi;limits=cpu=60;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/os-build/mt-build:develop"

deploys:
  - job: "test.operatorinstall"
    name: "deployFarm by operator"

tests:
# s5000 test
  - job: "daily.musa_cts"
    name: "musa_cts.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
# ddk 使用 master daily ddk, toolkit 使用 master stable toolkit 
  - job: "daily.musa_cts"
    name: "stable.musa_cts.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/musa_toolkits_install_full.tar.gz"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s5000"
    parameters:
      pythonVersion: "py310"
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_perf"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
      torchPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s5000/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s5000/torch_musa-2.5.0-cp310-cp310-linux_x86_64.whl"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s5000"
    parameters:
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=31"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.daily"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.weekly_2case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'bsrmm or spsm_csr'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.weekly_34case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'not (bsrmm or spsm_csr)'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muPP_cts"
    name: "test.muPP_cts.s5000"
    parameters:
      testType: "daily"
      testArgs: "-m 'rc'"
      containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSOLVER_cts"
    name: "test.muSOLVER_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muAlg_cts"
    name: "test.muAlg_cts.s5000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=31"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muThrust_cts"
    name: "test.muThrust_cts.s5000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=31"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.mtcc"
    name: "test.mtcc.s5000"
    parameters:
      testType: "daily"
      testArgs: "--device=ph1 --tag=daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.s5000"
    parameters:
        mudnnBranch: "develop"
        test_type: "daily"
        test_mark: "mudnn"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.s5000"
    parameters:
        mudnnBranch: "develop"
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000_perf"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mutlass"
    name: "test.mutlass.s5000"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=30;limits=cpu=30;requests=memory=50Gi;limits=memory=50Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mupti_cts"
    name: "test.mupti_cts.s5000"
    parameters:
        ctsBranch: "master"
        mtgpuArch: "mp_31"
        testType: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"
  - job: "CI_MTML_TEST"
    name: "test_mtml.s5000"
    parameters:
      mtmlBranch: "release_2.0"
      gpuType: "S5000"
      testMtml: "true"
      testGmi: "false"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v11"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
      exclude_cases: "DeviceGetPciInfoTest.baseGetPci,VpuGetDecoderSessionMetricsTest.getSessionMetricsWithFfmpeg,
                      VpuGetUtilizationTest.withFfmpegDecoding,DeviceGetPowerUsageTest.*,MemoryGetVendorTest.*,
                      GpuGetMaxClockTest.baseGetMaxClock,DeviceGetSerialNumberTest*,DeviceResetTest*,DeviceGetNameTest.validData,
                      DeviceInitByPciSbdfTest*"
  - job: "CI_msys_cli_test"
    name: "test_msys.s5000"
    parameters:
      msysBranch: "release-1.4"
      gpuType: "S5000"
      testType: "compute"
      benchmark: "--compute"
      msys_cases: '-k "not graphics"'
      containerImage: "sh-harbor.mthreads.com/qa/musa_test-ubuntu-22-04:v2"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
  - job: "test.musa_perf_daily"
    name: "test.musa_perf_daily.s5000"
    parameters:
      runChoice: "pod"
      testMark: "musa_benchmarks_perf_m3d"
      musa_benchmark_cts_branch: "m3d_master"
      testType: "daily"
      podNodeSelector: "In=GPU_TYPE=s5000_perf"
      podResources: "requests=cpu=10;requests=memory=20Gi;requests=mthreads.com/gpu=8;limits=cpu=10;limits=memory=62Gi;limits=mthreads.com/gpu=8;"
  - job: "test.triton_cts"
    name: "test.triton_cts.s5000"
    parameters:
      pythonVersion: "py310"
      torchPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s5000/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s5000/torch_musa-2.5.0-cp310-cp310-linux_x86_64.whl"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=cpu=10;requests=memory=25Gi;limits=cpu=10;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:${LD_LIBRARY_PATH};TEST_TYPE=$testType;"

# s4000 test
  - job: "daily.musa_cts"
    name: "musa_cts.s4000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
# ddk 使用 master daily ddk, toolkit 使用 master stable toolkit 
  - job: "daily.musa_cts"
    name: "stable.musa_cts.s4000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/musa_toolkits_install_full.tar.gz"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s4000"
    parameters:
      pythonVersion: "py310"
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_perf"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
      torchPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s4000/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s4000/torch_musa-2.5.0-cp310-cp310-linux_x86_64.whl"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s4000"
    parameters:
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=22"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.daily"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.weekly_2case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'bsrmm or spsm_csr'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.weekly_34case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'not (bsrmm or spsm_csr)'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muPP_cts"
    name: "test.muPP_cts.s4000"
    parameters:
      testType: "daily"
      testArgs: "-m 'rc'"
      containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSOLVER_cts"
    name: "test.muSOLVER_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muAlg_cts"
    name: "test.muAlg_cts.s4000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=22"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muThrust_cts"
    name: "test.muThrust_cts.s4000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=22"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.mtcc"
    name: "test.mtcc.s4000"
    parameters:
      testType: "daily"
      testArgs: "--device=quyuan2 --tag=daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s4000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.s4000"
    parameters:
        mudnnBranch: "develop"
        test_type: "daily"
        test_mark: "mudnn"
        timeoutFactor: "4"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.s4000"
    parameters:
        mudnnBranch: "develop"
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_perf"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mutlass"
    name: "test.mutlass.s4000"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=30;limits=cpu=30;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mutlass_benchmark"
    name: "test.mutlass_benchmark.s4000"
    parameters:
        mutlass_branch: "develop"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_perf"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=40;limits=cpu=40;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mupti_cts"
    name: "test.mupti_cts.s4000"
    parameters:
        ctsBranch: "master"
        mtgpuArch: "mp_22"
        testType: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"
  - job: "test.musa_perf_daily"
    name: "test.musa_perf_daily.s4000"
    parameters:
      runChoice: "pod"
      testMark: "musa_benchmarks_perf_m3d"
      musa_benchmark_cts_branch: "m3d_master"
      testType: "daily"
      podNodeSelector: "In=GPU_TYPE=s4000_perf"
      podResources: "requests=cpu=10;requests=memory=20Gi;requests=mthreads.com/gpu=8;limits=cpu=10;limits=memory=62Gi;limits=mthreads.com/gpu=8;"
  - job: "test.triton_cts"
    name: "test.triton_cts.s4000"
    parameters:
      pythonVersion: "py310"
      torchPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s4000/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s4000/torch_musa-2.5.0-cp310-cp310-linux_x86_64.whl"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=cpu=10;requests=memory=25Gi;limits=cpu=10;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:${LD_LIBRARY_PATH};TEST_TYPE=$testType;"

# s80 test
  # - job: "daily.musa_cts"
  #   name: "musa_cts.s80"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
  #     test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "stable.musa_cts.s80"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
  #     test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/musa_toolkits_install_full.tar.gz"
  # - job: "test.muBLAS_cts"
  #   name: "test.muBLAS_cts.s80"
  #   parameters:
  #     compileArgs: "-DTEST_MUBLAS_LT=ON"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muFFT_cts"
  #   name: "test.muFFT_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muRAND_cts"
  #   name: "test.muRAND_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.daily"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.weekly_2case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'bsrmm or spsm_csr'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.weekly_34case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'not (bsrmm or spsm_csr)'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muPP_cts"
  #   name: "test.muPP_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "-m 'rc'"
  #     containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSOLVER_cts"
  #   name: "test.muSOLVER_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muAlg_cts"
  #   name: "test.muAlg_cts.s80"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muThrust_cts"
  #   name: "test.muThrust_cts.s80"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.mtcc"
  #   name: "test.mtcc.s80"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "--device=quyuan1 --tag=daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "daily.musa_cts.mtcc.s80"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d"
  #     compileArgs: ""
  #     test_cmd: "pytest -v -m 'musa_mtcc'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mudnn_cts"
  #   name: "test.mudnn_cts.s80"
  #   parameters:
  #       mudnnBranch: "develop"
  #       test_type: "daily"
  #       test_mark: "mudnn"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s80"
  #       podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mupti_cts"
  #   name: "test.mupti_cts.s80"
  #   parameters:
  #       ctsBranch: "master"
  #       mtgpuArch: "mp_21"
  #       testType: "daily"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s80"
  #       podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"

# 海光 s4000 test
  # - job: "daily.musa_cts"
  #   name: "musa_cts.s4000_hygon"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
  #     test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
  #     gpuArch: "mp_22"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  # - job: "test.muBLAS_cts"
  #   name: "test.muBLAS_cts.s4000_hygon"
  #   parameters:
  #     compileArgs: "-DTEST_MUBLAS_LT=ON"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muRAND_cts"
  #   name: "test.muRAND_cts.s4000_hygon"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     compileArgs: "-DMUSA_ARCHS=22"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s4000_hygon.daily"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muSOLVER_cts"
  #   name: "test.muSOLVER_cts.s4000_hygon"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mtcc"
  #   name: "test.mtcc.s4000_hygon"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "--device=quyuan2 --tag=daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "daily.musa_cts.mtcc.s4000_hygon"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d"
  #     compileArgs: ""
  #     test_cmd: "pytest -v -m 'musa_mtcc'"
  #     gpuArch: "mp_22"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mudnn_cts"
  #   name: "test.mudnn_cts.s4000_hygon"
  #   parameters:
  #       mudnnBranch: "develop"
  #       test_type: "daily"
  #       test_mark: "mudnn"
  #       timeoutFactor: "4"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #       podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mupti_cts"
  #   name: "test.mupti_cts.s4000_hygon"
  #   parameters:
  #       ctsBranch: "master"
  #       mtgpuArch: "mp_22"
  #       testType: "daily"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #       podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"

# 兼容性测试 ddk release_musa_4.3.1 其他包使用daily build
  - job: "test.mtcc"
    name: "release_musa_4.3.1_test.mtcc.s5000"
    parameters:
      testType: "daily"
      testArgs: "--device=ph1 --tag=daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_release_musa_4.3.1"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "release_musa_4.3.1_daily.musa_cts.mtcc.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_release_musa_4.3.1"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.muBLAS_cts"
    name: "release_musa_4.3.1_test.muBLAS_cts.s5000"
    parameters:
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_release_musa_4.3.1"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muRAND_cts"
    name: "release_musa_4.3.1_test.muRAND_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=31"
      podNodeSelector: "In=GPU_TYPE=s5000_release_musa_4.3.1"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSOLVER_cts"
    name: "release_musa_4.3.1_test.muSOLVER_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_release_musa_4.3.1"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.triton_cts"
    name: "release_musa_4.3.1_test.triton_cts.s5000"
    parameters:
      pythonVersion: "py310"
      torchPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s5000/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/ai_pkgs/torch/master/s5000/torch_musa-2.5.0-cp310-cp310-linux_x86_64.whl"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_release_musa_4.3.1"
      podResources: "requests=cpu=10;requests=memory=25Gi;limits=cpu=10;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:${LD_LIBRARY_PATH};TEST_TYPE=$testType;"
  - job: "daily.musa_cts"
    name: "release_musa_4.3.1_musa_cts.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_release_musa_4.3.1"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"

# 异常 test
tests_exception:
  - job: "daily.musa_cts"
    name: "musa_exception_test.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      test_cmd: "pytest -v . -m 'MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
