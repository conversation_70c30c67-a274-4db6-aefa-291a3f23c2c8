build_toolkits:
  - job: "build.musa_toolkit"
    name: "musa_toolkit"
    parameters:
      setupDDK: "false"
      musa_toolkit_branch: "master"
      musart_branch: "master"
      mtcc_branch: "master"
      musify_branch: "master"
      mublas_branch: "develop"
      mublaslt_branch: "develop"
      mufft_branch: "develop"
      mupp_branch: "develop"
      murand_branch: "develop"
      mtjpeg_branch: "master"
      musparse_branch: "develop"
      musolver_branch: "develop"
      mupti_branch: "master"
      compileTargetArch: "x86_64"
      makecmds_musatoolkit: "--build_all --build_archs=21,22,31 --build_mupti --build_mtjpeg --ddk_backend=m3d -j128"
      podResources: "requests=cpu=50;requests=memory=200Gi;limits=cpu=50;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
      rename_pkg_musa_toolkits: "musa_toolkits_install_full.tar.gz"
  - job: "build.musa_toolkit"
    name: "musa_toolkit_gcov"
    parameters:
      setupDDK: "false"
      musa_toolkit_branch: "master"
      musart_branch: "master"
      mtcc_branch: "master"
      musify_branch: "master"
      mublas_branch: "develop"
      mufft_branch: "develop"
      mupp_branch: "develop"
      murand_branch: "develop"
      mtjpeg_branch: "master"
      musparse_branch: "develop"
      musolver_branch: "develop"
      mupti_branch: "master"
      compileTargetArch: "x86_64"
      makecmds_musatoolkit: "--build_all --build_archs=21,22,31 --build_mupti --build_mtjpeg --ddk_backend=m3d -j128 --enable_gcov"
      podResources: "requests=cpu=50;requests=memory=200Gi;limits=cpu=50;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
      rename_pkg_musa_toolkits: "musa_toolkits_install_full_gcov.tar.gz"
  - job: "build.musa_toolkit"
    name: "musa_toolkit_asan"
    parameters:
      setupDDK: "false"
      musa_toolkit_branch: "master"
      musart_branch: "master"
      mtcc_branch: "master"
      musify_branch: "master"
      mublas_branch: "develop"
      mufft_branch: "develop"
      mupp_branch: "develop"
      murand_branch: "develop"
      mtjpeg_branch: "master"
      musparse_branch: "develop"
      musolver_branch: "develop"
      mupti_branch: "master"
      compileTargetArch: "x86_64"
      makecmds_musatoolkit: "--build_all --build_archs=21,22,31 --build_mupti --build_mtjpeg --enable_asan --ddk_backend=m3d -j128"
      podResources: "requests=cpu=50;requests=memory=200Gi;limits=cpu=50;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
      rename_pkg_musa_toolkits: "musa_toolkits_install_full_asan.tar.gz"
  - job: "build.mtcc"
    name: "mtcc"
    parameters:
      branch: "master"
      compileArgs: "--build_type release --enable_hg"
      compileParallel: "48"
      rename_pkg: "mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
      podResources: "requests=cpu=50;requests=memory=200Gi;limits=cpu=50;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
  - job: "build.mtcc"
    name: "mtcc_gcov"
    parameters:
      branch: "master"
      exports: "export GCOV_TEST=ON"
      compileArgs: "--build_type release --enable_hg"
      compileParallel: "48"
      rename_pkg: "mtcc-x86_64-linux-gnu-ubuntu_gcov.tar.gz"
      podResources: "requests=cpu=50;requests=memory=200Gi;limits=cpu=50;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"

build_modules:
  - job: "build.triton_musa"
    name: "triton_musa_py310"
    parameters:
      branch: "main"
      packageName: "triton_py310.tar.gz"
      podResources: "requests=cpu=20;requests=memory=40Gi;limits=cpu=20;limits=memory=64Gi"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      parallelJob: 16
      cmd:
        - TRITON_WHEEL_VERSION_SUFFIX=+git`git rev-parse --short HEAD`
        - conda init
        - source ~/.bashrc
        - source /root/miniforge3/etc/profile.d/conda.sh
        - conda activate py310
        - clang -v
        - llvm-config --version
        - python --version
        - which python
      buildScripts:
        - cd python
        - export MAX_JOBS=16
        - python setup.py bdist_wheel -d bdist_wheel/
        - tar -czf triton_py310.tar.gz ./bdist_wheel
      testUtScipts:
        - cd python
        - pip install bdist_wheel/triton-*.whl
        - cd build/cmake.linux*
        - ninja test
        - pip install lit
        - lit test
  - job: "build.triton_musa"
    name: "triton_musa_py311"
    parameters:
      branch: "main"
      packageName: "triton_py311.tar.gz"
      podResources: "requests=cpu=20;requests=memory=40Gi;limits=cpu=20;limits=memory=64Gi"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      parallelJob: 16
      cmd:
        - TRITON_WHEEL_VERSION_SUFFIX=+git`git rev-parse --short HEAD`
        - conda init
        - source ~/.bashrc
        - source /root/miniforge3/etc/profile.d/conda.sh
        - conda activate py311
        - clang -v
        - llvm-config --version
        - python --version
        - which python
      buildScripts:
        - cd python
        - export MAX_JOBS=16
        - python setup.py bdist_wheel -d bdist_wheel/
        - tar -czf triton_py311.tar.gz ./bdist_wheel
      testUtScipts:
        - cd python
        - pip install bdist_wheel/triton-*.whl
        - cd build/cmake.linux*
        - ninja test
        - pip install lit
        - lit test
  - job: "build.mudnn"
    name: "mudnn_ph1"
    parameters:
      branch: "develop"
      packageName: "mudnn.PH1.tar.gz"
      cmd: "-m mp_31 -s"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2"
    parameters:
      branch: "develop"
      packageName: "mudnn.QY2.tar.gz"
      cmd: "-m mp_22 -s"
      podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  # - job: "build.mudnn"
  #   name: "mudnn_qy1"
  #   parameters:
  #     branch: "develop"
  #     packageName: "mudnn.QY1.tar.gz"
  #     cmd: "-m mp_21 -s"
  #     podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  - job: "build.mccl"
    name: "mccl_ph1"
    parameters:
      mcclBranch: "master"
      packageName: "mccl.PH1.tar.gz"
      build_args: "-lbpm -a 31  -q 'TRACE=1' -x '/usr/local/openmpi/'"
      use_build_scripts: 'true'
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-mccl-build-image:v0"
      exports: "
        LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/musa/lib:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/openmpi/lib:${LD_LIBRARY_PATH}
        "
  - job: "build.mccl"
    name: "mccl_qy2"
    parameters:
      mcclBranch: "develop_qy2"
      packageName: "mccl.QY2.tar.gz"
      build_args: "-lbpm -a 22  -q 'TRACE=1' -x '/usr/local/openmpi/'"
      use_build_scripts: 'true'
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-mccl-build-image:v0"
      exports: "
        LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/musa/lib:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/openmpi/lib:${LD_LIBRARY_PATH}
        "
  - job: "build.mtshmem"
    name: "mtshmem_ph1"
    parameters:
      branch: "develop"
      packageName: "mtshmem.PH1.tar.gz"
      cmd: "bash build.sh;cd ./build;make -j"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-mtshmem-build-image:v0"
      mtmlPackageUrl: 'https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/mccl_mtshmem_rely/79e98a02a_mtml_2.2.0-linux-R_amd64.deb'

build_others:
  # - job: "build.alphacore"
  #   name: "alphacore"
  #   parameters:
  #     muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
  #     muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
  - job: "build.MUPTI"
    name: "MUPTI"
    parameters:
      branch: "master"
      buildBranch: "master"
      packageName: "MUPTI.deb"
      compileArgs: "-DCPACK_PACKAGING_INSTALL_PREFIX=/usr/local/musa -DCMAKE_BUILD_TYPE=Release"
      podResources: "requests=cpu=20;requests=memory=32Gi;limits=cpu=20;limits=memory=32Gi;"
      scripts:
        - "./build.sh -b Release -a x86_64 --package"
        - "tar -zcvf musa_runtime_include.tar.gz /usr/local/musa/include"
        - "tar -zcvf musa_shared_include.tar.gz musa_shared_include"
      artifacts:
        - path: "MUPTI/build"
          name: "mupti*.deb"
          ossName: "${commitId}_mupti_x86_64_release.deb"
        - path: "MUPTI/build"
          name: "mupti*.tar.gz"
          ossName: "${commitId}_mupti_x86_64_release.tar.gz"
        - path: "MUPTI"
          name: "musa_runtime_include.tar.gz"
          ossName: "${commitId}_musa_runtime_include.tar.gz"
        - path: "MUPTI"
          name: "musa_shared_include.tar.gz"
          ossName: "${commitId}_musa_shared_include.tar.gz"

  - job: "build.mudnn"
    name: "mudnn_ph1_perf"
    parameters:
      branch: "develop"
      packageName: "mudnn.PH1.profiling.tar.gz"
      cmd: "-m mp_31 -s -p"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2_perf"
    parameters:
      branch: "develop"
      packageName: "mudnn.QY2.profiling.tar.gz"
      cmd: "-m mp_22 -s -p"
      podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_ph1_gcov"
    parameters:
      branch: "develop"
      packageName: "mudnn_gcov.PH1.tar.gz"
      cmd: "-m mp_31 -s -g"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2_gcov"
    parameters:
      branch: "develop"
      packageName: "mudnn_gcov.QY2.tar.gz"
      cmd: "-m mp_22 -s -g"
      podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  # - job: "build.mudnn"
  #   name: "mudnn_qy1_gcov"
  #   parameters:
  #     branch: "develop"
  #     packageName: "mudnn_gcov.QY1.tar.gz"
  #     cmd: "-m mp_21 -s -g"
  #     podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  - job: "copy_packages"
    name: "copy_packages"
  - job: "copy_sdk_packages"
    name: "copy_sdk_packages"
    parameters:
      mtml: 'true'
      mtml_branch: "release_2.2"
      msys: 'true'
      msys_branch: "develop"
      dcgm: 'true'
      dcgm_branch: "develop"

deploys:
  - job: "test.operatorinstall"
    name: "deployFarm by operator"

tests:
  - job: "daily.musa_cts"
    name: "musa_cts.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "daily.musa_cts"
    name: "compatible.musa_cts.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/master/musa_toolkits_install_full.tar.gz"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s5000"
    parameters:
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_perf"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s5000"
    parameters:
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=31"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.daily"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.weekly_2case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'bsrmm or spsm_csr'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.weekly_34case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'not (bsrmm or spsm_csr)'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muPP_cts"
    name: "test.muPP_cts.s5000"
    parameters:
      testType: "daily"
      testArgs: "-m 'rc'"
      containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSOLVER_cts"
    name: "test.muSOLVER_cts.s5000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muAlg_cts"
    name: "test.muAlg_cts.s5000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=31"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muThrust_cts"
    name: "test.muThrust_cts.s5000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=31"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.mtcc"
    name: "test.mtcc.s5000"
    parameters:
      testType: "daily"
      testArgs: "--device=ph1 --tag=daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.s5000"
    parameters:
        mudnnBranch: "develop"
        test_type: "daily"
        test_mark: "mudnn"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.s5000"
    parameters:
        mudnnBranch: "develop"
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000_perf"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mutlass"
    name: "test.mutlass.s5000"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=30;limits=cpu=30;requests=memory=50Gi;limits=memory=50Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mupti_cts"
    name: "test.mupti_cts.s5000"
    parameters:
        ctsBranch: "master"
        mtgpuArch: "mp_31"
        testType: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"
  - job: "CI_MTML_TEST"
    name: "test_mtml.s5000"
    parameters:
      mtmlBranch: "release_2.0"
      gpuType: "S5000"
      testMtml: "true"
      testGmi: "false"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v11"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
      exclude_cases: "DeviceGetPciInfoTest.baseGetPci,VpuGetDecoderSessionMetricsTest.getSessionMetricsWithFfmpeg,
                      VpuGetUtilizationTest.withFfmpegDecoding,DeviceGetPowerUsageTest.*,MemoryGetVendorTest.*,
                      GpuGetMaxClockTest.baseGetMaxClock,DeviceGetSerialNumberTest*,DeviceResetTest*,DeviceGetNameTest.validData,
                      DeviceInitByPciSbdfTest*"
  - job: "CI_msys_cli_test"
    name: "test_msys.s5000"
    parameters:
      msysBranch: "release-1.4"
      gpuType: "S5000"
      testType: "compute"
      benchmark: "--compute"
      msys_cases: '-k "not graphics"'
      containerImage: "sh-harbor.mthreads.com/qa/musa_test-ubuntu-22-04:v2"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
  - job: "test.musa_perf_daily"
    name: "test.musa_perf_daily.s5000"
    parameters:
      runChoice: "pod"
      testMark: "musa_benchmarks_perf_m3d"
      musa_benchmark_cts_branch: "m3d_master"
      testType: "daily"
      podNodeSelector: "In=GPU_TYPE=s5000_perf"
      podResources: "requests=cpu=10;requests=memory=20Gi;requests=mthreads.com/gpu=8;limits=cpu=10;limits=memory=62Gi;limits=mthreads.com/gpu=8;"
  - job: "test.triton_cts"
    name: "test.triton_cts.s5000"
    parameters:
      pythonVersion: "py310"
      torchPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/AI-Frameworks/s5000/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/AI-Frameworks/s5000/torch_musa-2.1.1-cp310-cp310-linux_x86_64.whl"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=cpu=10;requests=memory=25Gi;limits=cpu=10;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:${LD_LIBRARY_PATH};TEST_TYPE=$testType;"
  - job: "daily.musa_cts"
    name: "musa_cts.s4000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "daily.musa_cts"
    name: "compatible.musa_cts.s4000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/master/musa_toolkits_install_full.tar.gz"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s4000"
    parameters:
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_perf"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s4000"
    parameters:
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=22"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.daily"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.weekly_2case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'bsrmm or spsm_csr'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.weekly_34case"
    parameters:
      testType: "weekly"
      testArgs: "-k 'not (bsrmm or spsm_csr)'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muPP_cts"
    name: "test.muPP_cts.s4000"
    parameters:
      testType: "daily"
      testArgs: "-m 'rc'"
      containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSOLVER_cts"
    name: "test.muSOLVER_cts.s4000"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muAlg_cts"
    name: "test.muAlg_cts.s4000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=22"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muThrust_cts"
    name: "test.muThrust_cts.s4000"
    parameters:
      compileArgs: "-DMUSA_ARCHS=22"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.mtcc"
    name: "test.mtcc.s4000"
    parameters:
      testType: "daily"
      testArgs: "--device=quyuan2 --tag=daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s4000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.s4000"
    parameters:
        mudnnBranch: "develop"
        test_type: "daily"
        test_mark: "mudnn"
        timeoutFactor: "4"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.s4000"
    parameters:
        mudnnBranch: "develop"
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_perf"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mutlass"
    name: "test.mutlass.s4000"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=30;limits=cpu=30;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mutlass_benchmark"
    name: "test.mutlass_benchmark.s4000"
    parameters:
        mutlass_branch: "develop"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_perf"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=40;limits=cpu=40;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mupti_cts"
    name: "test.mupti_cts.s4000"
    parameters:
        ctsBranch: "master"
        mtgpuArch: "mp_22"
        testType: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"
  - job: "test.musa_perf_daily"
    name: "test.musa_perf_daily.s4000"
    parameters:
      runChoice: "pod"
      testMark: "musa_benchmarks_perf_m3d"
      musa_benchmark_cts_branch: "m3d_master"
      testType: "daily"
      podNodeSelector: "In=GPU_TYPE=s4000_perf"
      podResources: "requests=cpu=10;requests=memory=20Gi;requests=mthreads.com/gpu=8;limits=cpu=10;limits=memory=62Gi;limits=mthreads.com/gpu=8;"
  - job: "test.triton_cts"
    name: "test.triton_cts.s4000"
    parameters:
      pythonVersion: "py310"
      torchPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/AI-Frameworks/s4000/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/AI-Frameworks/s4000/torch_musa-2.1.1-cp310-cp310-linux_x86_64.whl"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=cpu=10;requests=memory=25Gi;limits=cpu=10;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:${LD_LIBRARY_PATH};TEST_TYPE=$testType;"
  # - job: "daily.musa_cts"
  #   name: "musa_cts.s80"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
  #     test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "compatible.musa_cts.s80"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
  #     test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/master/musa_toolkits_install_full.tar.gz"
  # - job: "test.muBLAS_cts"
  #   name: "test.muBLAS_cts.s80"
  #   parameters:
  #     compileArgs: "-DTEST_MUBLAS_LT=ON"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muFFT_cts"
  #   name: "test.muFFT_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muRAND_cts"
  #   name: "test.muRAND_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.daily"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.weekly_2case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'bsrmm or spsm_csr'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.weekly_34case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'not (bsrmm or spsm_csr)'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muPP_cts"
  #   name: "test.muPP_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "-m 'rc'"
  #     containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSOLVER_cts"
  #   name: "test.muSOLVER_cts.s80"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muAlg_cts"
  #   name: "test.muAlg_cts.s80"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muThrust_cts"
  #   name: "test.muThrust_cts.s80"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.mtcc"
  #   name: "test.mtcc.s80"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "--device=quyuan1 --tag=daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "daily.musa_cts.mtcc.s80"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d"
  #     compileArgs: ""
  #     test_cmd: "pytest -v -m 'musa_mtcc'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mudnn_cts"
  #   name: "test.mudnn_cts.s80"
  #   parameters:
  #       mudnnBranch: "develop"
  #       test_type: "daily"
  #       test_mark: "mudnn"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s80"
  #       podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mupti_cts"
  #   name: "test.mupti_cts.s80"
  #   parameters:
  #       ctsBranch: "master"
  #       mtgpuArch: "mp_21"
  #       testType: "daily"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s80"
  #       podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"

  - job: "daily.musa_cts"
    name: "musa_cts.s4000_hygon"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ ./test_multi_process/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_hygon"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  # - job: "daily.musa_cts"
  #   name: "musa_multi_dev.s4000_hygon"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON"
  #     test_cmd: "pytest . -v -m 'MULTI_DEV'"
  #     gpuArch: "mp_22"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s4000_hygon"
    parameters:
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_hygon"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muFFT_cts"
  #   name: "test.muFFT_cts.s4000_hygon"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s4000_hygon"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=22"
      podNodeSelector: "In=GPU_TYPE=s4000_hygon"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000_hygon.daily"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_hygon"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s4000_hygon.weekly_2case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'bsrmm or spsm_csr'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s4000_hygon.weekly_34case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'not (bsrmm or spsm_csr)'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muPP_cts"
  #   name: "test.muPP_cts.s4000_hygon"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "-m 'rc'"
  #     containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.muSOLVER_cts"
    name: "test.muSOLVER_cts.s4000_hygon"
    parameters:
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_hygon"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muAlg_cts"
  #   name: "test.muAlg_cts.s4000_hygon"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=22"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muThrust_cts"
  #   name: "test.muThrust_cts.s4000_hygon"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=22"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mtcc"
    name: "test.mtcc.s4000_hygon"
    parameters:
      testType: "daily"
      testArgs: "--device=quyuan2 --tag=daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_hygon"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s4000_hygon"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_hygon"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.s4000_hygon"
    parameters:
        mudnnBranch: "develop"
        test_type: "daily"
        test_mark: "mudnn"
        timeoutFactor: "4"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_hygon"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mutlass"
  #   name: "test.mutlass.s4000_hygon"
  #   parameters:
  #       test_type: "daily"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #       podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mupti_cts"
    name: "test.mupti_cts.s4000_hygon"
    parameters:
        ctsBranch: "master"
        mtgpuArch: "mp_22"
        testType: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_hygon"
        podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"

tests_exception:
  - job: "daily.musa_cts"
    name: "musa_exception_test.s5000"
    parameters:
      musa_cts_branch: "m3d_master"
      envExport: "export TEST_TYPE=dailyM3d"
      test_cmd: "pytest -v . -m 'MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
