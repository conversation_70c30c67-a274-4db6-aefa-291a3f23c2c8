build_toolkits:
  - job: "build.musa_toolkit"
    name: "musa_toolkit"
    parameters:
      setupDDK: "false"
      musa_toolkit_branch: "release_musa_4.3.0"
      musart_branch: "release_musa_4.3.0"
      mtcc_branch: "release_musa_4.3.0"
      musify_branch: "release_musa_4.3.0"
      mublas_branch: "release_musa_4.3.0"
      mublaslt_branch: "release_musa_4.3.0"
      mufft_branch: "release_musa_4.3.0"
      mupp_branch: "release_musa_4.3.0"
      murand_branch: "release_musa_4.3.0"
      mtjpeg_branch: "release_musa_4.3.0"
      musparse_branch: "release_musa_4.3.0"
      musolver_branch: "release_musa_4.3.0"
      mupti_branch: "release_musa_4.3.0"
      compileTargetArch: "x86_64"
      makecmds_musatoolkit: "--build_all --build_archs=21,22,31 --build_mupti --ddk_backend=m3d -j128"
      podResources: "requests=cpu=50;requests=memory=200Gi;limits=cpu=50;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
      rename_pkg_musa_toolkits: "musa_toolkits_rc4.3.0.tar.gz"
  - job: "build.mtcc"
    name: "mtcc"
    parameters:
      branch: "release_musa_4.3.0"
      compileArgs: "--build_type release"
      compileParallel: "48"
      rename_pkg: "mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
      podResources: "requests=cpu=50;requests=memory=200Gi;limits=cpu=50;limits=memory=200Gi;"
      containerImage: "sh-harbor.mthreads.com/qa/musa_compile:v7"
build_modules:
  - job: "build.triton_musa"
    name: "triton_musa_py310"
    parameters:
      branch: "release_musa_4.3.0"
      packageName: "triton_rc1.4.0_py310.tar.gz"
      podResources: "requests=cpu=20;requests=memory=40Gi;limits=cpu=20;limits=memory=64Gi"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      parallelJob: 16
      cmd:
        - TRITON_WHEEL_VERSION_SUFFIX=+git`git rev-parse --short HEAD`
        - conda init
        - source ~/.bashrc
        - source /root/miniforge3/etc/profile.d/conda.sh
        - conda activate py310
        - clang -v
        - llvm-config --version
        - python --version
        - which python
      buildScripts:
        - cd python
        - export MAX_JOBS=16
        - python setup.py bdist_wheel -d bdist_wheel/
        - tar -czf triton_rc1.4.0_py310.tar.gz ./bdist_wheel
      testUtScipts:
        - cd python
        - pip install bdist_wheel/triton-*.whl
        - cd build/cmake.linux*
        - ninja test
        - pip install lit
        - lit test
  - job: "build.triton_musa"
    name: "triton_musa_py311"
    parameters:
      branch: "release_musa_4.3.0"
      packageName: "triton_rc1.4.0_py311.tar.gz"
      podResources: "requests=cpu=20;requests=memory=40Gi;limits=cpu=20;limits=memory=64Gi"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      parallelJob: 16
      cmd:
        - TRITON_WHEEL_VERSION_SUFFIX=+git`git rev-parse --short HEAD`
        - conda init
        - source ~/.bashrc
        - source /root/miniforge3/etc/profile.d/conda.sh
        - conda activate py311
        - clang -v
        - llvm-config --version
        - python --version
        - which python
      buildScripts:
        - cd python
        - export MAX_JOBS=16
        - python setup.py bdist_wheel -d bdist_wheel/
        - tar -czf triton_rc1.4.0_py311.tar.gz ./bdist_wheel
      testUtScipts:
        - cd python
        - pip install bdist_wheel/triton-*.whl
        - cd build/cmake.linux*
        - ninja test
        - pip install lit
        - lit test
  - job: "build.mudnn"
    name: "mudnn_ph1"
    parameters:
      branch: "release_musa_4.3.0"
      packageName: "mudnn_rc3.1.0.PH1.tar.gz"
      cmd: "-m mp_31 -s"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2"
    parameters:
      branch: "release_musa_4.3.0"
      packageName: "mudnn_rc3.1.0.QY2.tar.gz"
      cmd: "-m mp_22 -s"
      podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  # - job: "build.mudnn"
  #   name: "mudnn_qy1"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     packageName: "mudnn_rc3.1.0.QY1.tar.gz"
  #     cmd: "-m mp_21 -s"
  #     podResources: "requests=cpu=40;requests=memory=64Gi;limits=cpu=40;limits=memory=64Gi;"
  - job: "build.mccl"
    name: "mccl_qy2"
    parameters:
      mcclBranch: "release_musa_4.3.0_s4000"
      packageName: "mccl_rc2.1.0.QY2.tar.gz"
      build_args: "-lbpm -a 22  -q 'TRACE=1' -x '/usr/local/openmpi/'"
      use_build_scripts: 'true'
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-mccl-build-image:v0"
      exports: "
        LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/musa/lib:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/openmpi/lib:${LD_LIBRARY_PATH}
        "
  - job: "build.mccl"
    name: "mccl_ph1"
    parameters:
      mcclBranch: "release_musa_4.3.0_s5000"
      packageName: "mccl_rc2.1.0.PH1.tar.gz"
      build_args: "-lbpm -a 31  -q 'TRACE=1' -x '/usr/local/openmpi/'"
      use_build_scripts: 'true'
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-mccl-build-image:v0"
      exports: "
        LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/musa/lib:${LD_LIBRARY_PATH}
        LD_LIBRARY_PATH=/usr/local/openmpi/lib:${LD_LIBRARY_PATH}
        "
  - job: "build.mtshmem"
    name: "mtshmem_ph1"
    parameters:
      branch: "develop"
      packageName: "mtshmem.PH1.tar.gz"
      cmd: "bash build.sh;cd ./build;make -j"
      containerImage: "sh-harbor.mthreads.com/qa/ubuntu-22-04-mtshmem-build-image:v0"
      mtmlPackageUrl: 'https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/mccl_mtshmem_rely/79e98a02a_mtml_2.2.0-linux-R_amd64.deb'
build_others:
  - job: "build.mudnn"
    name: "mudnn_ph1_perf"
    parameters:
      branch: "release_musa_4.3.0"
      packageName: "mudnn.PH1.profiling.tar.gz"
      cmd: "-m mp_31 -s -p"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mudnn"
    name: "mudnn_qy2_perf"
    parameters:
      branch: "release_musa_4.3.0"
      packageName: "mudnn.QY2.profiling.tar.gz"
      cmd: "-m mp_22 -s -p"
      podResources: "requests=cpu=60;requests=memory=64Gi;limits=cpu=60;limits=memory=64Gi;"
  - job: "build.mate"
    name: "mate_ph1"
    parameters:
      branch: "master"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/newest/master/muAlg.tar"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/newest/master/muThrust.tar"
      torchPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/master/2025-08-12/AI-Frameworks/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/master/2025-08-12/AI-Frameworks/torch_musa-2.1.0-cp310-cp310-linux_x86_64.whl"
      packageName: "mate.PH1.tar.gz"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=24;limits=cpu=24;requests=memory=40Gi;limits=memory=40Gi;"
  - job: "copy_packages"
    name: "copy_packages"
  - job: "copy_sdk_packages"
    name: "copy_sdk_packages"
    parameters:
      mtml: 'true'
      mtml_branch: "release_2.2"
      msys: 'false'
      dcgm: 'false'

deploys:
  - job: "test.operatorinstall"
    name: "deployFarm by operator"

tests:
  - job: "daily.musa_cts"
    name: "musa_cts.s5000"
    parameters:
      musa_cts_branch: "release_musa_4.3.0"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "daily.musa_cts"
    name: "compatible.musa_cts.s5000"
    parameters:
      musa_cts_branch: "release_musa_4.3.0"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/musa_toolkits_install_full.tar.gz"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000_perf"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s5000"
    parameters:
      muBLASCtsBranch: "release_musa_4.3.0"
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=31"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.daily"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.weekly_2case"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "weekly"
      testArgs: "-k 'bsrmm or spsm_csr'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s5000.weekly_34case"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "weekly"
      testArgs: "-k 'not (bsrmm or spsm_csr)'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muPP_cts"
    name: "test.muPP_cts.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      testArgs: "-m 'rc'"
      containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSOLVER_cts"
    name: "test.muSOLVER_cts.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muAlg_cts"
  #   name: "test.muAlg_cts.s5000"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=31"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s5000"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muThrust_cts"
  #   name: "test.muThrust_cts.s5000"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=31"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s5000"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.mtcc"
    name: "test.mtcc.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      testArgs: "--device=ph1 --tag=daily --disable_asm=true"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s5000"
    parameters:
      musa_cts_branch: "release_musa_4.3.0"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.s5000"
    parameters:
        mudnn_ctsBranch: "release_musa_4.3.0"
        mudnnBranch: "release_musa_4.3.0"
        test_type: "daily"
        test_mark: "mudnn"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.s5000"
    parameters:
        mudnnBranch: "release_musa_4.3.0"
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000_perf"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mutlass"
    name: "test.mutlass.s5000"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=30;limits=cpu=30;requests=memory=50Gi;limits=memory=50Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mate_cts"
    name: "test.mate_cts.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      mateBranch: "master"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/newest/master/muAlg.tar"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/newest/master/muThrust.tar"
      torchPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/master/2025-08-12/AI-Frameworks/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/master/2025-08-12/AI-Frameworks/torch_musa-2.1.0-cp310-cp310-linux_x86_64.whl"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=24;limits=cpu=24;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mupti_cts"
    name: "test.mupti_cts.s5000"
    parameters:
        ctsBranch: "release_musa_4.3.0"
        mtgpuArch: "mp_31"
        testType: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s5000"
        podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"
  - job: "CI_MTML_TEST"
    name: "test_mtml.s5000"
    parameters:
      mtmlBranch: "release_2.0"
      gpuType: "S5000"
      testMtml: "true"
      testGmi: "false"
      containerImage: "sh-harbor.mthreads.com/sdk/management:v11"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
      exclude_cases: "DeviceGetPciInfoTest.baseGetPci,VpuGetDecoderSessionMetricsTest.getSessionMetricsWithFfmpeg,
                      VpuGetUtilizationTest.withFfmpegDecoding,DeviceGetPowerUsageTest.*,MemoryGetVendorTest.*,
                      GpuGetMaxClockTest.baseGetMaxClock,DeviceGetSerialNumberTest*,DeviceResetTest*,DeviceGetNameTest.validData,
                      DeviceInitByPciSbdfTest*"
  - job: "CI_msys_cli_test"
    name: "test_msys.s5000"
    parameters:
      msysBranch: "release-1.4"
      gpuType: "S5000"
      testType: "compute"
      benchmark: "--compute"
      msys_cases: '-k "not graphics"'
      containerImage: "sh-harbor.mthreads.com/qa/musa_test-ubuntu-22-04:v2"
      podResources: "requests=cpu=6;requests=memory=8Gi;limits=cpu=10;limits=memory=8Gi;limits=mthreads.com/gpu=1;"
      podNodeSelector: "In=CPU_PLATFORM=intel;In=GPU_TYPE=s5000;"
      runChoice: "pod"
  - job: "test.musa_perf_daily"
    name: "test.musa_perf_daily.s5000"
    parameters:
      runChoice: "pod"
      testMark: "musa_benchmarks_perf_m3d"
      musa_benchmark_cts_branch: "m3d_master"
      testType: "daily"
      podNodeSelector: "In=GPU_TYPE=s5000_perf"
      podResources: "requests=cpu=10;requests=memory=20Gi;requests=mthreads.com/gpu=8;limits=cpu=10;limits=memory=62Gi;limits=mthreads.com/gpu=8;"
  - job: "test.triton_cts"
    name: "test.triton_cts.s5000"
    parameters:
      branch: "release_musa_4.3.0"
      pythonVersion: "py310"
      torchPackageUrl: "https://sh-moss.mthreads.com/dependency/SWQA_Server/release_musa_4.3.0/torch_musa_release_kuae21/musaDailyBuild_0929/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://sh-moss.mthreads.com/dependency/SWQA_Server/release_musa_4.3.0/torch_musa_release_kuae21/musaDailyBuild_0929/torch_musa-2.5.0-cp310-cp310-linux_x86_64.whl"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=cpu=10;requests=memory=25Gi;limits=cpu=10;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:${LD_LIBRARY_PATH};TEST_TYPE=$testType;"
  - job: "daily.musa_cts"
    name: "musa_cts.s4000"
    parameters:
      musa_cts_branch: "release_musa_4.3.0"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  - job: "daily.musa_cts"
    name: "compatible.musa_cts.s4000"
    parameters:
      musa_cts_branch: "release_musa_4.3.0"
      envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
      compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
      test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/musa_toolkits_install_full.tar.gz"
  - job: "test.mathLibs_perf"
    name: "test.mathLibs_perf.s4000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "benchmark"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000_perf"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muBLAS_cts"
    name: "test.muBLAS_cts.s4000"
    parameters:
      muBLASCtsBranch: "release_musa_4.3.0"
      compileArgs: "-DTEST_MUBLAS_LT=ON"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muFFT_cts"
    name: "test.muFFT_cts.s4000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muRAND_cts"
    name: "test.muRAND_cts.s4000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
      runChoice: "pod"
      compileArgs: "-DMUSA_ARCHS=22"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=20;limits=cpu=20;requests=memory=27Gi;limits=memory=27Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.daily"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.weekly_2case"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "weekly"
      testArgs: "-k 'bsrmm or spsm_csr'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSPARSE_cts"
    name: "test.muSPARSE_cts.s4000.weekly_34case"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "weekly"
      testArgs: "-k 'not (bsrmm or spsm_csr)'"
      containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muPP_cts"
    name: "test.muPP_cts.s4000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      testArgs: "-m 'rc'"
      containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.muSOLVER_cts"
    name: "test.muSOLVER_cts.s4000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=120Gi;limits=ephemeral-storage=120Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muAlg_cts"
  #   name: "test.muAlg_cts.s4000"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=22"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muThrust_cts"
  #   name: "test.muThrust_cts.s4000"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=22"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  - job: "test.mtcc"
    name: "test.mtcc.s4000"
    parameters:
      branch: "release_musa_4.3.0"
      testType: "daily"
      testArgs: "--device=quyuan2 --tag=daily --disable_asm=true"
      containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "daily.musa_cts"
    name: "daily.musa_cts.mtcc.s4000"
    parameters:
      musa_cts_branch: "release_musa_4.3.0"
      envExport: "export TEST_TYPE=dailyM3d"
      compileArgs: ""
      test_cmd: "pytest -v -m 'musa_mtcc'"
      gpuArch: "mp_22"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_cts"
    name: "test.mudnn_cts.s4000"
    parameters:
        mudnn_ctsBranch: "release_musa_4.3.0"
        mudnnBranch: "release_musa_4.3.0"
        test_type: "daily"
        test_mark: "mudnn"
        timeoutFactor: "4"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mudnn_benchmark"
    name: "test.mudnn_benchmark.s4000"
    parameters:
        mudnnBranch: "release_musa_4.3.0"
        test_type: "benchmark"
        test_mark: "m3d_mudnn_benchmark"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_perf"
        podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mutlass"
    name: "test.mutlass.s4000"
    parameters:
        test_type: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=30;limits=cpu=30;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  - job: "test.mutlass_benchmark"
    name: "test.mutlass_benchmark.s4000"
    parameters:
        mutlass_branch: "develop"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000_perf"
        podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=40;limits=cpu=40;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=8;limits=mthreads.com/gpu=8;"
  - job: "test.mupti_cts"
    name: "test.mupti_cts.s4000"
    parameters:
        ctsBranch: "release_musa_4.3.0"
        mtgpuArch: "mp_22"
        testType: "daily"
        runChoice: "pod"
        podNodeSelector: "In=GPU_TYPE=s4000"
        podResources: "requests=cpu=10;requests=memory=20Gi;limits=cpu=10;limits=memory=20Gi;limits=mthreads.com/gpu=1;"
  - job: "test.musa_perf_daily"
    name: "test.musa_perf_daily.s4000"
    parameters:
      runChoice: "pod"
      testMark: "musa_benchmarks_perf_m3d"
      musa_benchmark_cts_branch: "m3d_master"
      testType: "daily"
      podNodeSelector: "In=GPU_TYPE=s4000_perf"
      podResources: "requests=cpu=10;requests=memory=20Gi;requests=mthreads.com/gpu=8;limits=cpu=10;limits=memory=62Gi;limits=mthreads.com/gpu=8;"
  - job: "test.triton_cts"
    name: "test.triton_cts.s4000"
    parameters:
      branch: "release_musa_4.3.0"
      pythonVersion: "py310"
      torchPackageUrl: "https://oss.mthreads.com/ai-product/release-rc/torch_musa/kuae-release-2.1/s4000/20251011/torch-2.5.0-cp310-cp310-linux_x86_64.whl"
      torchMusaPackageUrl: "https://oss.mthreads.com/ai-product/release-rc/torch_musa/kuae-release-2.1/s4000/20251011/torch_musa-2.5.0-cp310-cp310-linux_x86_64.whl"
      muThrustPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muThrust.tar"
      muAlgPackageUrl: "https://sh-moss.mthreads.com/dependency/musa/tools/muAlg.tar"
      testType: "daily"
      containerImage: "sh-harbor.mthreads.com/qa/triton_test-ubuntu-22-04:v1"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s4000"
      podResources: "requests=cpu=10;requests=memory=25Gi;limits=cpu=10;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
      exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu/musa:/usr/local/musa/lib:${LD_LIBRARY_PATH};TEST_TYPE=$testType;"
  # - job: "daily.musa_cts"
  #   name: "musa_cts.s80"
  #   parameters:
  #     musa_cts_branch: "release_musa_4.3.0"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
  #     test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "compatible.musa_cts.s80"
  #   parameters:
  #     musa_cts_branch: "release_musa_4.3.0"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4; export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:$LD_LIBRARY_PATH;"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON -DENABLE_MODEL_TEST=ON"
  #     test_cmd: "pytest -v ./test_musa_cts/ ./test_cuda_samples/ ./test_model_ut/ ./test_ptsz/ -k 'not skip and not xorg' -m 'not XORG and not MUSA_EXCEPTION_TEST'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-release/musa/internal/stable/master/musa_toolkits_install_full.tar.gz"
  # - job: "test.muBLAS_cts"
  #   name: "test.muBLAS_cts.s80"
  #   parameters:
  #     muBLASCtsBranch: "release_musa_4.3.0"
  #     compileArgs: "-DTEST_MUBLAS_LT=ON"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mublas_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muFFT_cts"
  #   name: "test.muFFT_cts.s80"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muRAND_cts"
  #   name: "test.muRAND_cts.s80"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/murand_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=50Gi;limits=ephemeral-storage=50Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.daily"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.weekly_2case"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "weekly"
  #     testArgs: "-k 'bsrmm or spsm_csr'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s80.weekly_34case"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "weekly"
  #     testArgs: "-k 'not (bsrmm or spsm_csr)'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muPP_cts"
  #   name: "test.muPP_cts.s80"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "daily"
  #     testArgs: "-m 'rc'"
  #     containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muSOLVER_cts"
  #   name: "test.muSOLVER_cts.s80"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/musolver_test-ubuntu-22-04:v5"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muAlg_cts"
  #   name: "test.muAlg_cts.s80"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.muThrust_cts"
  #   name: "test.muThrust_cts.s80"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=21"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  #     exports: "PATH=/usr/local/musa/bin:${PATH};LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/musa/lib:${LD_LIBRARY_PATH};"
  # - job: "test.mtcc"
  #   name: "test.mtcc.s80"
  #   parameters:
  #     branch: "release_musa_4.3.0"
  #     testType: "daily"
  #     testArgs: "--device=quyuan1 --tag=daily --disable_asm=true"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "daily.musa_cts.mtcc.s80"
  #   parameters:
  #     musa_cts_branch: "release_musa_4.3.0"
  #     envExport: "export TEST_TYPE=dailyM3d"
  #     compileArgs: ""
  #     test_cmd: "pytest -v -m 'musa_mtcc'"
  #     gpuArch: "mp_21"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s80"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mudnn_cts"
  #   name: "test.mudnn_cts.s80"
  #   parameters:
  #       mudnn_ctsBranch: "release_musa_4.3.0"
  #       mudnnBranch: "release_musa_4.3.0"
  #       test_type: "daily"
  #       test_mark: "mudnn"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s80"
  #       podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.mupti_cts"
  #   name: "test.mupti_cts.s80"
  #   parameters:
  #       ctsBranch: "release_musa_4.3.0"
  #       mtgpuArch: "mp_21"
  #       testType: "daily"
  #       runChoice: "pod"
  #       podNodeSelector: "In=GPU_TYPE=s80"
  #       podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=8;limits=cpu=8;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "daily.musa_cts"
  #   name: "musa_multi_dev.s4000_hygon"
  #   parameters:
  #     musa_cts_branch: "m3d_master"
  #     envExport: "export TEST_TYPE=dailyM3d; export TIMEOUT_FACTOR=4"
  #     compileArgs: "-DENABLE_THIRD_PARTY_TEST=ON"
  #     test_cmd: "pytest . -v -m 'MULTI_DEV'"
  #     gpuArch: "mp_22"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"
  # - job: "test.muFFT_cts"
  #   name: "test.muFFT_cts.s4000_hygon"
  #   parameters:
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/mufft_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=80Gi;limits=memory=80Gi;requests=mthreads.com/gpu=2;limits=mthreads.com/gpu=2;"

  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s4000_hygon.weekly_2case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'bsrmm or spsm_csr'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muSPARSE_cts"
  #   name: "test.muSPARSE_cts.s4000_hygon.weekly_34case"
  #   parameters:
  #     testType: "weekly"
  #     testArgs: "-k 'not (bsrmm or spsm_csr)'"
  #     containerImage: "sh-harbor.mthreads.com/qa/musparse_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=20;limits=cpu=20;requests=memory=40Gi;limits=memory=40Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muPP_cts"
  #   name: "test.muPP_cts.s4000_hygon"
  #   parameters:
  #     testType: "daily"
  #     testArgs: "-m 'rc'"
  #     containerImage: "sh-harbor.mthreads.com/qa/mupp_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=20Gi;limits=memory=20Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"

  # - job: "test.muAlg_cts"
  #   name: "test.muAlg_cts.s4000_hygon"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=22"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=25Gi;limits=memory=25Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
  # - job: "test.muThrust_cts"
  #   name: "test.muThrust_cts.s4000_hygon"
  #   parameters:
  #     compileArgs: "-DMUSA_ARCHS=22"
  #     testType: "daily"
  #     containerImage: "sh-harbor.mthreads.com/qa/muthrust_test-ubuntu-22-04:v2"
  #     runChoice: "pod"
  #     podNodeSelector: "In=GPU_TYPE=s4000_hygon"
  #     podResources: "requests=ephemeral-storage=60Gi;limits=ephemeral-storage=60Gi;requests=cpu=10;limits=cpu=10;requests=memory=28Gi;limits=memory=28Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"

tests_exception:
  - job: "daily.musa_cts"
    name: "musa_exception_test.s5000"
    parameters:
      musa_cts_branch: "release_musa_4.3.0"
      envExport: "export TEST_TYPE=dailyM3d"
      test_cmd: "pytest -v . -m 'MUSA_EXCEPTION_TEST'"
      gpuArch: "mp_31"
      runChoice: "pod"
      podNodeSelector: "In=GPU_TYPE=s5000"
      podResources: "requests=ephemeral-storage=100Gi;limits=ephemeral-storage=100Gi;requests=cpu=20;limits=cpu=20;requests=memory=32Gi;limits=memory=32Gi;requests=mthreads.com/gpu=1;limits=mthreads.com/gpu=1;"
