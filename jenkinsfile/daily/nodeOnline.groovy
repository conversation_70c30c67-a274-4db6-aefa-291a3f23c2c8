@Library('swqa-ci')

import org.swqa.tools.common

commonLib = new common()

def build(config) {
    def buildTasks = [:]
    def builds = config.builds

    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            linuxDdkPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/musa/newest/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
            mtccPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz",
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    // get config from daily/repo.yaml
    config = commonLib.loadConfig("daily/nodeonline/${env.gpuArch}.yaml")
    def workflow = [:]
    workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true ]

    def tests = config.tests ?: []
    List nodeList = env.nodes ? env.nodes.split(';') : []
    for (node in nodeList) {
        for (testConfig in tests) {
            def _testConfig = testConfig
            def defaultParameters = _testConfig.parameters ?: [:]
            def name = _testConfig.name ?: _testConfig.job
            def parameters = [
                linuxDdkPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
                mtccPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz",
                musaToolkitsPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_toolkits_install_full.tar.gz",
                muDNNPackageUrl: "https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/newest/${defaultParameters.linuxDdkBranch}/mudnn_dev2.8.0.PH1.tar.gz",
                nodeLabel: node
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }
            workflow["${name}_${node}"] = [
                job: _testConfig.job,
                parameters: parameters,
                setGitlabStatus: true,
                async: true
            ]
        }
    }
    runPipeline(workflow)
}
