@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

/*
 * parameters
 * linuxDdkBranch (String) - master
 * linuxDdkPackageUrl
 * commitId (String) - null
 * packageName - ddk2.0.deb
 * sendEmail (Bool)
 * email_address
 * runChoice (Choice) - node
 * nodeLabel (Choice) - S80 && Linux
 * gpuType
*/

//install ddk
def installDriver() {
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
}

def installDependency() {
    if (env.jenkinsNode =~ '(10.116.|10.18.)') {
        def endpoint = 'http://***********:56548'
        env.linuxDdkPackageUrl = env.linuxDdkPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.musaToolkitsPackageUrl = env.musaToolkitsPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.mtccPackageUrl = env.mtccPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.anacondaPackageUrl = env.anacondaPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.musifyPackageUrl = env.musifyPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.musaRuntimePackageUrl = env.musaRuntimePackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.muAlgPackageUrl = env.muAlgPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
        env.muThrustPackageUrl = env.muThrustPackageUrl?.replace('https://sh-moss.mthreads.com', endpoint)
    }
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    } else {
        BUILD_TIMESTAMP = env.BUILD_TIMESTAMP.split('_')[0]
        BUILD_TIMESTAMP_NEW = BUILD_TIMESTAMP.replace('-', '.')
        generate_pkg_url = "sh-moss/sw-daily/musa/${env.linuxDdkBranch}/${BUILD_TIMESTAMP}/"
        // content = sh(script: "curl --insecure https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/${env.linuxDdkBranch}/${BUILD_TIMESTAMP}/latest.txt", returnStdout: true).trim()

        // musa_toolkit_pkg_name = content + "_musa_toolkits_install_full.tar.gz"
        // musa_toolkit_url = generate_pkg_url + musa_toolkit_pkg_name
        musa_toolkit_pkg_name = 'musa_toolkits_install_full.tar.gz'
        musa_toolkit_url = generate_pkg_url + musa_toolkit_pkg_name

        oss.install()
        sh """
            mc cp ${musa_toolkit_url} . && tar -xvf ${musa_toolkit_pkg_name}
            cd ./musa_toolkits_install && ./install.sh
        """
    }
}

def envCheck() {
    sh """
        export LD_LIBRARY_PATH=/usr/local/musa/lib:$LD_LIBRARY_PATH
        export PATH=/usr/local/musa/bin:$PATH
        musaInfo
    """
}

//install dependency
def installMsys() {
    //branch
    String pkgUrlPrefix = sh(script: "curl --insecure https://sh-moss.mthreads.com/sw-build/msight-system/${env.msysBranch}/latest.txt", returnStdout: true).trim()
    String pkgVersion = sh(script: "curl --insecure ${pkgUrlPrefix}_internal_version.txt", returnStdout: true).trim()
    String name = env.msysBranch == 'develop' ? '_moore-perf-system_' : '_moore-perf-system_kuae_'
    String pkgUrl = pkgUrlPrefix + name + pkgVersion + '_x86_64_internal.deb'
    currentBuild.description += "msys: ${pkgUrl} <br>"
    constants.downloadPackage(pkgUrl)
    sh 'dpkg -i *moore-perf-system*.deb'
    sh 'apt update && apt install g++-12 -y'
}

//run test
def runTest() {
    gitLib.fetchCode('qa_sdk', 'develop', null)
    dir('qa_sdk/msight') {
        //TODO:标签划分compute和graphics用例
        if (env.testType != 'compute') {
            sh """
                cd assets
                mc cp sh-moss/sw-build/computeQA/cuda_compatible/newest/master/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz .
                tar xzf mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz
                ./install.sh
                export PATH=/usr/local/musa/bin:\$PATH
                export LD_LIBRARY_PATH=/usr/local/musa/lib/:\$LD_LIBRARY_PATH
                cd ../test_cases/msys
                apt install glmark2 -y || true
                apt install libglfw3
                nohup /usr/lib/xorg/Xorg &
                export MOORE_PERF_DAU_DISABLED=1
                export DISPLAY=:0.0
                pip3 install -r ../../requirements.txt
                pytest -m "not loop" ${env.msys_cases}
            """
        } else {
            sh """
                cd assets
                mc cp sh-moss/sw-build/computeQA/cuda_compatible/newest/master/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz .
                tar xzf mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz
                ./install.sh
                export PATH=/usr/local/musa/bin:\$PATH
                export LD_LIBRARY_PATH=/usr/local/musa/lib/:\$LD_LIBRARY_PATH
                cd ../test_cases/msys
                export MOORE_PERF_DAU_DISABLED=1
                pip3 install -r ../../requirements.txt
                pytest -m "not loop" ${env.msys_cases}
            """
        }
    }
}

//run benchmark
def runBenchmark() {
    timeout(time: 60, unit: 'MINUTES') {
        gitLib.fetchCode('msight-system', 'develop', null)
        dir('msight-system/benchmark') {
            sh """
                export MOORE_PERF_DAU_DISABLED=1
                export DISPLAY=:0.0
                python3 run.py ${env.benchmark}
            """
            commonLib.publishHTML('', '*.html', 'Benchmark Report')
        }
    }
}

//send email
def sendEmail() {
    def currentTimeMillis = System.currentTimeMillis()
    def formattedTimestamp = new Date(currentTimeMillis).format('yyyy-MM-dd HH:mm:ss')
    def subject = "Moore Perf System Daily Test Report - ${formattedTimestamp}"
    String to = "${env.email_address}"
    String cc = "${env.cc_email_address}"
    emailext(
        subject: subject,
        mimetype: 'text/html',
        body: '''<!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
            </head>
            <body leftmargin="8" marginwidth="0" topmargin="8" marginheight="4" offset="0">
                <table width="95%" cellpadding="20" cellspacing="0" style="font-size: 1pt; font-family: Tahoma, Arial, Helvetica, sans-serif">
                    <tr>
                        <td><br />
                            <b><font color="#0B610B"><font size="6">构建信息</font></font></b>
                            <hr size="2" width="100%" align="center" /></td>
                    </tr>
                    <tr>
                        <td>
                            <ul>
                            <div style="font-size:15px">
                                <li>构建结果：<span style="color:red">${BUILD_STATUS} </span></li>
                                <li>构建编号：${BUILD_NUMBER}</li>
                                <li>构建地址：<a href=${BUILD_URL}>${BUILD_URL}</a></li>
                                <li>构建日志：<a href=${BUILD_URL}console>${BUILD_URL}console</a></li>
                                <li>Benchmark Report：<a href=${BUILD_URL}Benchmark_20Report>${BUILD_URL}Benchmark_20Report</a></li>
                                <li>Benchmark Report：<a href=${BUILD_URL}Benchmark_20Report>${BUILD_URL}Benchmark_20Report</a></li>
                            </div>
                            </ul>
                        </td>
                    </tr>
                </table></font>
            </body>
            </html>''',
        to: to,
        cc: cc
    )
}

runner.start(env.runChoice) {
    def currentTimeMillis = System.currentTimeMillis()
    def formattedTimestamp = new Date(currentTimeMillis).format('yyyy-MM-dd HH:mm:ss')
    env.mailSubject = "${env.mailSubject} - ${formattedTimestamp}"

    def workflow = [:]
    if (env.runChoice == 'node') {
        workflow['install driver'] = [closure: { installDriver() }]
    }
    workflow['install dependency'] = [closure: { installDependency() }]
    // workflow['env check'] = [closure: { envCheck() }]
    workflow['install msys'] = [closure: { installMsys() }]
    workflow['run test'] = [closure: { runTest() }]
    if (env.runBenchmark == 'true') {
        workflow['run benchmark'] = [closure: { runBenchmark() }]
    }

    if (env.sendEmail == 'true') {
        workflow['send email'] = [closure: { sendEmail() }]
    }

    runPipeline(workflow)
}
