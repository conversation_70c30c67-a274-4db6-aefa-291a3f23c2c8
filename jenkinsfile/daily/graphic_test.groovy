@Library('swqa-ci')

def xxx() { log.debug('xxx') }

runner.start(env.runChoice) {
    def workflow = [:]
    def tests = [
        'mtgl_cts': [
            job: 'test.mtgl_cts',
            paramters: [
                musaPackageUrl: env.musaPackageUrl ?: constants.genLatestMusaPackageUrl(),
                testMark: 'test',
                nodeLabel: 'GFX_DAILY_CTS_NODE'
            ],
            async: true
        ],
        'gcbs-gpu': [
            job: 'test.gcbs_gpu',
            parameters: [
                musaPackageUrl: env.musaPackageUrl ?: constants.genLatestMusaPackageUrl(),
                nodeLabel: 'GFX_DAILY_CTS_NODE'
            ],
            async: true
        ],
        'ogl_gles_cts': [
            job: 'daily.graphics_cts',
            parameters: [
                testConfig: '''{
                    "gl4650": {
                        "workdir": "/root/gfx_daily/opengl-cts-4.6.5.0-xorg-intel-ubuntu",
                        "binary": "glcts",
                        "ctsUrl": "https://sh-moss.mthreads.com/sw-release/cts/gl/opengl-cts-4.6.5.0-xorg-intel-ubuntu.tar.gz",
                        "case": "/root/gfx_daily/mt-gfx-test/VK-GL-CTS_caselist/linux/ogl/daily-passlist-for-ddk20-build-4650.txt",
                        "assertOnHWRTrigger": "false"
                    },
                    "gles": {
                        "workdir": "/root/gfx_daily/opengl-es-cts-*******-xorg-intel-ubuntu",
                        "binary": "glcts",
                        "ctsUrl": "https://sh-moss.mthreads.com/sw-release/cts/gles/opengl-es-cts-*******-xorg-intel-ubuntu.tar.gz",
                        "case": "/root/gfx_daily/mt-gfx-test/VK-GL-CTS_caselist/linux/gles/daily_ddk20.txt",
                        "assertOnHWRTrigger": "false"
                    },
                    "gles_hwr": {
                        "workdir": "/root/gfx_daily/opengl-es-cts-*******-xorg-intel-ubuntu",
                        "binary": "glcts",
                        "ctsUrl": "https://sh-moss.mthreads.com/sw-release/cts/gles/opengl-es-cts-*******-xorg-intel-ubuntu.tar.gz",
                        "case": "/root/gfx_daily/mt-gfx-test/VK-GL-CTS_caselist/linux/gles/daily_ddk20_hwr.txt",
                        "assertOnHWRTrigger": "true"
                    },
                }''',
                ctsTimeout: '330',
                nodeLabel: 'GFX_DAILY_CTS_NODE',
                useLatestDeb: 'true',
                logAddress: 'sh-moss/sw-build/gfx_cts_daily',
                submitResult: 'true'
            ],
            async: true
        ],
        'vk_cts': [
            job: 'daily.graphics_cts',
            parameters: [
                testConfig: '''{
                    "vk": {
                        "workdir": "/root/gfx_daily/vulkan-cts-*******-xorg-intel-ubuntu",
                        "binary": "deqp-vk",
                        "ctsUrl": "https://sh-moss.mthreads.com/sw-release/cts/vk/vulkan-cts-*******-xorg-intel-ubuntu.tar.gz",
                        "case": "/root/gfx_daily/mt-gfx-test/VK-GL-CTS_caselist/linux/vulkan/vk_cts_daily_ddk2_v241223.txt",
                        "assertOnHWRTrigger": "false"
                    }
                }''',
                ctsTimeout: '480',
                nodeLabel: 'GFX_DAILY_CTS_NODE',
                useLatestDeb: 'true',
                logAddress: 'sh-moss/sw-build/gfx_cts_daily',
                submitResult: 'true'
            ],
            async: true
        ]
    ]

    tests.keySet().each { test ->
        if (env[test] == 'true') { workflow[test] = tests[test] }
    }

    if (workflow) {
        runPipeline(workflow)
    } else {
        println('Exit without any workflow.')
    }
}
