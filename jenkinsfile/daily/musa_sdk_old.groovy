@Library('swqa-ci')
import org.swqa.tools.common

commonLib = new common()
/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_jump
 * podNodeSelector (String) - In=mt=buildserver 编译job的选择器
 * cluster_build (String) - releaseFarm
 * cluster_test (String) - releaseFarm
 * ddk_branch (String) - ''
 * ddk_commit (String) - ''
 * OSS_SAVE_URL (String) - sh-moss/sw-daily/musa/daily/${ddk_branch}/${BUILD_TIMESTAMP}/
 * aoto_subfolder (boolean) - true
 * ciConfig (String) - https://sh-moss.mthreads.com/sw-daily/musa/${ddk_branch}/daily.musa_sdk.yaml
 * linuxDdkPackageUrl (String) - ''
 * musaToolkitsPackageUrl (String) - ''
 * musaToolkitsSrcPackageUrl (String) - ''
 * mtccPackageUrl (String) - ''
 * mtccSrcPackageUrl (String) - ''
 * muDNNPackageUrl_s5000 (String) - ''
 * muDNNPackageUrl_s4000 (String) - ''
 * muDNNPackageUrl_s4000_hygon (String) - ''
 * muDNNPackageUrl_s80 (String) - ''
 * muDNNPerfPackageUrl_s5000_perf (String) - ''
 * muDNNPerfPackageUrl_s4000_perf (String) - ''
 * mcclPackageUrl_s5000 (String) - ''
 * mcclPackageUrl_s4000 (String) - ''
 * mcclPackageUrl_s4000_hygon (String) - ''
 * tritonMusaPackageUrl_py310 (String) - ''
 * tritonMusaPackageUrl_py311 (String) - ''
 * tritonMusaPackageUrl_py312 (String) - ''
*/

//constant values
ossAlias = constants.genOssAlias()
SAVE_URI = "${constants.OSS.MOSS_URL_PREFIX}/${env.OSS_SAVE_URL}"
SAVE_OSS_PATH = "${ossAlias}/${env.OSS_SAVE_URL}"
if (SAVE_OSS_PATH.endsWith('/')) {
    SAVE_OSS_PATH = SAVE_OSS_PATH.substring(0, SAVE_OSS_PATH.length() - 1)
}
if (SAVE_URI.endsWith('/')) {
    SAVE_URI = SAVE_URI.substring(0, SAVE_URI.length() - 1)
}

if (env.auto_subfolder == 'true') {
    BUILD_TIMESTAMP = env.BUILD_TIMESTAMP.split('_')[0]
    SAVE_URI = "${SAVE_URI}/${BUILD_TIMESTAMP}"
    SAVE_OSS_PATH = "${SAVE_OSS_PATH}/${BUILD_TIMESTAMP}"
}

//value will be set by get_ddk_url()
ddk_url = ''
ddk_commit = ''

//value will be set by generate_packageName()
gCover = 'false'

// 检查是否提供了PackageUrl参数，以决定daily CI执行哪些阶段
def hasPackageUrls() {
    return env.linuxDdkPackageUrl || env.musaToolkitsPackageUrl || env.mtccPackageUrl || env.muDNNPackageUrl_s5000 || env.mcclPackageUrl_s5000 || env.tritonMusaPackageUrl_py310
}

// PackageUrl
linuxDdkPackageUrl = ''
musaToolkitsPackageUrl = ''
musaToolkitsSrcPackageUrl = ''
mtccPackageUrl = ''
mtccSrcPackageUrl = ''
muDNNPackageUrl = ''
muDNNPerfPackageUrl = ''
mcclPackageUrl = ''
tritonMusaPackageUrl = ''

/*
1. get latest ddk
*/
def get_ddk_url(ddk_branch) {
    def pkg_suffix = env.ddk_suffix
    ddk_commit = constants.getLatestPackageCommitId('linux-ddk', ddk_branch)
    try {
        if (SAVE_OSS_PATH && !SAVE_OSS_PATH.endsWith('others')) {
            utils.appendVersionInfo('linux-ddk', ddk_branch, ddk_commit, "${SAVE_OSS_PATH}/others", 'musa_sdk_commit.txt')
        }
    } catch (e) {
        println "追加版本信息文件到OSS失败: ${e.getMessage()}"
    }

    // copy latest.txt of linux-ddk to daily build dir of oss.
    String ossPath_ddk = constants.genLatestOssPath('linux-ddk', ddk_branch)
    def ossPath_ddk_latest = "${constants.genOssAlias()}/${ossPath_ddk}/latest.txt"
    println "ddk_latest: ${ossPath_ddk_latest}"
    oss.setUp()
    oss.cp(ossPath_ddk_latest, "${SAVE_OSS_PATH}")

    def ddk_url_prefix = constants.genOssPath('linux-ddk', ddk_branch, ddk_commit)
    ddk_url = "${ossAlias}/${ddk_url_prefix}/${ddk_commit}_${pkg_suffix}"
    println "ddk_url: ${ddk_url}"
}

def adjust_ddk(ddk_branch) {
    oss.setUp()
    oss.cp(ddk_url)
    def target_ddk_name = null
    if (ddk_url.endsWith('.rpm')) {
        target_ddk_name = ddk_url.split('/')[-1]
    } else {
        sh '''
            rm *.asan.deb ||:
        '''
        def DEB_VERSION = env.DDK_VERSION ?: sh(script: "dpkg-deb -I *.deb|grep 'Version:'|awk '{print \$2}'", returnStdout: true).trim()
        sh '''
            pwd && ls -l
            dpkg-deb -R *.deb ddk_2_deb
            rm -rf ddk_2_deb/home ||:
        '''

        if (ddk_branch == 'release_KUAE_2.0_for_PH1_M3D' || ddk_branch == 'release_musa_4.0.0') {
            sh """
                sed -i 's/mtgpu_drm_major=2/compute_only=1/g' ddk_2_deb/DEBIAN/preinst
            """
        }

        sh """
            chmod 755 ddk_2_deb/DEBIAN/preinst
            chmod 755 ddk_2_deb/DEBIAN/postinst
            chmod 755 ddk_2_deb/DEBIAN/prerm
            chmod 755 ddk_2_deb/DEBIAN/postrm
            dpkg-deb --build ddk_2_deb/ musa_${DEB_VERSION}_amd64.deb
        """
        target_ddk_name = "musa_${DEB_VERSION}_amd64.deb"
        linuxDdkPackageUrl = "${SAVE_URI}/${target_ddk_name}"
    }
    oss.setUp()
    oss.cp(target_ddk_name, "${SAVE_OSS_PATH}")
}

def generate_packageName(config) {
    def dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK) - 1
    println "当前星期几(0=周日, 6=周六): ${dayOfWeek}"

    // 获取参数值，设置默认值
    def buildScheduleType = env.buildScheduleType ?: 'AUTO'
    def manualBuildType = env.manualBuildType ?: 'normal'
    def gcovDay = env.gcovDay ?: '6'  // 默认周六
    def asanDay = env.asanDay ?: '0'  // 默认周日

    println "构建调度类型: ${buildScheduleType}"
    println "GCOV运行日: 星期${gcovDay}"
    println "ASAN运行日: 星期${asanDay}"
    println "手动构建类型: ${manualBuildType}"

    // 根据日期获取对应的工具包配置
    def toolkitConfig = null
    def mtccConfig = null
    def target_toolkitsrc_name = null
    def target_mtccsrc_name = null

    // 判断逻辑
    if (buildScheduleType == 'MANUAL') {
        // 手动模式：直接使用手动选择的版本
        println "手动模式，使用 ${manualBuildType} 版本"
        switch (manualBuildType) {
            case 'gcov':
                gCover = 'true'
                toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit_gcov' }
                mtccConfig = config.build_toolkits.find { it.name == 'mtcc_gcov' }
                target_toolkitsrc_name = 'musa_toolkits_install_full_gcov_src.tar.gz'
                target_mtccsrc_name = 'mtcc_src.tar.gz'
                break
            case 'asan':
                // toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit_asan' }  // todo
                toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit' }
                mtccConfig = config.build_toolkits.find { it.name == 'mtcc' }  // todo
                break
            default: // normal
                toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit' }
                mtccConfig = config.build_toolkits.find { it.name == 'mtcc' }
                break
        }
    } else {
        // 自动模式：根据配置的日期选择版本
        println '自动模式，根据配置日期选择版本'
        if (env.ddk_branch == 'master') {
            if (dayOfWeek.toString() == gcovDay) {
                // 运行 gcov 版本
                println '今天是配置的 GCOV 日，使用 gcov 版本'
                gCover = 'true'
                toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit_gcov' }
                mtccConfig = config.build_toolkits.find { it.name == 'mtcc_gcov' }
                target_toolkitsrc_name = 'musa_toolkits_install_full_gcov_src.tar.gz'
                target_mtccsrc_name = 'mtcc_src.tar.gz'
            } else if (dayOfWeek.toString() == asanDay) {
                // 运行 asan 版本
                println '今天是配置的 ASAN 日，使用 asan 版本'
                // toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit_asan' }  // todo
                toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit' }
                mtccConfig = config.build_toolkits.find { it.name == 'mtcc' }  // todo
            } else {
                // 其他日期运行普通版本
                println '今天运行普通版本'
                toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit' }
                mtccConfig = config.build_toolkits.find { it.name == 'mtcc' }
            }
        } else {
            // 非 master 分支使用普通版本
            println '非 master 分支，使用普通版本'
            toolkitConfig = config.build_toolkits.find { it.name == 'musa_toolkit' }
            mtccConfig = config.build_toolkits.find { it.name == 'mtcc' }
        }
    }

    // 从yaml配置中获取包名
    if (toolkitConfig && toolkitConfig.parameters.rename_pkg_musa_toolkits) {
        musaToolkitsPackageUrl = "${SAVE_URI}/${toolkitConfig.parameters.rename_pkg_musa_toolkits}"
        musaToolkitsSrcPackageUrl = target_toolkitsrc_name ? "${SAVE_URI}/${target_toolkitsrc_name}" : ''
    } else {
        println '从yaml配置中获取 musa_toolkit 包名失败！'
    }

    if (mtccConfig && mtccConfig.parameters.rename_pkg) {
        mtccPackageUrl = "${SAVE_URI}/others/${mtccConfig.parameters.rename_pkg}"
        mtccSrcPackageUrl = target_mtccsrc_name ? "${SAVE_URI}/others/${target_mtccsrc_name}" : ''
    } else {
        println '从yaml配置中获取 mtcc 包名失败！'
    }
}

def display_PackageUrl() {
    // 设置Job描述
    currentBuild.description += """
    <p><b>cluster_test:</b> ${env.cluster_test}</p>
    <p><b>linuxDdkBranch:</b> ${env.ddk_branch}</p>
    <p><b>linuxDdkCommitId:</b> ${env.ddk_commit ?: ddk_commit}</p>
    <p><b>linuxDdkPackageUrl:</b> ${linuxDdkPackageUrl}</p>
    <p><b>musaToolkitsPackageUrl:</b> ${musaToolkitsPackageUrl}</p>
    <p><b>mtccPackageUrl:</b> ${mtccPackageUrl}</p>
    """
    if (musaToolkitsSrcPackageUrl) {
        currentBuild.description += """
        <p><b>musaToolkitsSrcPackageUrl:</b> ${musaToolkitsSrcPackageUrl}</p>
        """
    }
    if (mtccSrcPackageUrl) {
        currentBuild.description += """
        <p><b>mtccSrcPackageUrl:</b> ${mtccSrcPackageUrl}</p>
        """
    }
}

def build_musa_toolkit(config) {
    def buildTasksHigh = [:]
    def buildTasksMiddle = [:]
    def builds = config.build_toolkits

    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def taskName = _buildConfig.name ?: _buildConfig.job  // 获取任务名称
        def defaultParameters = _buildConfig.parameters ?: [:]

        // 合并参数（公共参数 + 任务自有参数）
        def parameters = utils.mergeParameters(defaultParameters, [
            packagePath: "${SAVE_OSS_PATH}/others",
            OSS_SAVE_URL: "${SAVE_OSS_PATH}",
            ddk_url: ddk_url,
            ddk_commit_id: ddk_commit,
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector
        ])

        // 根据任务名称划分到不同的任务组
        if (taskName == 'musa_toolkit') {
            // 只将 musa_toolkit 放入 high 优先级任务组
            buildTasksHigh[taskName] = {
                runPipeline.runJob([
                    job: _buildConfig.job,
                    parameters: utils.toStringParams(parameters)
                ])
            }
        } else {
            // 其他任务（musa_toolkit_gcov、mtcc等）放入 middle 优先级任务组
            buildTasksMiddle[taskName] = {
                runPipeline.runJob([
                    job: _buildConfig.job,
                    parameters: utils.toStringParams(parameters)
                ])
            }
        }
    }

    stage('Run build tasks in parallel') {
        parallel(
            'Run High Priority Tasks': {
                stage('High build') {
                    parallel buildTasksHigh
                }
            },
            'Run Middle Priority Tasks': {
                stage('Middle build') {
                    catchError(buildResult: 'FAILURE', stageResult: 'FAILURE') {
                        parallel buildTasksMiddle
                    }
                }
            }
        )
    }
}

def copy_mtcc() {
    try {
        def mtccPackagePath = "${SAVE_OSS_PATH}/others/mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
        def unstableMtccPath = "${ossAlias}/sw-daily/musa/daily/tmp/unstable/${env.ddk_branch}/"
        oss.setUp()
        oss.cp(mtccPackagePath, unstableMtccPath)
    } catch (e) {
        println "mtcc cp fail with dir: ${mtccPackagePath}"
    }
}

def build(config) {
    def buildTasks = [:]
    def modules = config.build_modules
    for (moduleConfig in modules) {
        Map _moduleConfig = moduleConfig
        def defaultParameters = _moduleConfig.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParameters, [
            packagePath: "${SAVE_OSS_PATH}",
            musaToolkitsPackageUrl: musaToolkitsPackageUrl,
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector
        ])

        buildTasks["${_moduleConfig.name ?: _moduleConfig.job}"] = {
            runPipeline.runJob([
                job: "${_moduleConfig.job}",
                parameters: utils.toStringParams(parameters)
            ])
        }
    }

    def others = config.build_others
    for (moduleConfig in others) {
        Map _moduleConfig = moduleConfig
        def defaultParameters = _moduleConfig.parameters ?: [:]

        // 为build_others任务添加muDNN和mccl包URL参数
        def taskName = _moduleConfig.name ?: ''
        def gpuType = utils.getGpuTypeFromtaskName(taskName)

        if (hasPackageUrls()) {
            // 如果 Jenkins 参数中提供了特定 GPU 类型的 muDNN/mccl 包 URL，则使用参数值，否则动态生成
            muDNNPackageUrl = env."muDNNPackageUrl_${gpuType}" ?: ''
            mcclPackageUrl = env."mcclPackageUrl_${gpuType}" ?: ''
        } else {
            generate_PackageUrl_from_gpuType(config, gpuType, gCover)
        }

        def parameters = utils.mergeParameters(defaultParameters, [
            packagePath: "${SAVE_OSS_PATH}/others",
            musaToolkitsPackageUrl: musaToolkitsPackageUrl,
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            ddk_commit: ddk_commit,
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector,
            muDNNPackageUrl: muDNNPackageUrl,
            mcclPackageUrl: mcclPackageUrl
        ])

        buildTasks["${_moduleConfig.name ?: _moduleConfig.job}"] = {
            runPipeline.runJob([
                job: "${_moduleConfig.job}",
                parameters: utils.toStringParams(parameters)
            ])
        }
    }
    parallel buildTasks
}

def deploy(config) {
    def deployTasks = [:]
    def deploys = config.deploys
    for (deployConfig in deploys) {
        Map _deployConfig = deployConfig
        def defaultParameters = _deployConfig.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParameters, [
            mtmlPackageUrl: env.mtmlPackageUrl,
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            context: env.cluster_test.toLowerCase(),
        ])
        deployTasks["${_deployConfig.name ?: _deployConfig.job}"] = {
            runPipeline.runJob([
                job: "${_deployConfig.job}",
                parameters: parameters
            ])
        }
    }
    println "deployTasks: ${deployTasks}"
    parallel deployTasks
}

/**
 * 根据GPU类型设置相关包URL
 * @param config 配置文件对象
 * @param gpuType GPU类型
 * @param gCover 是否测试代码覆盖率
 */
def generate_PackageUrl_from_gpuType(config, gpuType, gCover='false') {
    // 定义GPU类型到GPU芯片的映射
    def gpuTypeTogpuChip = [
        's5000': 'ph1',
        's4000': 'qy2',
        's4000_hygon': 'qy2',
        's80': 'qy1'
    ]

    def configSuffix = gpuTypeTogpuChip[gpuType]
    if (!configSuffix) {
        println "gpuType: ${gpuType} is invalid!"
        return
    }

    if (gCover != 'true') {
        // 查找对应的mudnn功能包
        def mudnnConfig = config.build_modules.find {
            it.name == "mudnn_${configSuffix}" && it.parameters?.packageName
        }
        if (mudnnConfig) {
            muDNNPackageUrl = "${SAVE_URI}/${mudnnConfig.parameters.packageName}"
        }

        // 查找对应的mudnn性能包
        def mudnnPerfConfig = config.build_others.find {
            it.name == "mudnn_${configSuffix}_perf" && it.parameters?.packageName
        }
        if (mudnnPerfConfig) {
            muDNNPerfPackageUrl = "${SAVE_URI}/others/${mudnnPerfConfig.parameters.packageName}"
        }

        // 查找对应的mccl包
        def mcclConfig = config.build_modules.find {
            it.name == "mccl_${configSuffix}" && it.parameters?.packageName
        }
        if (mcclConfig) {
            mcclPackageUrl = "${SAVE_URI}/${mcclConfig.parameters.packageName}"
        }
    } else {
        // 查找对应的mudnn gcov功能包
        def mudnnConfig = config.build_others.find {
            it.name == "mudnn_${configSuffix}_gcov" && it.parameters?.packageName
        }
        if (mudnnConfig) {
            muDNNPackageUrl = "${SAVE_URI}/others/${mudnnConfig.parameters.packageName}"
        }

        // 查找对应的mudnn gcov性能包（暂时使用非gcov版本）  // todo
        def mudnnPerfConfig = config.build_others.find {
            it.name == "mudnn_${configSuffix}_perf" && it.parameters?.packageName
        }
        if (mudnnPerfConfig) {
            muDNNPerfPackageUrl = "${SAVE_URI}/others/${mudnnPerfConfig.parameters.packageName}"
        }

        // 查找对应的mccl gcov功能包（暂时使用非gcov版本）  // todo
        def mcclConfig = config.build_modules.find {
            it.name == "mccl_${configSuffix}" && it.parameters?.packageName
        }
        if (mcclConfig) {
            mcclPackageUrl = "${SAVE_URI}/${mcclConfig.parameters.packageName}"
        }
    }
}

/**
 * 根据pythonVersion设置相关包URL
 * @param config 配置文件对象
 * @param pythonVersion python版本
 */
def generate_PackageUrl_from_pythonVersion(config, pythonVersion) {
    // 查找对应的triton配置
    def tritonConfig = config.build_modules.find {
        it.name == "triton_musa_${pythonVersion}" && it.parameters?.packageName
    }
    if (tritonConfig) {
        tritonMusaPackageUrl = "${SAVE_URI}/${tritonConfig.parameters.packageName}"
    } else {
        // 回退到默认处理方式
        def pythonVersionMap = [
            'py310': 'triton_py310.tar.gz',
            'py311': 'triton_py311.tar.gz',
            'py312': 'triton_py312.tar.gz',
            ]

        def packageName = pythonVersionMap[pythonVersion] ?: 'triton_py310.tar.gz'
        tritonMusaPackageUrl = "${SAVE_URI}/${packageName}"
        println "pythonVersion: ${pythonVersion} is invalid! Using default URL."
    }
}

def test(tests_group, config) {
    def testTasks = [:]
    def tests = tests_group
    // 生成统一的基础路径
    def reportOssPath = "${SAVE_OSS_PATH}/driver_toolkits_test"

    for (testConfig in tests) {
        Map _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]

        // 从podNodeSelector中提取gpuType
        def gpuType = utils.getGpuTypeFromSelector(defaultParameters['podNodeSelector'])

        // 从yaml中提取pythonVersion
        def pythonVersion = defaultParameters && defaultParameters['pythonVersion'] ? defaultParameters['pythonVersion'] : null

        if (hasPackageUrls()) {
            // 如果 Jenkins 参数中提供了特定 GPU 类型的 muDNN/mccl 包 URL，则使用参数值，否则动态生成
            muDNNPackageUrl = env."muDNNPackageUrl_${gpuType}" ?: ''
            muDNNPerfPackageUrl = env."muDNNPerfPackageUrl_${gpuType}" ?: ''
            mcclPackageUrl = env."mcclPackageUrl_${gpuType}" ?: ''
            // 如果 Jenkins 参数中提供了特定 Python 版本的 triton 包 URL，则使用参数值，否则动态生成
            tritonMusaPackageUrl = env."tritonMusaPackageUrl_${pythonVersion}" ?: null
        } else {
            generate_PackageUrl_from_gpuType(config, gpuType, gCover)
            generate_PackageUrl_from_pythonVersion(config, pythonVersion)
        }

        def parameters = utils.mergeParameters(defaultParameters, [
            cluster: env.cluster_test,
            ddkBranch: env.ddk_branch,
            ddkCommitId: env.ddk_commit ?: ddk_commit,
            musaToolkitsPackageUrl: musaToolkitsPackageUrl,
            musaToolkitsSrcPackageUrl: musaToolkitsSrcPackageUrl,
            muDNNPackageUrl: muDNNPackageUrl,
            muDNNPerfPackageUrl: muDNNPerfPackageUrl,
            mcclPackageUrl: mcclPackageUrl,
            tritonMusaPackageUrl: tritonMusaPackageUrl,
            gCover: gCover
        ])

        // master分支时，传递mtcc给mtcc_test使用，测试反汇编相关功能；kuae等release分支不测试反汇编相关功能
        if (env.ddk_branch == 'master') {
            parameters = utils.mergeParameters(parameters, [
                mtccPackageUrl: mtccPackageUrl,
                mtccSrcPackageUrl: mtccSrcPackageUrl
            ])
        }

        // 生成最终的报告路径
        parameters['reportOssPath'] = "${reportOssPath}/${gpuType}/"
        println "reportOssPath: ${parameters['reportOssPath']}"
        testTasks["${_testConfig.name ?: _testConfig.job}"] = {
            runPipeline.runJob([
                job: "${_testConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel testTasks
}

runner.start(env.runChoice) {
    def config = null
    oss.install()
    constants.downloadPackage(env.ciConfig)
    yaml_file = "${env.ciConfig}".split('/')[-1]
    config = commonLib.loadPipelineConfig("${yaml_file}", '')

    def workflow = [:]
    if (!hasPackageUrls()) {
        // 如果没有提供PackageUrl参数，则执行完整流程
        workflow['get_ddk_url'] = [ closure: { get_ddk_url(env.ddk_branch) } ]
        workflow['adjust_ddk'] = [ closure: { adjust_ddk(env.ddk_branch) } ]
        workflow['generate_packageName'] = [ closure: { generate_packageName(config) } ]
        workflow['build_musa_toolkit'] = [ closure: { build_musa_toolkit(config) }]
        workflow['build_pkgs'] = [ closure: {
            catchError(stageResult: 'FAILURE') {
                build(config)
                if (env.ddk_branch == 'master') {
                    copy_mtcc()
                }
            }
        }]
    }
    else {
        // 如果提供了PackageUrl参数，则跳过构建阶段，直接使用提供的URL
        linuxDdkPackageUrl = env.linuxDdkPackageUrl ?: ''
        musaToolkitsPackageUrl = env.musaToolkitsPackageUrl ?: ''
        musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl ?: ''
        mtccPackageUrl = env.mtccPackageUrl ?: ''
        mtccSrcPackageUrl = env.mtccSrcPackageUrl ?: ''
        if (musaToolkitsPackageUrl.contains('gcov') && env.ddk_branch == 'master') {
            gCover = 'true'
            println 'ℹ️ master分支，从 musaToolkitsPackageUrl 检测到gcov，自动设置gCover=true'
        }
    }

    workflow['display_PackageUrl'] = [ closure: { display_PackageUrl() } ]
    workflow['deploy'] = [ closure: { deploy(config) }]
    workflow['test'] = [ closure: { test(config.tests, config) }]
    workflow['tests_exception'] = [ closure: { test(config.tests_exception, config) }]
    runPipeline(workflow)
}
