@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commitInfo = [:]
today = new Date().format('yyyyMMdd')

def setTag(repo, base, tag) {
    dir("${repo}-${base}") {
        def commitId = gitLib.fetchCode(repo, base, null, [preBuildMerge: false, disableSubmodules: true])
        dir(repo) {
            log.info("setting tag ${tag} for ${repo}(${base}) now...")
            gitLib.setTag(tag)
        }
        return commitId
    }
}

def setRepoTags() {
    def repos = env.repos.split('\n')
    def tasks = [:]
    repos.each { line ->
        // the adding :default is to make sure split result contains at lease 3 contents
        def (repo, base, postfix) = "${line}:default".split(':')
        postfix = postfix == 'default' ? base : postfix
        def tag = "${today}_${postfix}"
        tasks["${repo} @${base}"] = {
            def commitId = null
            catchError(stageResult: 'FAILURE', buildResult: 'FAILURE') { commitId = gitLib.createGitlabTagByApi(repo, tag, base) }
            currentBuild.description += "${repo}: ${postfix}@${commitId} - ${tag}<br />"
            if (!(repo in commitInfo)) { commitInfo[repo] = [:] }
            commitInfo[repo][postfix] = commitId
        }
    }
    parallel tasks
}

def uploadCommitInfo() {
    def filename = "${today}.txt"
    def ossPath = 'sh-moss/sw-daily/repo_tags/'
    try {
        oss.install()

        // Check if remote file exists before downloading
        def remoteFilePath = "${ossPath}${filename}"
        echo "Checking if remote file exists: ${remoteFilePath}"

        def lsOutput = oss.ls(remoteFilePath)

        if (lsOutput && lsOutput.contains(filename)) {
            echo 'Remote file exists, downloading...'
            oss.cp(remoteFilePath, '.')
            def currentInfo = readJSON file: filename
            def latestCommitInfo = new common().mergeMap([currentInfo, commitInfo])
            writeJSON file: filename, json: latestCommitInfo
        } else {
            echo 'Remote file does not exist, creating new file...'
            writeJSON file: filename, json: commitInfo
        }
    } catch (exc) {
        echo "Error checking/downloading remote file: ${exc.getMessage()}"
        echo 'Creating new file with current commit info...'
        writeJSON file: filename, json: commitInfo
    }
    oss.cp(filename, "${ossPath}")
}

runner.start(env.runChoice) {
    def workflow = [
        'main': [closure: { setRepoTags() }],
    ]
    if (env.updateCommitInfoFile == 'true') {
        workflow['upload commit info'] = [closure: { uploadCommitInfo() }]
    }

    runPipeline(workflow)
}
