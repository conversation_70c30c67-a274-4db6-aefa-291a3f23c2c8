/* groovylint-disable-next-line UnusedImport */
@Library('swqa-ci')

import org.swqa.tools.common

properties([
    parameters([
        choice(name: 'runChoice',
            choices: ['node', 'pod']),
        string(name: 'node<PERSON>abel', defaultValue: 'ai_training_s5000', trim: true,
            description: ''),
        string(name: 'containerImage', defaultValue: 'sh-harbor.mthreads.com/qa/m3d_ai_training_ib_m:v1', trim: true,
            description: ''),
        string(name: 'packagePath', defaultValue: 'sh-moss/sw-build/computeQA/cuda_compatible/release_KUAE_2.0_for_PH1_M3D', trim: true,
            description: ''),
        string(name: 'date', defaultValue: '', trim: true,
            description: ''),
        string(name: 'entrypoint', defaultValue: 'cd /data && export INTEGRATION_DATA_ROOT=/data/data && cd /data/data && python resnet_fp32.py', trim: true,
            description: ''),
        string(name: 'mountParms', defaultValue: '-v /root/wwp:/data', trim: true,
            description: ''),
        string(name: 'envExports', defaultValue: 'export PATH=/usr/local/musa/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/bin;export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/lib/libdrm:/usr/local/musa/lib:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/local/musa/lib/:', trim: true,
            description: ''),
    ])
])

def downloadPackage() {
    def dateFlag = env.date ?: new Date().format('yyyy-MM-dd')
    oss.cp("${packagePath}/${dateFlag}/", './')
}

def setupHost() {
    sh 'dpkg -i *_musa_S5000_*_Ubuntu_amd64.deb'
    sh '''
        modinfo mtgpu | grep version
        echo ================ modinfo mtgpu==================================
        modinfo mtgpu
        echo ================================================================
        cat /sys/kernel/debug/musa/version
    '''
    new common().reboot(env.NODE_NAME)
}

def setupContainer() {
    sh '''
        dpkg -i *_musa_S5000_*_Ubuntu_amd64.deb
        (tar xf *_musa_toolkits_install_full.tar.gz && cd musa_toolkits_install && ./install.sh)
        (tar xf mudnn_dev*.PH1.tar.gz && cd mudnn && ./install_mudnn.sh)
        (tar xf mccl_dev*.PH1.tar.gz && cd mccl && ./install.sh)
        (tar xf muThrust.tar && tar xf muAlg.tar && cd package && dpkg -i *)
    '''
    sh '''
        export PATH=/usr/local/musa/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/bin
        export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:/usr/local/lib/libdrm:/usr/local/musa/lib:/usr/local/lib/musa/lib/x86_64-linux-gnu/:/usr/local/musa/lib/:
        musaInfo && muMemcpyTest
    '''
}

def runTest() {
    sh """
        ${envExports}
        ${entrypoint}
    """
}

runner.start(env.runChoice, [pre: {
    def workflow = [
        'download package': [closure: { downloadPackage() }],
        'setupHost': [closure: { setupHost() }],
    ]
    runPipeline(workflow, [disablePost: true])
}, main: {
    def workflow = [
        'setupContainer': [closure: { setupContainer() }],
        'runTest': [closure: { runTest() }],
    ]
    runPipeline(workflow, [disablePost: true, disablePre: true])
}])
