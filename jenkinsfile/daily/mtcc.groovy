@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git
import groovy.json.JsonOutput

commonLib = new common()
gitLib = new git()

env.repo = 'mtcc'
// https://sh-moss.mthreads.com/sw-build/wddm/develop/${wddmCommitId}
env.latestURLWddm = 'https://sh-moss.mthreads.com/sw-build/wddm/develop/latest_new.txt'

// 'https://sh-moss.mthreads.com/sw-build/Daily'  just build release_branch
// Get lastest wddm post build package url
env.wddmBaseUrl = 'https://sh-moss.mthreads.com/sw-build/wddm'

// initialize environment variables
def init() {
    def initParams = [:]
    print('Start build daily')
    // Get daily commit id from tag
    daily_tag_name = env.daily_tag_name ?: new Date().format('yyyyMMdd')
    Map tags_info = new common().fetchCommitFromTag(daily_tag_name)
    initParams['isMr'] = 'false'
    initParams['today'] = daily_tag_name
    initParams['ossRelPath'] = daily_tag_name
    initParams['buildBranch'] = env.gitlabTargetBranch ?: tags_info[env.repo].keySet().first()
    initParams['buildWddmBranch'] = env.gitlabTargetBranch == 'master' ? 'develop' : env.gitlabTargetBranch
    initParams['mtccCommitId'] = env.mtccCommitId ?: tags_info[env.repo][initParams.buildBranch][0..8]
    initParams['prefix'] = "${daily_tag_name}_${initParams.mtccCommitId}_"
    initParams['wddmBaseUrl'] = env.wddmBaseUrl
    initParams['wddmCommitId'] = env.wddmCommitId ?: tags_info['wddm'][initParams.buildWddmBranch][0..8]
    initParams['wddmUrl'] = "${env.wddmBaseUrl}/${initParams.buildWddmBranch}/${initParams.wddmCommitId}/${initParams.wddmCommitId}_wddm"
    print(initParams)
    return initParams
}

def testAfterBuild(testsConfig, initParams) {
    def parallelTests = [:]
    for (testConfig in testsConfig) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def testname = _testConfig.name ?: _testConfig.job
        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: testname,
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        parallelTests[testname] = {
            runPipeline.runJob([
                job: _testConfig.job,
                parameters: parameters,
                wait: true
            ])
        }
    }
    parallel parallelTests
}

def build(builds, initParams) {
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: name,
        ]
        defaultParameters.each { key, value ->
            parameters[key] = parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: parameters
            ])
            if (_buildConfig.tests) {
                testAfterBuild(_buildConfig.tests, initParams)
            }
        }
    }
    parallel buildTasks
}

def download(String packageName, String packagePath) {
    def formatPackagePath = packagePath.replaceFirst('^oss/', '').replaceFirst('^sh-moss/', '').replaceAll("/\$", '')
    oss.install()
    oss.cp("sh-moss/${formatPackagePath}", packageName)
}

def uploadPkg(Map uploadConfig, initParams) {
    pkgRepo = uploadConfig.pakcageRepo ?: 'graphics-compiler-package'
    gitLib.fetchCode(pkgRepo, initParams.buildBranch, '', [disableSubmodules: true])

    def pkgOssPath = upload_pkg.ossPath ?: 'sw-pr/mtcc/daily'
    pkgOssPath = "${pkgOssPath}/${initParams.ossRelPath}/${initParams.prefix}"
    pkgName = upload_pkg.packageReName.name ?: 'musa_compiler_shared'
    for (pkgUpload in upload_pkg.packages) {
        pkgUploadName = pkgUpload.name ?: pkgName
        pkgUploadPath = pkgUpload.path ?: 'Win'
        dir("${pkgRepo}/${pkgUploadPath}")
        new common().runRelyNetwork(3, 10) {
            download("${pkgName}.tar.gz", "${pkgOssPath}${pkgUploadName}.tar.gz")
        }
    }
    credentials.runWithCredential('SSH_GITLAB') {
        dir(pkgRepo) {
            sh """
                git add . || :
                git commit -m "daily build ${initParams.mtccCommitId} ${initParams.buildBranch} ${initParams.buildWddmBranch}" || :
            """
            new common().runRelyNetwork(3, 10) {
                sh "git push origin ${initParams.buildBranch} || :"
            }
        }
    }
}
def test(tests, initParams) {
    def parallelTests = [:]
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: name,
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        parallelTests[testname] = {
            runPipeline.runJob([
                job: _testConfig.job,
                parameters: parameters,
                wait: true
            ])
        }
    }
    parallel parallelTests
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    def initParams = init()
    // get config from repo mtcc
    gitLib.fetchCode('mtcc', initParams.buildBranch, env.mtccCommitId, [disableSubmodules: true])

    dir('mtcc') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', '', [:], 'mtcc/default.yaml')
    }

    def workflow = [:]
    workflow['build'] = [ closure: { build(config.daily.builds, initParams) }, setGitlabStatus: true, statusName: 'jenkins/pkg_builds']
    def tests = config.daily.tests ?: []

    workflow['test'] = [ closure: { test(tests, initParams) }, setGitlabStatus: true, statusName: 'jenkins/daily_tests']

    def upload_pkg = config.daily.update_packages ?: []
    workflow['upload_pkg'] = [ closure: { uploadPkg(upload_pkg, initParams) }, setGitlabStatus: true, statusName: 'jenkins/upload_pkgs']

    runPipeline(workflow)
}
