@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

date = new Date().format('yyyyMMdd')
logAddress = "sh-moss/sw-build/sdk/dcgm/dailyTestLog/${date}/"

/*
 * parameters
 * mailReceiver
 * logAddress - "sh-moss/sw-build/sdk/dcgm/dailyTestLog/${date}/"
 * mtmlBranch - develop
 * dcgmBranch - develop
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - dailyFarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

//install musa toolkits
def installDependency() {
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    } else {
        BUILD_TIMESTAMP = env.BUILD_TIMESTAMP.split('_')[0]
        BUILD_TIMESTAMP_NEW = BUILD_TIMESTAMP.replace('-', '.')
        generate_pkg_url = "sh-moss/sw-daily/computeQA/cuda_compatible/CI/${env.linuxDdkBranch}/${BUILD_TIMESTAMP}/"
        // content = sh(script: "curl --insecure https://sh-moss.mthreads.com/sw-build/computeQA/cuda_compatible/CI/${env.linuxDdkBranch}/${BUILD_TIMESTAMP}/latest.txt", returnStdout: true).trim()

        // musa_toolkit_pkg_name = content + "_musa_toolkits_install_full.tar.gz"
        // musa_toolkit_url = generate_pkg_url + musa_toolkit_pkg_name
        musa_toolkit_pkg_name = 'musa_toolkits_install_full.tar.gz'
        musa_toolkit_url = generate_pkg_url + musa_toolkit_pkg_name

        oss.install()
        sh """
            mc cp ${musa_toolkit_url} . && tar -xvf ${musa_toolkit_pkg_name}
            cd ./musa_toolkits_install && ./install.sh
        """
    }
}

//install mtml&dcgm
def installSDK() {
    String mtmlUrl = sh(script: "curl --insecure https://sh-moss.mthreads.com/sw-build/management/${mtmlBranch}/latest.txt", returnStdout: true).trim()
    constants.downloadAndUnzipPackage(mtmlUrl, '/root')
    String dcgmUrl = sh(script: "curl --insecure https://sh-moss.mthreads.com/sw-build/sdk/dcgm/${dcgmBranch}/latest.txt", returnStdout: true).trim()
    constants.downloadPackage(dcgmUrl)
    sh 'dpkg -i *_mt-datacenter-gpu-manager_*.deb'
}

//build&test
def test() {
    gitLib.fetchCode('qa_sdk', 'develop', null)
    try {
        dir('qa_sdk/dcgm') {
            credentials.runWithCredential('SSH_GITLAB') {
                sh"""
                    apt update; apt install -y cmake git
                    export C_INCLUDE_PATH=/root/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/include/:/usr/local/mtdcgm/include/:\$C_INCLUDE_PATH
                    export CPLUS_INCLUDE_PATH=/root/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/include/:/usr/local/mtdcgm/include/:\$CPLUS_INCLUDE_PATH
                    export LD_LIBRARY_PATH=/root/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/lib/:/usr/local/musa/lib/:/usr/local/mtdcgm/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH
                    export MTML_LIBRARY_PATH=/root/sdk_build_pkg/RELEASE_x86_64_sdk/LINUX/x86_64/RELEASE/lib/
                    export PATH=/usr/local/mtdcgm/bin/:\${PATH}
                    cmake -B build . && cmake --build build
                    cd build
                    ctest --output-on-failure
                """
            }
        }
    } catch (e) {
        dir('qa_sdk/dcgm/build/Testing/Temporary/') {
            if (fileExists('LastTestsFailed.log')) {
                sh """
                    mc cp LastTest.log  ${logAddress}
                    mc cp /var/log/dcgm_mtml.log  ${logAddress} ||:
                    mc cp /var/log/mt-hostengine.log  ${logAddress} ||:
                """
                currentBuild.description += "testLog: https://sh-moss.mthreads.com/sw-build/sdk/dcgm/dailyTestLog/${date}/LastTest.log<br>"
            }
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'install dependency': [closure: { installDependency() }],
        'install sdk': [closure: { installSDK() }],
        'test': [closure: { test() }]
    ]

    runPipeline(workflow)
}
