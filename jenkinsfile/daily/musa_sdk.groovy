@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_jump
 * podNodeSelector (String) - In=mt=buildserver 编译job的选择器
 * cluster_build (String) - releaseFarm
 * cluster_test (String) - releaseFarm
 * ddk_branch (String) - ''
 * OSS_SAVE_URL (String) - sh-moss/sw-daily/musa/daily/${ddk_branch}/${BUILD_TIMESTAMP}/
 * aoto_subfolder (boolean) - true
 * ciConfig (String) - https://sh-moss.mthreads.com/sw-daily/musa/${ddk_branch}/daily.musa_sdk.yaml
 * linuxDdkPackageUrl (String) - ''
 * musaToolkitsPackageUrl (String) - ''
 * musaToolkitsSrcPackageUrl (String) - ''
 * mtccPackageUrl (String) - ''
 * mtccSrcPackageUrl (String) - ''
 * muDNNPackageUrl_s5000 (String) - ''
 * muDNNPackageUrl_s4000 (String) - ''
 * muDNNPackageUrl_s4000_hygon (String) - ''
 * muDNNPerfPackageUrl_s5000_perf (String) - ''
 * muDNNPerfPackageUrl_s4000_perf (String) - ''
 * mcclPackageUrl_s5000 (String) - ''
 * mcclPackageUrl_s4000 (String) - ''
 * mcclPackageUrl_s4000_hygon (String) - ''
 * tritonMusaPackageUrl_py310 (String) - ''
 * tritonMusaPackageUrl_py311 (String) - ''
 * tritonMusaPackageUrl_py312 (String) - ''
*/

//constant values
ossAlias = constants.genOssAlias()
SAVE_URI = "${constants.OSS.MOSS_URL_PREFIX}/${env.OSS_SAVE_URL}"
SAVE_OSS_PATH = "${ossAlias}/${env.OSS_SAVE_URL}"
if (SAVE_OSS_PATH.endsWith('/')) {
    SAVE_OSS_PATH = SAVE_OSS_PATH.substring(0, SAVE_OSS_PATH.length() - 1)
}
if (SAVE_URI.endsWith('/')) {
    SAVE_URI = SAVE_URI.substring(0, SAVE_URI.length() - 1)
}

if (env.auto_subfolder == 'true') {
    BUILD_TIMESTAMP = env.BUILD_TIMESTAMP.split('_')[0]
    SAVE_URI = "${SAVE_URI}/${BUILD_TIMESTAMP}"
    SAVE_OSS_PATH = "${SAVE_OSS_PATH}/${BUILD_TIMESTAMP}"
}

// 检查是否提供了PackageUrl参数，以决定daily CI执行哪些阶段
def hasPackageUrls() {
    return env.linuxDdkPackageUrl || env.musaToolkitsPackageUrl || env.mtccPackageUrl || env.muDNNPackageUrl_s5000 || env.mcclPackageUrl_s5000 || env.tritonMusaPackageUrl_py310
}

ddk_version = ''
gCover = 'false'

// PackageUrl
musaasmPackageUrl = ''
linuxDdkPackageUrl = ''
musaToolkitsPackageUrl = ''
musaToolkitsSrcPackageUrl = ''
mtccPackageUrl = ''
mtccSrcPackageUrl = ''
musaRuntimePackageUrl = ''
mtmlPackageUrl = ''
muDNNPackageUrl = ''
muDNNPerfPackageUrl = ''
mcclPackageUrl = ''
tritonMusaPackageUrl = ''

def generate_PackageUrl(config) {
    def dayOfWeek = Calendar.getInstance().get(Calendar.DAY_OF_WEEK) - 1
    println "当前星期几(0=周日, 6=周六): ${dayOfWeek}"

    // 获取参数值，设置默认值
    def buildScheduleType = env.buildScheduleType ?: 'AUTO'
    def manualBuildType = env.manualBuildType ?: 'normal'
    def gcovDay = env.gcovDay ?: '6'  // 默认周六
    def asanDay = env.asanDay ?: '0'  // 默认周日

    println "构建调度类型: ${buildScheduleType}"
    println "GCOV运行日: 星期${gcovDay}"
    println "ASAN运行日: 星期${asanDay}"
    println "手动构建类型: ${manualBuildType}"

    // 根据日期获取对应的工具包配置
    def ddkConfig = null
    def musa_asmConfig = null
    def mtmlConfig = null
    def toolkitConfig = null
    def mtccConfig = null
    def musaRuntimeConfig = null
    def target_toolkitsrc_name = null
    def target_mtccsrc_name = null

    // 判断逻辑
    if (buildScheduleType == 'MANUAL') {
        // 手动模式：直接使用手动选择的版本
        println "手动模式，使用 ${manualBuildType} 版本"
        switch (manualBuildType) {
            case 'gcov':
                gCover = 'true'
                // ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb_gcov' }  // todo
                // musa_asmConfig = config.build_1st.find { it.name == 'musa_asm_gcov' }  // todo
                // mtmlConfig = config.build_1st.find { it.name == 'mtml_gcov' }  // todo
                ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb' }
                musa_asmConfig = config.build_1st.find { it.name == 'musa_asm' }
                mtmlConfig = config.build_1st.find { it.name == 'mtml' }
                toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit_gcov' }
                mtccConfig = config.build_2nd.find { it.name == 'mtcc_gcov' }
                musaRuntimeConfig = config.build_2nd.find { it.name == 'MUSA-Runtime' }  // todo
                target_toolkitsrc_name = 'musa_toolkits_install_full_gcov_src.tar.gz'
                target_mtccsrc_name = 'mtcc_gcov_src.tar.gz'
                break
            case 'asan':
                // ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb_asan' }  // todo
                // musa_asmConfig = config.build_1st.find { it.name == 'musa_asm_asan' }  // todo
                // mtmlConfig = config.build_1st.find { it.name == 'mtml_asan' }  // todo
                // toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit_asan' }  // todo
                // mtccConfig = config.build_2nd.find { it.name == 'mtcc_asan' }  // todo
                ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb' }
                musa_asmConfig = config.build_1st.find { it.name == 'musa_asm' }
                mtmlConfig = config.build_1st.find { it.name == 'mtml' }
                toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit' }
                mtccConfig = config.build_2nd.find { it.name == 'mtcc' }
                musaRuntimeConfig = config.build_2nd.find { it.name == 'MUSA-Runtime' }  // todo
                break
            default: // normal
                ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb' }
                musa_asmConfig = config.build_1st.find { it.name == 'musa_asm' }
                mtmlConfig = config.build_1st.find { it.name == 'mtml' }
                toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit' }
                mtccConfig = config.build_2nd.find { it.name == 'mtcc' }
                musaRuntimeConfig = config.build_2nd.find { it.name == 'MUSA-Runtime' }
                break
        }
    } else {
        // 自动模式：根据配置的日期选择版本
        println '自动模式，根据配置日期选择版本'
        // 其他日期运行普通版本
        ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb' }
        musa_asmConfig = config.build_1st.find { it.name == 'musa_asm' }
        mtmlConfig = config.build_1st.find { it.name == 'mtml' }
        toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit' }
        mtccConfig = config.build_2nd.find { it.name == 'mtcc' }
        musaRuntimeConfig = config.build_2nd.find { it.name == 'MUSA-Runtime' }
        if (env.ddk_branch == 'master') {
            if (dayOfWeek.toString() == gcovDay) {
                // 运行 gcov 版本
                println '今天是配置的 GCOV 日，使用 gcov 版本'
                gCover = 'true'
                // ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb_gcov' }  // todo
                // musa_asmConfig = config.build_1st.find { it.name == 'musa_asm_gcov' }  // todo
                // mtmlConfig = config.build_1st.find { it.name == 'mtml_gcov' }  // todo
                ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb' }
                musa_asmConfig = config.build_1st.find { it.name == 'musa_asm' }
                mtmlConfig = config.build_1st.find { it.name == 'mtml' }
                toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit_gcov' }
                mtccConfig = config.build_2nd.find { it.name == 'mtcc_gcov' }
                musaRuntimeConfig = config.build_2nd.find { it.name == 'MUSA-Runtime' }  // todo
                target_toolkitsrc_name = 'musa_toolkits_install_full_gcov_src.tar.gz'
                target_mtccsrc_name = 'mtcc_gcov_src.tar.gz'
            } else if (dayOfWeek.toString() == asanDay) {
                // 运行 asan 版本
                println '今天是配置的 ASAN 日，使用 asan 版本'
                // ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb_asan' }  // todo
                // musa_asmConfig = config.build_1st.find { it.name == 'musa_asm_asan' }  // todo
                // mtmlConfig = config.build_1st.find { it.name == 'mtml_asan' }  // todo
                // toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit_asan' }  // todo
                // mtccConfig = config.build_2nd.find { it.name == 'mtcc_asan' }  // todo
                ddkConfig = config.build_1st.find { it.name == 'linux-ddk_deb' }
                musa_asmConfig = config.build_1st.find { it.name == 'musa_asm' }
                mtmlConfig = config.build_1st.find { it.name == 'mtml' }
                toolkitConfig = config.build_2nd.find { it.name == 'musa_toolkit' }
                mtccConfig = config.build_2nd.find { it.name == 'mtcc' }
                musaRuntimeConfig = config.build_2nd.find { it.name == 'MUSA-Runtime' }  // todo
            }
        } else {
            // 非 master 分支使用普通版本
            println '非 master 分支，使用普通版本'
        }
    }

    // 从yaml配置中获取包名
    // linuxDdkPackageUrl由于每日包名不一致，需要去oss获取
    if (ddkConfig && ddkConfig.parameters.packageName) {
        println "linux-ddk packageName: ${ddkConfig.parameters.packageName}"
    // linuxDdkPackageUrl = "${SAVE_URI}/${ddkConfig.parameters.packageName}"
    } else {
        println '从yaml配置中获取 linux-ddk 包名失败！'
    }

    if (musa_asmConfig && musa_asmConfig.parameters.packageName) {
        musaasmPackageUrl = "${SAVE_URI}/${musa_asmConfig.parameters.packageName}"
    } else {
        println '从yaml配置中获取 musa_asm 包名失败！'
    }

    if (mtmlConfig && mtmlConfig.parameters.packageName) {
        mtmlPackageUrl = "${SAVE_URI}/${mtmlConfig.parameters.packageName}.rpm"
    } else {
        println '从yaml配置中获取 mtml 包名失败！'
    }

    if (toolkitConfig && toolkitConfig.parameters.packageName) {
        musaToolkitsPackageUrl = "${SAVE_URI}/${toolkitConfig.parameters.packageName}"
        musaToolkitsSrcPackageUrl = target_toolkitsrc_name ? "${SAVE_URI}/${target_toolkitsrc_name}" : ''
    } else {
        println '从yaml配置中获取 musa_toolkit 包名失败！'
    }

    if (mtccConfig && mtccConfig.parameters.packageName) {
        mtccPackageUrl = "${SAVE_URI}/others/${mtccConfig.parameters.packageName}"
        mtccSrcPackageUrl = target_mtccsrc_name ? "${SAVE_URI}/others/${target_mtccsrc_name}" : ''
    } else {
        println '从yaml配置中获取 mtcc 包名失败！'
    }

    if (musaRuntimeConfig && musaRuntimeConfig.parameters.packageName) {
        musaRuntimePackageUrl = "${SAVE_URI}/others/${musaRuntimeConfig.parameters.packageName}"
    } else {
        println '从yaml配置中获取 MUSA-Runtime 包名失败！'
    }
}

def build_1st(config) {
    def buildTasks = [:]
    def buildJobs = config.build_1st
    for (buildJob in buildJobs) {
        Map _buildJob = buildJob
        println "_buildJob: ${_buildJob}"
        def taskName = _buildJob.name ?: _buildJob.job
        def defaultParams = _buildJob.parameters ?: [:]
        def ddk_version_tmp = defaultParams && defaultParams['ddk_version'] ? defaultParams['ddk_version'] : ''
        if (ddk_version_tmp) {
            ddk_version = ddk_version_tmp
        }
        def parameters = utils.mergeParameters(defaultParams, [
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector,
            packagePath: "${SAVE_OSS_PATH}"
        ])

        buildTasks[taskName] = {
            runPipeline.runJob([
                job: "${_buildJob.job}",
                parameters: utils.toStringParams(parameters)
            ])
        }
    }
    parallel buildTasks
}

def build_2nd(config) {
    // 将 musa_toolkit 放入 high 优先级任务组
    def highPriorityTasks = ['musa_toolkit']
    def buildTasksHigh = [:]
    def buildTasksMiddle = [:]
    def buildJobs = config.build_2nd ?: []

    for (buildJob in buildJobs) {
        def taskName = buildJob.name ?: buildJob.job
        def defaultParams = buildJob.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParams, [
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector,
            packagePath: (taskName.contains('mtcc') || taskName.contains('MUSA-Runtime')) ? "${SAVE_OSS_PATH}/others" : "${SAVE_OSS_PATH}",
            musaasmPackageUrl: constants.urlToOSSPath(musaasmPackageUrl)
        ])

        // 根据任务名称划分到不同的任务组
        if (taskName in highPriorityTasks) {
            buildTasksHigh[taskName] = {
                runPipeline.runJob([
                    job: buildJob.job,
                    parameters: utils.toStringParams(parameters)
                ])
            }
        } else {
            // 其他任务（musa_toolkit_gcov、mtcc等）放入 middle 优先级任务组
            buildTasksMiddle[taskName] = {
                runPipeline.runJob([
                    job: buildJob.job,
                    parameters: utils.toStringParams(parameters)
                ])
            }
        }
    }

    stage('Run build tasks in parallel') {
        parallel(
            'Run High Priority Tasks': {
                stage('High build') {
                    echo "Starting high priority tasks: ${buildTasksHigh.keySet().join(', ')}"
                    parallel buildTasksHigh
                }
            },
            'Run Middle Priority Tasks': {
                stage('Middle build') {
                    echo "Starting middle priority tasks: ${buildTasksMiddle.keySet().join(', ')}"
                    catchError(buildResult: 'FAILURE', stageResult: 'FAILURE') {
                        parallel buildTasksMiddle
                    }
                }
            }
        )
    }
}

def build_3rd(config) {
    def buildTasks = [:]
    def buildJobs = config.build_3rd
    for (buildJob in buildJobs) {
        Map _buildJob = buildJob
        def taskName = _buildJob.name ?: _buildJob.job
        def defaultParams = _buildJob.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParams, [
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector,
            packagePath: "${SAVE_OSS_PATH}",
            musaasmPackageUrl: constants.urlToOSSPath(musaasmPackageUrl),
            linuxDdkPackageUrl: constants.urlToOSSPath(linuxDdkPackageUrl.replaceAll('\\.deb$', '.rpm')),
            musaToolkitsPackageUrl: constants.urlToOSSPath(musaToolkitsPackageUrl),
            mtmlPackageUrl: constants.urlToOSSPath(mtmlPackageUrl_deploy.replace('.deb', '.rpm'))
        ])

        buildTasks[taskName] = {
            runPipeline.runJob([
                job: "${_buildJob.job}",
                parameters: utils.toStringParams(parameters)
            ])
        }
    }

    def others = config.build_others
    for (buildJob in others) {
        Map _buildJob = buildJob
        def taskName = _buildJob.name ?: _buildJob.job
        def defaultParams = _buildJob.parameters ?: [:]

        // 为build_others任务添加muDNN和mccl包URL参数
        def gpuType = utils.getGpuTypeFromtaskName(taskName)
        if (hasPackageUrls()) {
            // 如果 Jenkins 参数中提供了特定 GPU 类型的 muDNN/mccl 包 URL，则使用参数值，否则动态生成
            muDNNPackageUrl = env."muDNNPackageUrl_${gpuType}" ?: ''
            mcclPackageUrl = env."mcclPackageUrl_${gpuType}" ?: ''
        } else {
            generate_PackageUrl_from_gpuType(config, gpuType, gCover)
        }

        def parameters = utils.mergeParameters(defaultParams, [
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector,
            packagePath: "${SAVE_OSS_PATH}/others",
            musaasmPackageUrl: constants.urlToOSSPath(musaasmPackageUrl),
            linuxDdkPackageUrl: constants.urlToOSSPath(linuxDdkPackageUrl.replaceAll('\\.deb$', '.rpm')),
            musaToolkitsPackageUrl: constants.urlToOSSPath(musaToolkitsPackageUrl),
            musaRuntimePackageUrl: constants.urlToOSSPath(musaRuntimePackageUrl),
            mtmlPackageUrl: constants.urlToOSSPath(mtmlPackageUrl_deploy.replace('.deb', '.rpm')),
            muDNNPackageUrl: constants.urlToOSSPath(muDNNPackageUrl),
            mcclPackageUrl: constants.urlToOSSPath(mcclPackageUrl)
        ])

        buildTasks[taskName] = {
            runPipeline.runJob([
                job: "${_buildJob.job}",
                parameters: utils.toStringParams(parameters)
            ])
        }
    }
    parallel buildTasks
}

def build_4th(config) {
    def buildTasks = [:]
    def buildJobs = config.build_4th
    for (buildJob in buildJobs) {
        Map _buildJob = buildJob
        def taskName = _buildJob.name ?: _buildJob.job
        def defaultParams = _buildJob.parameters ?: [:]

        // 为build_others任务添加muDNN和mccl包URL参数
        def gpuType = utils.getGpuTypeFromtaskName(taskName)
        if (hasPackageUrls()) {
            // 如果 Jenkins 参数中提供了特定 GPU 类型的 muDNN/mccl 包 URL，则使用参数值，否则动态生成
            muDNNPackageUrl = env."muDNNPackageUrl_${gpuType}" ?: ''
            mcclPackageUrl = env."mcclPackageUrl_${gpuType}" ?: ''
        } else {
            generate_PackageUrl_from_gpuType(config, gpuType, gCover)
        }

        def parameters = utils.mergeParameters(defaultParams, [
            cluster: env.cluster_build,
            podNodeSelector: env.podNodeSelector,
            packagePath: "${SAVE_OSS_PATH}/ai_framework",
            musaasmPackageUrl: constants.urlToOSSPath(musaasmPackageUrl),
            linuxDdkPackageUrl: constants.urlToOSSPath(linuxDdkPackageUrl.replaceAll('\\.deb$', '.rpm')),
            musaToolkitsPackageUrl: constants.urlToOSSPath(musaToolkitsPackageUrl),
            mtmlPackageUrl: constants.urlToOSSPath(mtmlPackageUrl_deploy.replace('.deb', '.rpm')),
            muDNNPackageUrl: constants.urlToOSSPath(muDNNPackageUrl),
            mcclPackageUrl: constants.urlToOSSPath(mcclPackageUrl)
        ])

        buildTasks[taskName] = {
            runPipeline.runJob([
                job: "${_buildJob.job}",
                parameters: utils.toStringParams(parameters)
            ])
        }
    }

    parallel buildTasks
}

// todo
def copy_mtcc() {
    try {
        def mtccPackagePath = "${SAVE_OSS_PATH}/others/mtcc-x86_64-linux-gnu-ubuntu.tar.gz"
        def unstableMtccPath = "${ossAlias}/sw-daily/musa/daily/tmp/unstable/${env.ddk_branch}/"
        oss.setUp()
        oss.cp(mtccPackagePath, unstableMtccPath)
    } catch (e) {
        println "mtcc cp fail with dir: ${mtccPackagePath}"
    }
}

def display_PackageUrl() {
    // 设置Job描述
    currentBuild.description += """
    <p><b>cluster_test:</b> ${env.cluster_test}</p>
    <p><b>ddk_branch:</b> ${env.ddk_branch}</p>
    <p><b>ddk_version:</b> ${ddk_version}</p>
    <p><b>musaasmPackageUrl:</b> ${musaasmPackageUrl}</p>
    <p><b>linuxDdkPackageUrl:</b> ${linuxDdkPackageUrl}</p>
    <p><b>musaToolkitsPackageUrl:</b> ${musaToolkitsPackageUrl}</p>
    <p><b>mtccPackageUrl:</b> ${mtccPackageUrl}</p>
    <p><b>musaRuntimePackageUrl:</b> ${musaRuntimePackageUrl}</p>
    <p><b>mtmlPackageUrl:</b> ${mtmlPackageUrl}</p>
    """
    if (musaToolkitsSrcPackageUrl) {
        currentBuild.description += """
        <p><b>musaToolkitsSrcPackageUrl:</b> ${musaToolkitsSrcPackageUrl}</p>
        """
    }
    if (mtccSrcPackageUrl) {
        currentBuild.description += """
        <p><b>mtccSrcPackageUrl:</b> ${mtccSrcPackageUrl}</p>
        """
    }
}

def deploy(config) {
    def deployTasks = [:]
    def deploys = config.deploys
    for (deployConfig in deploys) {
        Map _deployConfig = deployConfig
        def defaultParams = _deployConfig.parameters ?: [:]
        def parameters = utils.mergeParameters(defaultParams, [
            linuxDdkPackageUrl: linuxDdkPackageUrl,
            mtmlPackageUrl: env.mtmlPackageUrl_deploy,  // todo 出于稳定性考虑,部署集群使用稳定的mtml(暂时不使用daily build的)
            context: env.cluster_test.toLowerCase()
        ])
        deployTasks["${_deployConfig.name ?: _deployConfig.job}"] = {
            runPipeline.runJob([
                job: "${_deployConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel deployTasks
}

def test(config, testJobs) {
    // 生成统一的基础路径
    def reportOssPath = "${SAVE_OSS_PATH}/report"
    def testTasks = [:]
    for (testJob in testJobs) {
        Map _testJob = testJob
        def taskName = _testJob.name ?: _testJob.job
        def defaultParams = _testJob.parameters ?: [:]

        // 从podNodeSelector中提取gpuType
        def gpuType = utils.getGpuTypeFromSelector(defaultParams['podNodeSelector'])

        // 从yaml中提取pythonVersion
        def pythonVersion = ''
        def pythonVersion_tmp = defaultParams && defaultParams['pythonVersion'] ? defaultParams['pythonVersion'] : ''
        if (pythonVersion_tmp) {
            pythonVersion = pythonVersion_tmp
        }

        if (hasPackageUrls()) {
            // 如果 Jenkins 参数中提供了特定 GPU 类型的 muDNN/mccl 包 URL，则使用参数值，否则动态生成
            muDNNPackageUrl = env."muDNNPackageUrl_${gpuType}" ?: ''
            muDNNPerfPackageUrl = env."muDNNPerfPackageUrl_${gpuType}" ?: ''
            mcclPackageUrl = env."mcclPackageUrl_${gpuType}" ?: ''
            // 如果 Jenkins 参数中提供了特定 Python 版本的 triton 包 URL，则使用参数值，否则动态生成
            tritonMusaPackageUrl = env."tritonMusaPackageUrl_${pythonVersion}" ?: ''
        } else {
            generate_PackageUrl_from_gpuType(config, gpuType, gCover)
            generate_PackageUrl_from_pythonVersion(config, pythonVersion)
        }

        def parameters = utils.mergeParameters(defaultParams, [
            cluster: env.cluster_test,
            ddkBranch: env.ddk_branch,
            ddk_version: ddk_version,
            musaToolkitsPackageUrl: musaToolkitsPackageUrl,
            musaToolkitsSrcPackageUrl: musaToolkitsSrcPackageUrl,
            muDNNPackageUrl: muDNNPackageUrl,
            muDNNPerfPackageUrl: muDNNPerfPackageUrl,
            mcclPackageUrl: mcclPackageUrl,
            tritonMusaPackageUrl: tritonMusaPackageUrl,
            gCover: gCover
        ])

        // master分支时，传递mtcc给mtcc_test使用，测试反汇编相关功能；kuae等release分支不测试反汇编相关功能
        if (env.ddk_branch == 'master') {
            parameters = utils.mergeParameters(parameters, [
                mtccPackageUrl: mtccPackageUrl,
                mtccSrcPackageUrl: mtccSrcPackageUrl
            ])
        }

        // 生成最终的报告路径
        parameters['reportOssPath'] = "${reportOssPath}/${gpuType}/"
        println "reportOssPath: ${parameters['reportOssPath']}"
        testTasks[taskName] = {
            runPipeline.runJob([
                job: "${_testJob.job}",
                parameters: parameters
            ])
        }
    }
    parallel testTasks
}

def get_linuxDdkPackageUrl() {
    def linuxDdk = ''
    def linuxDdkCommit = ''
    def matchStr = "mc ls ${SAVE_OSS_PATH} | grep -i 'musa.*amd64\\.deb' | head -n 1"
    linuxDdk = sh(
        returnStdout: true,
        script: "${matchStr} | awk -F ' ' '{print \$NF}'"
    ).trim()

    // 校验：确保匹配到包，避免空指针
    if (!linuxDdk) {
        error("未在路径 ${SAVE_OSS_PATH} 中找到匹配的 linux-ddk.deb 包")
    }

    // 3. 正则提取commit：匹配包名格式 "musa_日期-xxx-commit_amd64.deb"
    // 正则说明：捕获最后一个 "-" 和 "_amd64.deb" 之间的字符串（即commit）
    def commitMatch = (linuxDdk =~ /musa_.*?-([a-f0-9]+)_amd64\.deb/)
    if (commitMatch) {
        linuxDdkCommit = commitMatch[0][1]
    }
    ddk_version = ddk_version ?: linuxDdkCommit
    linuxDdkPackageUrl = constants.ossPathToUrl("${SAVE_OSS_PATH}/${linuxDdk}")
    println "linuxDdkPackageUrl: ${linuxDdkPackageUrl}"
    println "linuxDdkCommit: ${linuxDdkCommit}"
    println "ddk_version: ${ddk_version}"
}

/**
 * 根据GPU类型设置相关包URL
 * @param config 配置文件对象
 * @param gpuType GPU类型
 * @param gCover 是否测试代码覆盖率
 */
def generate_PackageUrl_from_gpuType(config, gpuType, gCover='false') {
    // 定义GPU类型到GPU芯片的映射
    def gpuTypeTogpuChip = [
        's5000': 'ph1',
        's5000_release_musa_4.3.1': 'ph1',
        's4000': 'qy2',
        's4000_hygon': 'qy2',
        's80': 'qy1'
    ]

    def configSuffix = gpuTypeTogpuChip[gpuType]
    if (!configSuffix) {
        println "gpuType: ${gpuType} is invalid!"
        return
    }

    if (gCover != 'true') {
        // 查找对应的mudnn功能包
        def mudnnConfig = config.build_3rd.find {
            it.name == "mudnn_${configSuffix}" && it.parameters?.packageName
        }
        if (mudnnConfig) {
            muDNNPackageUrl = "${SAVE_URI}/${mudnnConfig.parameters.packageName}"
        }

        // 查找对应的mudnn性能包
        def mudnnPerfConfig = config.build_others.find {
            it.name == "mudnn_${configSuffix}_perf" && it.parameters?.packageName
        }
        if (mudnnPerfConfig) {
            muDNNPerfPackageUrl = "${SAVE_URI}/others/${mudnnPerfConfig.parameters.packageName}"
        }

        // 查找对应的mccl包
        def mcclConfig = config.build_3rd.find {
            it.name == "mccl_${configSuffix}" && it.parameters?.packageName
        }
        if (mcclConfig) {
            mcclPackageUrl = "${SAVE_URI}/${mcclConfig.parameters.packageName}"
        }
    } else {
        // 查找对应的mudnn gcov功能包
        def mudnnConfig = config.build_others.find {
            it.name == "mudnn_${configSuffix}_gcov" && it.parameters?.packageName
        }
        if (mudnnConfig) {
            muDNNPackageUrl = "${SAVE_URI}/others/${mudnnConfig.parameters.packageName}"
        }

        // 查找对应的mudnn gcov性能包（暂时使用非gcov版本）  // todo
        def mudnnPerfConfig = config.build_others.find {
            it.name == "mudnn_${configSuffix}_perf" && it.parameters?.packageName
        }
        if (mudnnPerfConfig) {
            muDNNPerfPackageUrl = "${SAVE_URI}/others/${mudnnPerfConfig.parameters.packageName}"
        }

        // 查找对应的mccl gcov功能包（暂时使用非gcov版本）  // todo
        def mcclConfig = config.build_3rd.find {
            it.name == "mccl_${configSuffix}" && it.parameters?.packageName
        }
        if (mcclConfig) {
            mcclPackageUrl = "${SAVE_URI}/${mcclConfig.parameters.packageName}"
        }
    }
}

/**
 * 根据pythonVersion设置相关包URL
 * @param config 配置文件对象
 * @param pythonVersion python版本
 */
def generate_PackageUrl_from_pythonVersion(config, pythonVersion) {
    // 查找对应的triton配置
    def tritonConfig = config.build_2nd.find {
        it.name == "triton_musa_${pythonVersion}" && it.parameters?.packageName
    }
    if (tritonConfig) {
        tritonMusaPackageUrl = "${SAVE_URI}/${tritonConfig.parameters.packageName}"
        println "tritonMusaPackageUrl: ${tritonMusaPackageUrl}"
    } else {
        // 回退到默认处理方式
        def pythonVersionMap = [
            'py310': 'triton_py310.tar.gz',
            'py311': 'triton_py311.tar.gz',
            'py312': 'triton_py312.tar.gz',
            ]

        def packageName = pythonVersionMap[pythonVersion] ?: 'triton_py310.tar.gz'
        tritonMusaPackageUrl = "${SAVE_URI}/${packageName}"
        println "pythonVersion: ${pythonVersion} is invalid! Using default URL."
    }
}

runner.start(env.runChoice) {
    def config = null
    oss.install()
    constants.downloadPackage(env.ciConfig)
    yaml_file = "${env.ciConfig}".split('/')[-1]
    config = commonLib.loadPipelineConfig("${yaml_file}", '')

    def workflow = [:]
    if (!hasPackageUrls()) {
        // 如果没有提供PackageUrl参数，则执行完整流程
        workflow['generate_PackageUrl'] = [ closure: { generate_PackageUrl(config) } ]
        workflow['build_1st'] = [ closure: { build_1st(config) }]
        workflow['get_linuxDdkPackageUrl'] = [ closure: { get_linuxDdkPackageUrl() }]
        workflow['build_2nd'] = [ closure: { build_2nd(config) }]
        workflow['build_3rd'] = [ closure: {
            catchError(stageResult: 'FAILURE') {
                build_3rd(config)
                if (env.ddk_branch == 'master') {
                    copy_mtcc()
                }
            }
        }]
        workflow['build_4th'] = [ closure: {
            catchError(stageResult: 'FAILURE') {
                build_4th(config)
            }
        }]
    }
    else {
        // 如果提供了PackageUrl参数，则跳过构建阶段，直接使用提供的URL
        musaasmPackageUrl = env.musaasmPackageUrl ?: ''
        linuxDdkPackageUrl = env.linuxDdkPackageUrl ?: ''
        musaToolkitsPackageUrl = env.musaToolkitsPackageUrl ?: ''
        musaToolkitsSrcPackageUrl = env.musaToolkitsSrcPackageUrl ?: ''
        mtccPackageUrl = env.mtccPackageUrl ?: ''
        mtccSrcPackageUrl = env.mtccSrcPackageUrl ?: ''
        musaRuntimePackageUrl = env.musaRuntimePackageUrl ?: ''
        mtmlPackageUrl = env.mtmlPackageUrl ?: ''
        if (musaToolkitsPackageUrl.contains('gcov') && env.ddk_branch == 'master') {
            gCover = 'true'
            println 'ℹ️ master分支，从 musaToolkitsPackageUrl 检测到gcov，自动设置gCover=true'
        }
    }

    workflow['display_PackageUrl'] = [ closure: {
        catchError(stageResult: 'FAILURE') {
            display_PackageUrl()
        }
    }]
    workflow['deploy'] = [ closure: { deploy(config) }]
    workflow['test'] = [ closure: { test(config, config.tests) }]
    workflow['tests_exception'] = [ closure: { test(config, config.tests_exception) }]
    runPipeline(workflow)
}
