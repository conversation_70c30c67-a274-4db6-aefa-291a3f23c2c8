@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def sendEmail(String S80BuildId, String S4000BuildId) {
    def currentTimeMillis = System.currentTimeMillis()
    def formattedTimestamp = new Date(currentTimeMillis).format('yyyy-MM-dd HH:mm:ss')
    def subject = "Moore Perf System Daily Test Report - ${formattedTimestamp}"
    String to = "${env.email_address}"
    emailext(
        subject: subject,
        mimetype: 'text/html',
        body: """<!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
            </head>
            <body leftmargin="8" marginwidth="0" topmargin="8" marginheight="4" offset="0">
                <table width="95%" cellpadding="20" cellspacing="0" style="font-size: 1pt; font-family: Tahoma, Arial, Helvetica, sans-serif">
                    <tr>
                        <td><br />
                            <b><font color="#0B610B"><font size="6">构建信息</font></font></b>
                            <hr size="2" width="100%" align="center" /></td>
                    </tr>
                    <tr>
                        <td>
                            <ul>
                            <div style="font-size:15px">
                                <li>构建结果：<span style="color:red">SUCCESS </span></li>
                                <li>构建编号：${env.BUILD_NUMBER}</li>
                                <li>构建地址：<a href=${env.BUILD_URL}>${env.BUILD_URL}</a></li>
                                <li>构建日志：<a href=${env.BUILD_URL}console>${env.BUILD_URL}console</a></li>
                                <li>S80 Benchmark Report：<a href="http://sh-jenkins.mthreads.com/job/CI_msys_cli_test_qy1/${S80BuildId}/Benchmark_20Report">http://sh-jenkins.mthreads.com/job/CI_msys_cli_test_qy1/${S80BuildId}/Benchmark_20Report</a></li>
                                <li>S4000 Benchmark Report：<a href="http://sh-jenkins.mthreads.com/job/CI_msys_cli_test/${S4000BuildId}/Benchmark_20Report">http://sh-jenkins.mthreads.com/job/CI_msys_cli_test/${S4000BuildId}/Benchmark_20Report</a></li>
                            </div>
                            </ul>
                        </td>
                    </tr>
                </table></font>
            </body>
            </html>""",
        to: to
    )
}

runner.start(env.runChoice) {
    def config = null
    config = commonLib.loadConfig('daily/msys.yaml')

    def workflow = [:]
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name
        def parameters = [
            triggerInfo: env.triggerInfo
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }

        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            async: true
        ]
    }

    runPipeline(workflow)

    println(env.subJobs)
    S4000BuildId = sh(script:"echo \"${env.subJobs}\" | grep -oP 'CI_msys_cli_test#\\K\\d+'", returnStdout: true).trim()
    println(S4000BuildId)
    // S5000BuildId = sh(script:"${env.subJobs} | tr ',' '\\n' | grep -P 'CI_msys_daily_test#\\d+' | tail -n 1 | sed -E 's/.*#([0-9]+).*/\\1/'", returnStdout: true).trim()
    S80BuildId = sh(script:"echo \"${env.subJobs}\" | grep -oP 'CI_msys_cli_test_qy1#\\K\\d+'", returnStdout: true).trim()
    println(S80BuildId)

    if (env.sendEmail == 'true') {
        sendEmail(S80BuildId, S4000BuildId)
    }
}
