@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

def build(config) {
    def buildTasks = [:]
    def builds = config.builds

    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        if (_buildConfig.buildAfterMerged == true) {
            def defaultParameters = _buildConfig.parameters ?: [:]
            def parameters = [
                triggerInfo: env.triggerInfo,
                repo: env.repo,
                branch: env.gitlabSourceBranch,
                commit_id: env.gitlabMergeRequestLastCommit
            ]
            defaultParameters.each { key, value ->
                parameters[key] = value
            }

            buildTasks["${_buildConfig.name}"] = {
                runPipeline.runJob([
                    job: "${_buildConfig.job}",
                    parameters: parameters
                ])
            }
        }
    }

    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt(env.repo, env.gitlabTargetBranch ?: env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit ?: env.commitId)
}

runner.start(env.runChoice) {
    def config = null
    env.repo = env.gitlabSourceRepoName
    gitLib.fetchCode(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])

    dir(env.repo) {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "mt-perf/${env.gitlabTargetBranch}.yaml", [
            branch: env.gitlabSourceBranch
        ], 'mt-perf/develop.yaml')
    }

    def workflow = [
        'build': [closure: { build(config) }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ]

    runPipeline(workflow)
}
