@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def build(config) {
    def builds = config.buildList
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        buildTasks["${_buildConfig.pkgName}"] = {
            runPipeline.runJob([
                job: 'build.linux-ddk',
                parameters: [
                    repo: env.gitlabSourceRepoName,
                    branch: env.gitlabSourceBranch,
                    commitId: env.gitlabMergeRequestLastCommit,
                    triggerInfo: env.triggerInfo,
                    packageName: _buildConfig.pkgName,
                    cmd: _buildConfig.cmd,
                    containerImage: _buildConfig.dockerImage ?: config.dockerImage,
                    exports: _buildConfig.env ? _buildConfig.env.join('\n') : '',
                    buildChoice: _buildConfig.buildChoice ?: '',
                    os: _buildConfig.os ?: 'Ubuntu',
                    arch: _buildConfig.arch ?: 'x86_64',
                    version: _buildConfig.version ?: '',
                    deviceType: _buildConfig.deviceType ?: 'pc',
                    isReleasePkg: _buildConfig.isReleasePkg ?: 'false',
                    packagePath: _buildConfig.packagePath ?: '',
                    packRepoBranch: _buildConfig.packRepoBranch ?: 'ddk2.0_deb',
                    signFirmware: _buildConfig.signFirmware ?: 'false',
                ]
            ])
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt('linux-ddk', env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

runner.start(env.runChoice) {
    def config = ddk.loadBuildConfig('linux-ddk', env.gitlabTargetBranch, [
        'date': new Date().format('yyyy.MM.dd'),
    ])

    runPipeline([
        'buildPkg': [closure: { build(config) }],
        'update latest txt': [closure: { updateLatestTxt() }]
    ])
}
