@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

repo = 'ogl'

def uploadCase() {
    gitLib.fetchCode(repo, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [preBuildMerge: false, disableSubmodules: true])
    dir(repo) {
        if (fileExists('cts')) {
            oss.install()
            oss.cp('cts/linux/*', "sh-moss/sw-build/m3d/ogl/${env.gitlabTargetBranch}/")
        }
    }
}

runner.start(env.runChoice) {
    stage('upload case') {
        uploadCase()
    }
}
