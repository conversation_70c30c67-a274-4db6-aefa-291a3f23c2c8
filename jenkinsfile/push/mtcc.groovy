@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git
import groovy.json.JsonOutput

commonLib = new common()
gitLib = new git()

// https://sh-moss.mthreads.com/sw-build/wddm/develop/${wddmCommitId}
env.latestURLWddm = 'https://sh-moss.mthreads.com/sw-build/wddm/develop/latest_new.txt'
env.wddmBaseUrl = 'https://sh-moss.mthreads.com/sw-build/wddm'

// initialize environment variables
def init() {
    def initParams = [:]
    print('Start post build')
    def compilerCommitid = env.gitlabMergeRequestLastCommit[0..8]
    def compiler_branch = env.gitlabBranch
    initParams['isMr'] = 'true'
    initParams['ossRelPath'] = compilerCommitid
    initParams['buildBranch'] = compiler_branch
    initParams['buildWddmBranch'] = compiler_branch == 'master' ? 'develop' : compiler_branch
    initParams['mtccCommitId'] = compilerCommitid
    initParams['prefix'] = "${initParams.mtccCommitId}_"
    initParams['wddmBaseUrl'] = env.wddmBaseUrl
    if (env.wddmCommitId) {
        initParams['wddmCommitId'] = env.wddmCommitId[0..8]
        initParams['wddmUrl'] = "${env.wddmBaseUrl}/${initParams.buildWddmBranch}/${initParams.wddmCommitId}/${initParams.wddmCommitId}_wddm"
    } else if (initParams.buildWddmBranch == 'develop') {
        new common().runRelyNetwork(3, 10) {
            wddmPkgURL = sh(script: "curl --insecure ${env.latestURLWddm}", returnStdout: true).trim()
        }
        initParams['wddmCommitId'] = wddmPkgURL.split('/')[-1][0..8]
        initParams['wddmUrl'] = "${wddmPkgURL}/${initParams.wddmCommitId}_wddm"
    } else {
        // Non-develop branch, get wddm last commit id from gitlab
        initParams['wddmCommitId'] = ''
        initParams['wddmUrl'] = ''
    }

    return initParams
}

def testAfterBuild(testsConfig, initParams) {
    for (testConfig in testsConfig) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def testname = _testConfig.name ?: _testConfig.job
        def parameters = [
            triggerInfo: env.triggerInfo,
            testLabel: testname
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        runPipeline.runJob([
            wait: false,
            job: _testConfig.job,
            parameters: parameters,
        ])
    }
}

def build(builds, initParams) {
    def buildTasks = [:]
    for (buildConfig in builds) {
        if (buildConfig.job?.endsWith('.win')) { continue }
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            triggerInfo: env.triggerInfo,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        initParams.each { key, value ->
            parameters[key] = value in Map || value in List ? JsonOutput.toJson(value) : value
        }
        buildTasks[name] = {
            runPipeline.runJob([
                job: _buildConfig.job,
                parameters: parameters
            ])
            if (_buildConfig.tests) {
                testAfterBuild(_buildConfig.tests, initParams)
            }
        }
    }
    parallel buildTasks
}

def updateLatestTxt() {
    constants.updateLatestTxt('mtcc', env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit)
}

// use POD instead
runner.start(env.runChoice) {
    def config = null
    def initParams = init()
    // get config from repo mtcc
    // def config = gitLib.getFileContentByApi('mtcc', env.branch, 'buildConfig.yaml', 'yaml')
    gitLib.fetchCode('mtcc', initParams['buildBranch'], initParams['mtccCommitId'], [disableSubmodules: true])

    dir('mtcc') {
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', '', [:], 'mtcc/default.yaml')
    }

    def workflow = [:]

    def builds = config.builds.findAll { buildConfig ->
        buildConfig.buildAfterMerged?.toLowerCase() == 'true' ||
        buildConfig.buildOnlyMerged?.toLowerCase() == 'true'
    }

    workflow['build'] = [ closure: { build(builds, initParams) }, setGitlabStatus: true, statusName: 'jenkins/pkg_builds']

    workflow['update latest'] = [closure: { updateLatestTxt() }]

    runPipeline(workflow)
}
