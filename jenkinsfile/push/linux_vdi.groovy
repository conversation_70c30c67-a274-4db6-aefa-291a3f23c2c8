@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def getLatestCommit(String dependencyUrl) {
    def latestUrl = dependencyUrl.substring(0, dependencyUrl.lastIndexOf('/') + 1) + 'latest.txt'
    def latestContent = utils.runCommandWithStdout("curl --insecure ${latestUrl}").with {
        it.endsWith('_') ? it[0..-2] : it
    }
    return latestContent.split('/')[-1]
}

def updateLatestTxt() {
    def ossDrvPath = "release-ci/VDI/XC-VDI/${env.gitlabTargetBranch}/drivers/"
    def ossDrvUrl = "https://oss.mthreads.com/${ossDrvPath}"
    sh """
        echo ${ossDrvUrl + env.gitlabMergeRequestLastCommit[0..8]}_ > latest.txt
        mc cp latest.txt sh-moss/${ossDrvPath}
    """
}

def updateLatestHost() {
    def targets = [
        'host_gr-kmd_deb', 'host_gr-kmd_rpm',
        'kylin_host_gr-kmd_deb', 'kylin_host_gr-kmd_rpm',
        'uos_host_gr-kmd_deb', 'uos_host_gr-kmd_rpm',
        'zkfd_host_gr-kmd_deb', 'zkfd_host_gr-kmd_rpm'
    ]
    oss.install()
    targets.each { targetName ->
        dir(targetName) {
            def dependencyPath = "sh-moss/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/${targetName}/"
            def dependencyUrl = "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/${targetName}/"
            def latestCommit = getLatestCommit(dependencyUrl)
            def latestTargetPath = utils.runCommandWithStdout("mc find ${dependencyPath} --name ${latestCommit}_mtgpu-*")
            def packagetName = latestTargetPath.split('/')[-1].split('_')[-1]
            def targetOs = targetName.contains('_host_') ? targetName.split('_host_')[0] : ''
            def fullPackageName = targetOs ? "${env.gitlabMergeRequestLastCommit[0..8]}_${targetOs}_${packagetName}" : "${env.gitlabMergeRequestLastCommit[0..8]}_${packagetName}"
            sh """
                mc cp ${latestTargetPath} .
                mv -n *${packagetName} ${fullPackageName}
            """
            artifact.upload(fullPackageName, "sh-moss/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/drivers/")
        }
    }
}

def build(config) {
    def builds = config.buildList
    def buildTasks = [:]
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def name = _buildConfig.name ?: _buildConfig.job
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabSourceRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit[0..8],
            updateLatest: 'true',
            triggerInfo: env.triggerInfo,
            containerImage: defaultParameters.dockerImage ?: config.dockerImage,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks[defaultParameters?.targetName ?: name] = [
            closure : {
                runPipeline.runJob([
                    job: _buildConfig.job,
                    parameters: parameters,
                ])
            },
            async: _buildConfig?.async ?: false
        ]
    }
    runPipeline.runSyncThenAsyncTasks(buildTasks)
}

def pack(config) {
    def packs = config.packList
    def packTasks = [:]
    for (packConfig in packs) {
        Map _packConfig = packConfig
        def name = _packConfig.name ?: _packConfig.job
        def defaultParameters = _packConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabSourceRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit[0..8],
            updateLatest: 'true',
            triggerInfo: env.triggerInfo,
            containerImage: defaultParameters.dockerImage ?: config.dockerImage,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        packTasks[defaultParameters?.packageName ?: name] = {
            runPipeline.runJob([
                job: _packConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel packTasks
}

def packAll(config) {
    def packAlls = config.packAllList
    def packAllTasks = [:]
    for (packConfig in packAlls) {
        Map _packConfig = packConfig
        def name = _packConfig.name ?: _packConfig.job
        def defaultParameters = _packConfig.parameters ?: [:]
        def parameters = [
            repo: env.gitlabSourceRepoName,
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit[0..8],
            updateLatest: 'true',
            triggerInfo: env.triggerInfo,
            containerImage: defaultParameters.dockerImage ?: config.dockerImage,
            testLabel: name
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        packAllTasks[defaultParameters?.packageName ?: name] = {
            runPipeline.runJob([
                job: _packConfig.job,
                parameters: parameters
            ])
        }
    }
    parallel packAllTasks
}

runner.start(env.runChoice) {
    def umdDependency = "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/guest_gr-umd/"
    def kmdDependency = "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/guest_gr-kmd/"
    def mediaDependency = "https://sh-moss.mthreads.com/sw-build/VDI/XC-VDI/${env.gitlabTargetBranch}/repoPackages/guest_mt-media-driver/"
    def latestUmdCommit = env.gitlabSourceRepoName == 'gr-umd' ? env.gitlabMergeRequestLastCommit[0..8] : getLatestCommit(umdDependency)
    def latestKmdCommit = env.gitlabSourceRepoName == 'gr-kmd' ? env.gitlabMergeRequestLastCommit[0..8] : getLatestCommit(kmdDependency)
    def latestMediaCommit = env.gitlabSourceRepoName == 'mt-media-driver' ? env.gitlabMergeRequestLastCommit[0..8] : getLatestCommit(mediaDependency)
    env.configName = 'vdiConfig.yaml'
    def config = ddk.loadBuildConfig(env.gitlabSourceRepoName, env.gitlabTargetBranch, [
        date: env.date ?: new Date().format('yyyy.MM.dd'),
        branch: env.gitlabTargetBranch,
        commit: env.gitlabMergeRequestLastCommit[0..8],
        latestUmdCommit: latestUmdCommit,
        latestKmdCommit: latestKmdCommit,
        latestMediaCommit: latestMediaCommit
    ])
    def workflow = [:]
    workflow['buildPkg'] = [closure: { build(config) }]
    if (config.packList?.size() > 0) { workflow['packPkg'] = [closure: { pack(config) }] }
    if (config.packAllList?.size() > 0) { workflow['packAll'] = [closure: { packAll(config) }] }
    workflow['update latest host'] = [closure: { updateLatestHost() }]
    workflow['update latest txt'] = [closure: { updateLatestTxt() }]

    runPipeline(workflow)
}
