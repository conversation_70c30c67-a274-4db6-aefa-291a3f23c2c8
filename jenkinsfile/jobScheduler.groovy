@Library('swqa-ci')

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

runner.start(env.runChoice) {
    def config = null
    def commitId = constants.formatCommitID(env.gitlabMergeRequestLastCommit)
    def packageVersion = gitLib.getFileContentByApi(env.gitlabSourceRepoName, env.gitlabSourceBranch, '.ciConfig.yaml', 'yaml', 'sw')?.packageVersion ?: ''
    gitLib.fetchCode(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
    if (env.gitlabSourceRepoName == 'mt-vgpu') {
        def infoData = "${env.WORKSPACE}/${commitId}_package_version_info.txt"
        dir(env.gitlabSourceRepoName) {
            Map packageInfo = env.packageInfo ? readJSON(text: env.packageInfo) : [
                'host_version': 'linux-ddk/gr-kmd',
                'fw_version': 'linux-ddk/gpu-fw',
                'win_guest_version': 'wddm',
                'linux_guest_version': 'linux-ddk/gr-kmd'
            ]
            packageInfo.each { key, submodulePath ->
                def subCommit = gitLib.getSubmoduleCommit(submodulePath)
                sh "echo '${key}: ${subCommit}' >> '${infoData}'"
            }
        }
        artifact.upload(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, "${commitId}_package_version_info.txt")
        def defaultCompat = [ ['2.9.0', '2.9.0', '2.9.0'] ]
        def compatJson = JsonOutput.prettyPrint(JsonOutput.toJson(env.compatInfo ? new JsonSlurper().parseText(env.compatInfo) : defaultCompat))
        writeFile(file: "${commitId}_compat.json", text: compatJson)
        artifact.upload(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, "${commitId}_compat.json")
    }

    // Pass this parameter for gmi ci
    def date = new Date().format('yyyy.MM.dd')

    // Pass this parameter for mt-rm ci
    def ddkCommitId = ''
    try {
        ddkCommitId = constants.getLatestPackageCommitId('linux-ddk', 'master', true)
    } catch (e) {
        log.error(e.message)
    }

    if (['mt-management', 'mthreads-smi'].contains(env.gitlabSourceRepoName)) {
        if (env.gitlabTargetBranch == 'develop') {
            packageVersion = date
        } else if (env.gitlabActionType?.toLowerCase() == 'push') {
            dir(env.gitlabSourceRepoName) {
                packageVersion = utils.runCommandWithStdout('cat config.ini | grep "version =" | cut -d "=" -f2')
            }
        } else {
            packageVersion = date
        }
    }

    // this map will be used to bind yaml config for pipeline
    Map yamlBinding = [
        shMossPrefix: constants.OSS.MOSS_URL_PREFIX,
        ossBucket: constants.genOssBucket(),
        ddkCommitId: ddkCommitId,
        gitlabSourceRepoName: env.gitlabSourceRepoName,
        gitlabSourceBranch: env.gitlabSourceBranch,
        gitlabMergeRequestIid: env.gitlabMergeRequestIid,
        branch: env.gitlabSourceBranch,
        commitId: commitId,
        packageVersion: packageVersion
    ]
    // this map will be passed to builds
    Map buildParameters = [
        date: date,
        mtmlVersion: packageVersion
    ]
    if (env.gitlabSourceRepoName in ['mt-rm']) {
        buildParameters.ddkCommitId = ddkCommitId
    }
    // this map will be passed to tests
    Map testParameters = [date: date]
    if (constants.musaChangeBranches.contains(env.gitlabTargetBranch)) {
        testParameters.musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', env.gitlabTargetBranch, 'musaRuntime.tar.gz')
        buildParameters.musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', env.gitlabTargetBranch, 'musaRuntime.tar.gz')
    }
    dir(env.gitlabSourceRepoName) {
        // no gitlabTargetRepoName in namespace when pipeline is triggered by push events
        config = commonLib.loadPipelineConfig('.ciConfig.yaml', "${env.gitlabSourceRepoName}/${env.gitlabTargetBranch}.yaml", yamlBinding, "${env.gitlabSourceRepoName}/default.yaml")
        config = commonLib.removeWinJobs(config)
    }
    if (env.gitlabSourceRepoName == 'MUPTI') {
        def ddkCommitIdMap = constants.getCommitIdfromciConfig(config)
        testParameters.ddkCommitIdMap = ddkCommitIdMap
        buildParameters.ddkCommitIdMap = ddkCommitIdMap
    }

    // def workflow = runPipeline.genWorkflowByConfig(config, buildParameters, testParameters)
    // runPipeline(workflow)

    runPipeline.runYamlWorkflow(config, buildParameters, testParameters)
}
