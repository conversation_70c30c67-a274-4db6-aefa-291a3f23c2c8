#!/bin/bash

# OSS File Synchronization Script
# Usage: ./oss_sync.sh <src> <target> [options]
# Options:
#   --max-age <days>    Only sync files modified within specified days (default: 365 days)
#   --skip-existing     Skip files that already exist in target
#   --dry-run          Only show operations to be executed, don't actually execute
#   --help             Show help information

set -e

# Default configuration
DEFAULT_MAX_AGE_DAYS=365
SKIP_EXISTING=false
DRY_RUN=false
MAX_AGE_DAYS=$DEFAULT_MAX_AGE_DAYS

# OSS configuration
declare -A OSS_CONFIGS=(
    # Format: [alias_name]="endpoint|username|password"
    ["oss"]="https://oss.mthreads.com|mtoss-swci|Z2Z4LWNp"
    ["swci-oss"]="https://swci-oss.mthreads.com|mtoss-swci|H65KwcY1poayvgqTEReHBJu"
    ["sh-moss"]="https://sh-moss.mthreads.com|${SHMOSS_USERNAME}|${SHMOSS_PASSWORD}"
    # Add more OSS configurations...
)

# Color output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show help information
show_help() {
    cat << EOF
OSS File Synchronization Script - Efficient file sync tool supporting multiple OSS providers

Usage: $0 <src> <target> [options]

Arguments:
  src                 Source path (supports local path or OSS path, e.g: bucket/path/)
  target              Target path (supports local path or OSS path, e.g: bucket/path/)

Options:
  --max-age <days>    Only sync files modified within specified days (default: ${DEFAULT_MAX_AGE_DAYS} days)
  --skip-existing     Skip files that already exist in target (note: limited mc support)
  --dry-run          Only show operations to be executed, don't actually execute
  --help             Show this help information

Configure OSS Information:
  Modify the OSS_CONFIGS array in the script with the following format:
  ["alias_name"]="endpoint|username|password"

  Example configuration:
  ["oss-cn-beijing"]="https://oss-cn-beijing.aliyuncs.com|LTAI5tXXXXXXXXXXXXXX|XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
  ["oss-cn-shanghai"]="https://oss-cn-shanghai.aliyuncs.com|LTAI5tYYYYYYYYYYYYYY|YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY"

Basic Examples:
  # Sync local files to OSS
  $0 /local/data/ oss-cn-beijing/my-bucket/data/

  # Sync from OSS to local, only files modified within 30 days
  $0 oss-cn-shanghai/my-bucket/backup/ /local/backup/ --max-age 30

  # Skip existing files (speeds up sync)
  $0 /local/data/ oss-cn-beijing/my-bucket/data/ --skip-existing

  # Preview mode, don't actually execute
  $0 /local/data/ oss-cn-beijing/my-bucket/data/ --dry-run

Advanced Usage:
  # Only sync files from last 7 days, skip existing files
  $0 /local/data/ oss-cn-beijing/my-bucket/data/ --max-age 7 --skip-existing

  # Sync between OSS providers
  $0 oss-cn-beijing/bucket1/data/ oss-cn-shanghai/bucket2/data/

  # Scheduled sync (add to crontab)
  0 2 * * * $0 /local/data/ oss-cn-beijing/my-bucket/data/ --max-age 1 --skip-existing

Performance Optimization Tips:
  - Set appropriate --max-age: daily incremental sync use 1 day, weekly use 7 days, monthly use 30 days
  - Choose OSS region closest to source/target geographical location
  - For better skip existing file support, consider using 'mc mirror' command directly

Important Notes:
  - Requires mc (MinIO Client) to be installed and available in PATH
  - --skip-existing behavior depends on mc version (may use --continue or --overwrite=false)
  - Check supported options: mc --help | grep -E "(continue|overwrite)"
  - Recommend using --dry-run to preview operations before first use
  - Ensure OSS account has appropriate read/write permissions
  - Backup important data before large-scale sync
EOF
}

# Setup OSS aliases
setup_oss_aliases() {
    log_info "Setting up OSS alias configurations..."

    for alias_name in "${!OSS_CONFIGS[@]}"; do
        IFS='|' read -r endpoint username password <<< "${OSS_CONFIGS[$alias_name]}"

        if [[ "$endpoint" == "your_username_"* ]] || [[ "$username" == "your_username_"* ]]; then
            log_warning "Skipping unconfigured alias: $alias_name"
            continue
        fi

        log_info "Setting alias: $alias_name -> $endpoint"
        if [[ "$DRY_RUN" == "false" ]]; then
            if mc alias set "$alias_name" "$endpoint" "$username" "$password" > /dev/null 2>&1; then
                log_success "Alias $alias_name set successfully"
            else
                log_error "Failed to set alias $alias_name"
            fi
        else
            echo "  [DRY-RUN] mc alias set $alias_name $endpoint ****** ******"
        fi
    done
}



# Build mc cp command options
build_mc_options() {
    local options=""

    # Recursive copy
    options="$options --recursive"

    # Filter by time (note: --newer-than format varies by mc version)
    if [[ $MAX_AGE_DAYS -gt 0 ]] && [[ $MAX_AGE_DAYS -lt 365 ]]; then
        # Try different time formats that mc might support
        local cutoff_date
        cutoff_date=$(date -d "$MAX_AGE_DAYS days ago" +%Y-%m-%dT%H:%M:%S)
        # Some mc versions support duration format like "7d", "30d"
        if mc cp --help 2>/dev/null | grep -q "newer-than"; then
            options="$options --newer-than ${MAX_AGE_DAYS}d"
        else
            log_warning "Time filtering not supported by this mc version"
        fi
    fi

    # Skip existing files (note: limited support in mc cp)
    # Most mc versions don't have reliable skip existing options for cp command

    echo "$options"
}

# Execute file synchronization
sync_files() {
    local src="$1"
    local target="$2"

    log_info "Starting file synchronization..."
    log_info "Source path: $src"
    log_info "Target path: $target"

    # Show sync options
    if [[ $MAX_AGE_DAYS -gt 0 ]]; then
        local cutoff_date
        cutoff_date=$(date -d "$MAX_AGE_DAYS days ago" +%Y-%m-%d)
        log_info "Only sync files modified after $cutoff_date"
    fi

    if [[ "$SKIP_EXISTING" == "true" ]]; then
        log_info "Skip existing files mode enabled"
        log_warning "Skip existing files requested but may not be supported by all mc versions"
        log_info "Consider using mc mirror command for better skip existing file support"
    fi

    local mc_options
    mc_options=$(build_mc_options)
    local cmd="mc cp $mc_options \"$src\" \"$target\""

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Preview mode - command to be executed:"
        echo "  mc cp $mc_options \"$src\" \"$target\""
        return 0
    fi

    log_info "Executing command: $cmd"
    if eval "$cmd"; then
        log_success "File synchronization completed"
    else
        log_error "File synchronization failed"
        return 1
    fi
}

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --max-age)
                MAX_AGE_DAYS="$2"
                if ! [[ "$MAX_AGE_DAYS" =~ ^[0-9]+$ ]]; then
                    log_error "Invalid number of days: $MAX_AGE_DAYS"
                    exit 1
                fi
                shift 2
                ;;
            --skip-existing)
                SKIP_EXISTING=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            -*)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "$SRC" ]]; then
                    SRC="$1"
                elif [[ -z "$TARGET" ]]; then
                    TARGET="$1"
                else
                    log_error "Too many arguments: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# Main function
main() {
    log_info "OSS file synchronization script started"

    # Parse arguments
    parse_arguments "$@"

    # Check required parameters
    if [[ -z "$SRC" ]] || [[ -z "$TARGET" ]]; then
        log_error "Missing required parameters"
        show_help
        exit 1
    fi

    # Setup OSS aliases
    setup_oss_aliases

    # Execute synchronization
    sync_files "$SRC" "$TARGET"

    log_success "Script execution completed"
}

# Execute main function
main "$@"
