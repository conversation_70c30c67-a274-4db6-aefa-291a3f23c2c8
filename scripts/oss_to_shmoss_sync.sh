#!/bin/bash

# OSS to SH-MOSS Synchronization Script
# Simple wrapper to call oss_sync.sh for specific sync rules

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
OSS_SYNC="$SCRIPT_DIR/oss_sync.sh"
DRY_RUN_FLAG=""

# Parse arguments
if [[ "$1" == "--dry-run" ]]; then
    DRY_RUN_FLAG="--dry-run"
elif [[ "$1" == "--help" ]]; then
    echo "OSS to SH-MOSS Sync - executes 4 sync rules:"
    echo "  oss/sw-pr/ -> sh-moss/sw-pr/ (90 days)"
    echo "  oss/sw-build/ -> sh-moss/sw-build/ (365 days)"
    echo "  oss/dependency/ -> sh-moss/dependency/ (all)"
    echo "  oss/debug-symbols/ -> sh-moss/dependency/debug-symbols/ (all)"
    echo ""
    echo "Usage: $0 [--dry-run|--help]"
    exit 0
fi

# Execute sync commands
"$OSS_SYNC" oss/sw-pr/ sh-moss/sw-pr/ --max-age 90 $DRY_RUN_FLAG
"$OSS_SYNC" oss/sw-build/ sh-moss/sw-build/ --max-age 365 $DRY_RUN_FLAG
"$OSS_SYNC" oss/dependency/ sh-moss/dependency/ $DRY_RUN_FLAG
"$OSS_SYNC" oss/debug-symbols/ sh-moss/dependency/debug-symbols/ $DRY_RUN_FLAG
